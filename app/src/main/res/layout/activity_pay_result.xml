<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <TextView
        android:id="@+id/tv_tips"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colors_fff7ef"
        android:gravity="center_vertical"
        android:minHeight="50dp"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:text=""
        android:textColor="@color/colors_99664D"
        android:textSize="14sp"
        android:visibility="gone"
        tools:visibility="visible" />



    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/smartrefresh"
        android:layout_width="match_parent"
        android:background="@color/color_f7f7f8"
        android:layout_height="match_parent">

        <!-- 推荐商品流 -->
<!--            <androidx.recyclerview.widget.RecyclerView-->
<!--                android:id="@+id/rv_result"-->
<!--                android:layout_marginStart="@dimen/dimen_dp_5"-->
<!--                android:layout_marginEnd="@dimen/dimen_dp_5"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="match_parent" />-->


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_result"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false"
            android:overScrollMode="never"
            android:scrollbars="none"
            android:focusable="false"
            tools:itemCount="2"
            tools:listitem="@layout/item_show_bottom_car_coupon_dialog_list" />

        <!-- 推荐商品流 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_result_list"
            android:layout_marginStart="@dimen/dimen_dp_5"
            android:layout_marginEnd="@dimen/dimen_dp_5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false"
            android:overScrollMode="never"
            android:scrollbars="none"
            android:focusable="false"/>

        <com.scwang.smart.refresh.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>


</LinearLayout>