<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          xmlns:tools="http://schemas.android.com/tools"
          package="com.ybmmarket20"
          tools:remove="android:appComponentFactory"
          tools:targetApi="r">

    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/> <!-- 允许程序改变网络连接状态 -->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.REORDER_TASKS"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/> <!-- 银联增加权限 -->
    <uses-permission android:name="org.simalliance.openmobileapi.SMARTCARD"/>
    <uses-permission android:name="android.permission.NFC"/>
    <uses-feature
            android:name="android.hardware.nfc.hce"/> <!-- <uses-permission android:name="android.permission.GET_TASKS" /> -->
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS"/>
    <uses-permission android:name="org.simalliance.openmobileapi.SMARTCARD"/> <!-- zxing -->
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.FLASHLIGHT"/>
    <uses-permission android:name="android.permission.VIBRATE"/> <!-- 相机 -->
    <uses-feature android:name="android.hardware.camera"/>
    <uses-feature android:name="android.hardware.camera.autofocus"/> <!-- 应用内更新在8.0以上需要 -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/> <!-- 极光 -->
    <uses-permission android:name="${applicationId}.permission.JPUSH_MESSAGE"/>
    <uses-permission android:name="android.permission.CAPTURE_VIDEO_OUTPUT"
        tools:ignore="ProtectedPermissions" />


    <permission
            android:name="${applicationId}.permission.JPUSH_MESSAGE"
            android:protectionLevel="signature"/>

    <uses-permission
            android:name="android.permission.CHANGE_WIFI_STATE"
            tools:node="remove"/>
    <uses-permission
            android:name="android.permission.SYSTEM_ALERT_WINDOW"
            tools:node="remove"/>
    <permission android:name="com.ybmmarket20.permission.READ_DID"
                android:label="read_content_provider"
                android:protectionLevel="signature"
                />
    <queries>
        <package android:name="com.ybm100.app.crm" />
        <package android:name="com.ybm100.app.crm.debug" />
    </queries>
    <queries>
        <package android:name="com.tencent.mm" />   <!--指定微信包名-->
    </queries>
    <application
            android:name=".common.YBMAppLike"
            android:allowBackup="false"
            android:icon="${app_logo}"
            android:label="${app_name}"
            android:largeHeap="true"
            android:supportsRtl="true"
            android:theme="@style/AppTheme"
            android:resizeableActivity="true"
            android:maxAspectRatio="2.4"
            android:allowNativeHeapPointerTagging="false"
            tools:replace="android:label,android:name,android:theme,android:allowBackup">
        <provider
                android:name=".content.DidContentProvider"
                android:authorities="${applicationId}.didcprovider"
                android:readPermission="com.ybmmarket20.permission.READ_DID"
                android:enabled="true"
                android:exported="true">
        </provider>

        <activity android:name="com.ybmmarketkotlin.activity.PushRecommendProductActiviy"/>
        <activity android:name=".activity.ChangeMobilePhoneActivity"/>
        <activity
                android:name=".activity.SplashActivity"
                android:launchMode="singleTask"
                android:theme="@style/AppTheme2">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>

                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="atm.mn3vwmgc6uvxm0wnmz34bz19" />
            </intent-filter>
        </activity>
        <!--
<activity
            android:name="io.flutter.embedding.android.FlutterActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize" />
        <activity android:name="com.flutter.CustomFlutterActivity" />
        -->
        <activity
                android:name=".home.MainActivity"
                android:launchMode="singleTask"
                android:windowSoftInputMode="adjustNothing"/>
        <activity
                android:name=".activity.AlterPasswordActivity"
                android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity
                android:name=".activity.LoginActivity"
                android:launchMode="singleTask"/>
        <activity
                android:name="com.ybmmarketkotlin.activity.LoginVertificationActivity"
                android:launchMode="singleTask"/>
        <activity android:name="com.ybmmarketkotlin.activity.BindAccountActivity"
                android:launchMode="singleTask"/>
        <activity
                android:name=".activity.AddressEditActivity"
                android:windowSoftInputMode="stateHidden|adjustResize"/>
        <activity android:name=".activity.DebugActivity"/>
        <activity android:name=".activity.DebugAPIActivity"/>
        <activity android:name=".activity.RefundListActivity"/>
        <activity android:name=".activity.ControlMarketActivity"/>
        <activity android:name=".activity.ProductCategoryActivity" />
        <activity android:name=".activity.ControlMallActivity" />
        <activity android:name=".activity.MsgSettingActivity" />
        <activity
            android:name=".activity.CommonH5Activity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:windowSoftInputMode="adjustResize" />
        <activity android:name=".search.SearchProductActivity" />
        <activity android:name=".search.SearchProductSectionActivity" />
        <activity android:name=".search.SearchProductOPActivity" />
        <activity android:name=".search.SearchProductOrderBundlingActivity" />
        <activity android:name="com.ybmmarketkotlin.feature.collect.CollectActivity" />
        <activity
            android:name=".activity.ProductDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.GiftSelectGoodsActivity"
            android:windowSoftInputMode="adjustPan"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.RegisterV2Activity"
            android:launchMode="singleTask" />
        <activity
            android:name=".activity.LinkShopActivity"
            android:launchMode="singleTask"
            android:windowSoftInputMode="stateHidden|adjustResize" />
        <activity android:name=".activity.RefundDetailActivity" />
        <activity android:name=".activity.PayResultActivity" />
        <activity android:name=".activity.AuthorizationActivity" />
        <activity android:name=".activity.ClinicActivity"/>
        <activity android:name=".activity.YbmPhotoViewActivity"/>
        <activity
                android:name=".activity.PaymentActivity"
                android:launchMode="singleTask"
                android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity
                android:name=".activity.PaywayForDrugSchollActivity"
                android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity
                android:name=".activity.ApplyRefundActivity"
                android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity
                android:name=".activity.ChoiceProductActivity"
                android:windowSoftInputMode="adjustNothing"/>
        <activity
                android:name=".activity.ChoiceProductNewActivity"
                android:windowSoftInputMode="adjustNothing"/>
        <activity
                android:name=".activity.OrderProductListActivity"
                android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity
                android:name=".activity.RefundDetailProductListActivity"
                android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity
                android:name=".activity.PaywayActivity"
                android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity android:name=".activity.CommonProcureActivity"/>
        <activity android:name=".activity.OrderDetailActivity"/>
        <activity android:name=".activity.AptitudeActivity"/>
        <activity android:name=".activity.DataUpdateActivity"/> <!-- 切换图标使用 -->
        <activity
                android:name=".activity.SearchOrderActivity"
                android:windowSoftInputMode="adjustPan|stateAlwaysVisible"/>
        <activity android:name=".activity.AboutActivity"/>
        <activity android:name=".activity.SetPasswordActivity"/>
        <activity
                android:name=".activity.CouponMemberActivity"
                android:hardwareAccelerated="false"/>
        <activity android:name=".activity.MemberSignActivity"/>
        <activity android:name=".activity.BindWXActivity"/>
        <activity android:name=".activity.WishSucceedActivity"/>
        <activity android:name=".activity.VoucherAvailableActivity"/>
        <activity android:name=".activity.ForgetpwdActivity"/>
        <activity android:name=".activity.NewPasswordActivity"/>
        <activity android:name=".activity.MsgCenterActivity"/>
        <activity android:name=".activity.MsgDetailActivity"/>
        <activity
                android:name=".activity.SearchVoiceActivity"
                android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity android:name=".activity.ElsePageActivity"/>
        <activity android:name=".activity.AccountRelaActivity"/>
        <activity android:name=".activity.MoreToolActivity"/>
        <activity android:name=".activity.GuidePageActivity"/>
        <activity android:name=".activity.AddWishActivity"/>
        <activity android:name=".activity.WishActivity"/>
        <activity android:name=".activity.MySupplierActivity"/>
        <activity android:name=".activity.LookSupplierQualificationActivity"/>
        <activity android:name=".activity.OrderProductPriceActivity"/>
        <activity android:name=".activity.ElectronicPlanListActivity"/>
        <activity android:name=".activity.ElectronicPlanDetailActivity"/>
        <activity android:name=".activity.POPActivity"/>
        <activity android:name=".business.order.ui.OrderListActivity"/> <!-- 屏幕整体上移 -->
        <activity android:name=".activity.BigPicActivity"/>
        <activity android:name=".business.comment.ui.CommentDetailActivity"/>
        <activity
                android:name=".activity.ReplenishmentProgramActivity"
                android:windowSoftInputMode="adjustPan|stateAlwaysVisible"/>
        <activity
                android:name=".activity.PlayerActivity"
                android:configChanges="keyboard|orientation|screenSize"
                android:launchMode="singleTask"
                android:screenOrientation="portrait"
                tools:ignore="LockedOrientationActivity"/> <!-- zxing -->
        <activity
                android:name="com.zxing.activity.CaptureActivity"
                android:windowSoftInputMode="adjustPan"/>
        <activity
                android:name=".activity.DispatchActivity"
                android:excludeFromRecents="true"
                android:screenOrientation="portrait"
                android:theme="@android:style/Theme.NoDisplay"
                tools:ignore="LockedOrientationActivity">
            <intent-filter>
                <action android:name="com.ybmmarket20"/>

                <data android:scheme="ybmapp"/>
                <!-- 改成自己的scheme -->
            </intent-filter>
        </activity>
        <activity
                android:name=".activity.VoucherDispatchActivity"
                android:theme="@android:style/Theme.NoDisplay"/>
        <activity
                android:name=".common.YBMRouterActivity"
                android:allowTaskReparenting="true"
                android:launchMode="singleTask"
                android:parentActivityName=".home.MainActivity"
                android:theme="@android:style/Theme.NoDisplay">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>

                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>

                <data android:scheme="ybmpage"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>

                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>

                <data android:scheme="yaobangmang"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>

                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>

                <data
                        android:host="ybm100.com"
                        android:scheme="xyy"/>
                <!-- 改成自己的scheme -->
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>

                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>

                <data
                        android:host="ybmmarket20.com"
                        android:scheme="ybm100"/>
                <!-- 改成自己的scheme -->
            </intent-filter>
        </activity> <!-- 微信支付 -->
        <activity
                android:name=".wxapi.WXPayEntryActivity"
                android:exported="true"
                android:launchMode="singleTop"
                android:theme="@android:style/Theme.Translucent.NoTitleBar"/> <!-- 微信分享 -->
        <activity
                android:name=".wxapi.WXEntryActivity"
                android:configChanges="keyboardHidden|orientation|screenSize"
                android:exported="true"
                android:launchMode="singleTask"
                android:theme="@android:style/Theme.Translucent.NoTitleBar"/> <!-- qq分享 -->
        <activity
                android:name="com.tencent.tauth.AuthActivity"
                android:launchMode="singleTask"
                android:noHistory="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>

                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>

                <data android:scheme="tencent1105981433"/>
            </intent-filter>
        </activity>
        <activity
                android:name="com.tencent.connect.common.AssistActivity"
                android:configChanges="orientation|keyboardHidden|screenSize"
                android:screenOrientation="behind"
                android:theme="@android:style/Theme.Translucent.NoTitleBar"/> <!-- 微信支付结束 -->
        <!-- 银联支付开始 -->
        <uses-library
                android:name="org.simalliance.openmobileapi"
                android:required="false"/>

        <activity
                android:name="com.unionpay.uppay.PayActivity"
                android:configChanges="orientation|keyboardHidden|keyboard"
                android:screenOrientation="portrait"/>
        <activity
                android:name="com.unionpay.UPPayWapActivity"
                android:configChanges="orientation|keyboardHidden|fontScale"
                android:screenOrientation="portrait"
                android:windowSoftInputMode="adjustResize"/> <!-- 银联支付结束 -->
        <meta-data
                android:name="com.ybm.app.common.ImageLoader.OkHttpGlideModule"
                android:value="GlideModule"/>

        <activity android:name=".activity.BalanceActivity"/>
        <activity android:name=".activity.BalanceOtherTypeActivity"/>
        <activity
                android:name=".activity.ModifyPlanAmountActivity"
                android:windowSoftInputMode="adjustPan|stateAlwaysVisible"/>
        <activity
                android:name=".activity.ReplenishProductActivity"
                android:windowSoftInputMode="adjustPan|stateAlwaysVisible"/>
        <activity android:name=".activity.SpecManufacturerActivity"/>
        <activity android:name=".activity.ReplenishProductSearchActivity"/>
        <activity
                android:name=".activity.AddAccountActivity"
                android:windowSoftInputMode="adjustPan|stateAlwaysVisible"/>
        <activity android:name=".activity.AddressListActivity"/>
        <activity android:name="com.ybm.activity.ad.AdActivity"/>
        <activity android:name=".activity.OverlayTicketActivity"/>
        <activity android:name=".activity.InvoiceInformationActivity"/>
        <activity android:name=".activity.InvoiceListActivity"/>
        <activity android:name=".activity.TbsPdfDisplayActivity"/> <!-- 评价 -->
        <activity
                android:name=".business.comment.ui.CommentActivity"
                android:windowSoftInputMode="adjustPan"/>
        <activity
                android:name=".activity.DepreciateInformActivity"
                android:windowSoftInputMode="adjustPan|stateAlwaysVisible"/> <!-- 说明书 -->
        <activity android:name=".activity.SpecificationActivity"/> <!-- 图片说明书详情 -->
        <activity android:name=".activity.ImageCartDetailActivity"/> <!-- 上传采购单图片 -->
        <activity android:name=".activity.UploadImageCartDetailActivity"/> <!-- 商家列表 -->
        <activity android:name=".activity.PopMerchantsActivity"/> <!-- 物流 -->
        <activity android:name=".activity.LogisticsActivity"/> <!-- 常见问题 -->
        <activity android:name=".activity.PersonalHelpActivity"/> <!-- 搜索高毛专区 -->
        <!-- 我的礼品卡 -->
        <activity android:name=".activity.MineVipGiftActivity"/> <!-- 店铺搜索结果 -->
        <activity
                android:name=".business.shop.ui.SearchResultActivity"
                android:launchMode="singleTask"
                android:windowSoftInputMode="adjustPan|stateHidden"/> <!-- 店铺首页 -->
        <activity android:name=".business.shop.ui.ShopAllActvity"/>
        <activity android:name=".activity.mailcertificate.MailCertificateActivity"/> <!-- 个人中心-保驾护航 -->
        <activity android:name=".activity.ProtectPriceRecordActivity"/>
        <activity android:name=".activity.selectApplyForGoodsActivity"/>
        <activity android:name=".activity.ApplyForConvoyActivity"/>
        <activity android:name=".activity.SelectApplyForOrderActivity"/>
        <activity android:name=".activity.ApplyForRecordDetailsActivity"/>
        <activity android:name=".activity.SelectApplyForOrderSearchActivity"/>
        <activity android:name=".activity.EditGatheringActivity"/>
        <activity android:name=".activity.FillReturnLogisticsActivity"/>
        <activity android:name=".activity.SetNotificationActivity"/>
        <activity android:name=".activity.SmsInvitationActivity"/>
        <activity android:name=".activity.BonusPoolsActivity"/>
        <activity android:name=".activity.ConsultantListActivity"/>
        <activity android:name=".activity.TheInvitationActivity"/>
        <activity android:name=".activity.AptitudeDetailActivity"/> <!-- 商品纠错 -->
        <activity android:name=".business.correction.ui.activity.MainCorrectionActivity"/> <!-- 价格纠错 -->
        <activity
                android:name=".business.correction.ui.activity.PriceCorrectionActivity"
                android:windowSoftInputMode="stateHidden"/> <!-- 商品纠错 -->
        <activity
                android:name=".business.correction.ui.activity.GoodsCorrectionActivity"
                android:windowSoftInputMode="stateHidden"/> <!-- 其他纠错 -->
        <activity
                android:name=".business.correction.ui.activity.OthersCorrectionActivity"
                android:windowSoftInputMode="stateHidden"/> <!-- 交易快照列表页 -->
        <activity android:name=".business.snapshot.ui.TradingSnapshotListActivity"/> <!-- 交易快照详情页 -->
        <activity android:name=".business.snapshot.ui.TradingSnapshotDetailActivity"/> <!-- 代下单专区 -->
        <activity android:name=".activity.AgentOrderActivity"/> <!-- 授权专区 -->
        <activity android:name=".activity.AuthorizationAreaActivity"/> <!-- 代下单授权详情 -->
        <activity android:name=".activity.AuthorizationDetailActivity"/> <!-- 代下单搜索 -->
        <activity android:name=".activity.AgentOrderSearchActivity"/> <!-- 代下单订单详情 -->
        <activity android:name=".activity.AgentOrderListDetailActivity"/> <!-- 优惠券商品使用范围（新） -->
        <activity android:name=".activity.CouponToUseActivity"/> <!-- 优惠券商品使用范围（旧） -->
        <activity android:name=".activity.CouponToUseV2Activity"/>
        <activity android:name=".activity.CouponAvailableProductActivity"/> <!-- 小药白条 -->
        <activity android:name=".activity.MedicineIousActivity"/> <!-- 小药白条-交易明细 -->
        <activity android:name=".activity.TransactionDetailsActivity"/> <!-- 小药白条-利息补贴 -->
        <activity android:name=".activity.InterestSubsidyActivity"/> <!-- 小药白条-我的金融心愿单 -->
        <activity android:name=".activity.MyBankingForWishListActivity"/> <!-- 小药白条-我的金融 -->
        <activity android:name=".activity.MyBankingActivity"/> <!-- 小药药资质 -->
        <!-- 小药药资质-审批日志 -->
        <activity android:name=".activity.AptitudeLogActivity"/> <!-- 小药药资质-添加资质 -->
        <activity
                android:name=".activity.AddAptitudeActivity"
                android:windowSoftInputMode="adjustPan"/> <!-- 小药药资质-填下基本信息 -->
        <activity
                android:name=".activity.AddAptitudeBasicInfoActivity"
                android:windowSoftInputMode="adjustPan"/> <!-- 小药药资质-小药药资质列表 -->
        <activity android:name=".activity.AptitudeXyyActivity"/> <!-- 小药药资质-发送邮箱 -->
        <activity android:name=".activity.AptitudeXyyEmailActivity"/> <!-- 小药药资质-查看资质pdf -->
        <activity android:name=".activity.AptitudeXyyPdfActivity"/> <!-- 注册添加店铺 -->
        <activity android:name=".activity.RegisterAddShopActivity"/> <!-- 专题页面 -->
        <activity android:name=".activity.SubjectActivity"/> <!-- 频道页 -->
        <activity android:name=".activity.ChannelActivity"/> <!-- 小直播 -->
        <activity
                android:name=".activity.TVLiveActivity"
                android:theme="@android:style/Theme.Translucent.NoTitleBar"/>
        <activity android:name="com.videolive.ECAudienceActivity"/>
        <activity android:name="com.videolive.ECPlaybackActivity"/>
        <activity android:name=".activity.CrmRecommendActivity"/>
        <activity android:name=".activity.CrmRecommendHistoryActivity"/>
        <activity android:name=".activity.AccountBasicInfoActivity"
            android:launchMode="singleTask" />
        <activity android:name="com.ybmmarketkotlin.activity.FreightOverWeightGoodsActivity"/>
        <activity android:name="com.ybmmarketkotlin.activity.FreightAddOnItemActivity"/>
        <activity android:name="com.ybmmarketkotlin.activity.AddOnItemShopActivity"/>
        <activity android:name="com.ybmmarketkotlin.activity.PayForAnotherActivity"/>
        <activity android:name="com.ybmmarketkotlin.activity.OftenBuyActivity"/>
        <activity
                android:name=".more_account.ui.AccountManageActivity"
                android:configChanges="orientation|keyboardHidden|keyboard"
                android:launchMode="singleTop"
                android:screenOrientation="portrait"/>
        <activity android:name=".activity.InvoiceListPopActivity"/>
        <activity
                android:name=".activity.VideoPlayerActivity"
                android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
                android:screenOrientation="portrait"/>
        <activity android:name="com.ybmmarketkotlin.activity.UnregisterAccountActivity"/>
        <activity android:name=".activity.SelectServiceTypeActivity"/>
        <activity android:name=".activity.FindSameGoodsActivity"/> <!-- 7.0 文件访问零时授权 -->
        <activity android:name=".activity.SameGoodsForShopActivity" />
        <activity android:name=".activity.MyRedEnvelopeActivity" />
        <activity android:name=".activity.RecommendManagerActivity" />
        <activity android:name=".activity.BrowsePDFActivity" />
        <activity android:name=".activity.OrderDetailProductListActivity" />
        <activity android:name=".activity.QualificationAndAfterSaleActivity" />
        <activity android:name=".activity.PurchaseReconciliationActivity" />
        <activity android:name=".activity.MyVirtualMoneyActivity"
            android:launchMode="singleTask"/>
        <activity android:name=".activity.SendInvoiceByEmailActivity" />
        <activity android:name=".activity.SpellGroupRecommendSelectedGoodsActivity" />
        <activity android:name=".activity.SearchSpellGroupRecommendGoodsActivity" />
        <activity android:name=".activity.ClerkAptitudeAuthenticationActivity" />
        <activity android:name=".activity.AssociatedShopAuthenticationProcessingActivity" />
        <activity android:name=".activity.SelectLoginShopActivity" />
        <activity android:name=".activity.ShopInfoAuthenticationProcessingActivity" />
        <activity android:name=".activity.ClerkInfoActivity" />
        <activity android:name=".activity.RejectRefundActivity"/>
        <activity android:name=".activity.RealNameAuthenticationActivity"
            android:screenOrientation="portrait"
            />
        <activity android:name=".activity.ShoppingGoldRechargeResultActivity"
            android:screenOrientation="portrait"
            />
        <activity android:name=".activity.jdpay.MyWealthActivity" />
        <activity android:name=".activity.jdpay.BankCardActivity"
            android:launchMode="singleTask"/>
        <activity android:name=".activity.jdpay.PaySettingActivity" />
        <activity android:name=".activity.jdpay.SetPayPwActivity" />
        <activity android:name=".activity.jdpay.AddBankCardActivity"
            android:launchMode="singleTask"/>
        <activity android:name=".activity.jdpay.AddBankCardDetailActivity" />
        <activity android:name=".activity.jdpay.AddBankCardDetailWithNumActivity" />
        <activity android:name=".activity.jdpay.CheckReservePhoneNumActivity" />
        <activity android:name=".activity.RemindProgressActivity" />
        <activity android:name=".activity.jdpay.PayWayV2Activity"
            android:launchMode="singleTask"/>
        <activity android:name=".activity.jdpay.AddBankCardFailResultActivity" />
        <activity android:name=".activity.jdpay.MyBankCardActivity" />
        <activity android:name=".activity.jdpay.EnableFingerprintCheckActivity" />
        <activity android:name=".activity.afterSales.activity.LicenseRequirementWithOrderEditActivity"
            android:windowSoftInputMode="adjustPan" />
        <activity android:name=".activity.afterSales.activity.LicenseRequirementWithOrderStaticActivity"
            android:windowSoftInputMode="adjustPan" />
        <activity android:name=".activity.afterSales.activity.CompanyAfterSalesTipsActivity" />
        <activity android:name=".activity.afterSales.activity.InvoiceAfterSalesServiceActivity"
            android:windowSoftInputMode="adjustPan" />
        <activity android:name=".activity.afterSales.activity.LicenseAfterSalesServiceActivity"
            android:windowSoftInputMode="adjustPan" />
        <activity android:name=".home.newpage.AllDrugActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity android:name=".activity.afterSales.activity.RefundOrAfterSalesActivity"
            android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity android:name=".home.newpage.FrequentPurchaseListActivity"
            android:windowSoftInputMode="adjustPan|stateHidden"/>
        <activity android:name=".activity.afterSales.activity.AfterSalesDetailActivity"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustPan"/>
        <activity android:name=".activity.CouponRelatedGoodsActivity"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity android:name=".activity.DownloadRecordActivity"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity android:name=".activity.DownloadRelatedAptitudeActivity"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <provider
                android:name="androidx.core.content.FileProvider"
                android:authorities="${applicationId}.fileProvider"
                android:exported="false"
                android:grantUriPermissions="true"
                tools:replace="android:authorities">
            <meta-data
                    android:name="android.support.FILE_PROVIDER_PATHS"
                    android:resource="@xml/file_provider_path"
                    tools:replace="android:resource"/>
        </provider>

        <service
                android:name="com.tencent.smtt.export.external.DexClassLoaderProviderService"
                android:label="dexopt"
                android:process=":dexopt"/> <!-- 18:9屏幕适配 -->
        <meta-data
                android:name="android.max_aspect"
                android:value="2.2"/>
        <meta-data
                android:name="com.zhuge.config.MaxSendSize"
                android:value="1000000"/> <!-- 百度地图开始 -->
        <service
                android:name="com.baidu.location.f"
                android:enabled="true"
                android:process=":remote"/>

        <meta-data
                android:name="com.baidu.lbsapi.API_KEY"
                android:value="${BaiduMap_AK}"/> <!-- 百度地图结束 -->
        <meta-data
                android:name="com.xyy.config.UploadLimit"
                android:value="1"/> <!-- ****************************** 推送  **************************************** -->
        <receiver android:name=".service.YbmPushMsgReceiver">
            <intent-filter>
                <action android:name="com.xyy.push.ACTION_RECEIVE_NOTIFICATION"/>
                <action android:name="com.xyy.push.ACTION_RECEIVE_NOTIFICATION_CLICK"/>
                <action android:name="com.xyy.push.ACTION_RECEIVE_MESSAGE"/>
                <action android:name="com.xyy.push.ACTION_RECEIVE_REGISTRATION_ID"/>
                <action android:name="com.xyy.push.ACTION_RECEIVE_LOGIN_OUT"/>
                <action android:name="com.xyy.push.ACTION_RECEIVE_SET_ALIAS"/>

                <category android:name="${applicationId}"/>
            </intent-filter>
        </receiver> <!-- 极光 -->
        <!-- Required since 3.0.7 -->
        <!-- 新的 tag/alias 接口结果返回需要开发者配置一个自定的广播 -->
        <!-- 3.3.0开始所有事件将通过该类回调 -->
        <!-- 该广播需要继承 JPush 提供的 JPushMessageReceiver 类, 并如下新增一个 Intent-Filter -->
        <receiver
                android:name=".service.CustomJPushMessageReceiver"
                android:enabled="true"
                android:exported="false">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.RECEIVE_MESSAGE"/>

                <category android:name="${applicationId}"/>
            </intent-filter>
        </receiver> <!-- 小米推送的AppKey ,APPID****请务必在数值前添加"\\"，否则长数字取值会null**** -->
        <meta-data
                android:name="XMPUSH_APPKEY"
                android:value="${XMPUSH_APPKEY}"/>
        <meta-data
                android:name="XMPUSH_APPID"
                android:value="${XMPUSH_APPID}"/> <!-- 小米推送的AppKey ,APPID****请务必在数值前添加"\\"，否则长数字取值会null**** -->
        <!-- 华为推送的appId     android:value="appid=********" -->
        <meta-data
                android:name="com.huawei.hms.client.appid"
                android:value="${HWPUSH_APPID}"/> <!-- 华为推送 -->
        <!-- 处理推送通知点击事件 -->
        <activity
                android:name=".service.HandleNotificationPendingIntentActivity"
                android:launchMode="singleTask"
                android:theme="@android:style/Theme.NoDisplay"/>
        <activity android:name=".activity.CommonWebviewActivity"/> <!-- OPPO推送的appId -->
        <activity android:name=".activity.NongWebviewActivity"
            android:hardwareAccelerated="true"/>
        <meta-data
                android:name="OPPOPUSH_APPKEY"
                android:value="${OPPOPUSH_APPKEY}"/>
        <meta-data
                android:name="OPPOPUSH_APPSECRET"
                android:value="${OPPOPUSH_APPSECRET}"/>
        <!--Push开放平台中应用的appid 和api key-->
        <meta-data
            android:name="com.vivo.push.api_key"
            android:value="${VIVOPUSH_APPKEY}"/>

        <meta-data
            android:name="com.vivo.push.app_id"
            android:value="${VIVOPUSH_APPID}"/>
        <!-- 荣耀推送APPID -->
        <meta-data
            android:name="com.hihonor.push.app_id"
            android:value="${HONORPUSH_APPID}" />
        <!--适配华为（huawei）刘海屏-->
        <meta-data
            android:name="android.notch_support"
            android:value="true"/>
        <!--适配小米（xiaomi）刘海屏-->
        <meta-data
            android:name="notch.config"
            android:value="portrait|landscape" />

    </application>

</manifest>