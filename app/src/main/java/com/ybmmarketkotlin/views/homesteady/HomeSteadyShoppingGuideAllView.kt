package com.ybmmarketkotlin.views.homesteady

import com.ybmmarket20.view.homesteady.*

import android.content.Context
import android.graphics.Rect
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.SimpleItemAnimator
import com.ybm.app.view.WrapGridLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.bean.homesteady.*
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.utils.IntervalListener
import com.ybmmarket20.utils.IntervalUtil
import com.ybmmarketkotlin.adapter.HomeSteadyShoppingGuideAllAdapter

/**
 * 首页导购入口(新的)
 */
class HomeSteadyShoppingGuideAllView(context: Context, attr: AttributeSet) : RecyclerView(context, attr), IHomeSteady {

    var mData = mutableListOf<ShoppingGuideAllItem>()
    var mAdapter: HomeSteadyShoppingGuideAllAdapter? = null
    var mIntervalListenerList = mutableListOf<IntervalListener>()//记录注册的倒计时
    private var analysisCallback: ((String, Int, String, String) -> Unit)? = null


    init {
        //防止刷新图片闪烁
        itemAnimator?.changeDuration = 0
        try {
            (itemAnimator as SimpleItemAnimator).supportsChangeAnimations = false
        } catch (e: Exception) {
            e.printStackTrace()
        }
        addItemDecoration(ShoppingGuideDecoration(5f))
    }

    override fun initPlaceHold() {
        //setAdapterData(generatePlaceHoldData())
    }

    fun setshoppingGuideData(data: MutableList<ShoppingGuideAllItem>?) {
        if (data == null) return
        data.apply(this::setAdapterData)
        //反注册记录下的倒计时
        if (mIntervalListenerList.size > 0) {
            for (listener in mIntervalListenerList) {
                IntervalUtil.unRegisterInterval(listener)
            }
        }
        // 处理倒计时
        for (i in 0 until data.size) {
            val seckillInfo: SeckillAllInfo? = data[i].seckillInfo
            var intervalListener: IntervalListener? = null
            if (seckillInfo != null && seckillInfo.status != SECKILL_STATUS_END && seckillInfo.status != SECKILL_CONVERSE) {
                seckillInfo.also {
                    intervalListener = object : IntervalListener {
                        override fun callback() {
                            if (it.status == SECKILL_STATUS_WAIT) {
                                it.currentDate += 1000
                                if (it.currentDate >= it.startDate) it.status = SECKILL_STATUS_HAVE_IN_HAND
                            } else if (it.status == SECKILL_STATUS_HAVE_IN_HAND) {
                                it.currentDate += 1000
                                if (it.currentDate >= it.endDate) {
                                    it.status = SECKILL_STATUS_END
                                    it.time = timeFormat(it.endDate - it.currentDate)
                                    mAdapter?.notifyItemRangeChanged(i, 1)
                                    if (intervalListener != null) {
                                        IntervalUtil.unRegisterInterval(intervalListener!!)
                                    }
                                } else {
                                    it.time = timeFormat(it.endDate - it.currentDate)
                                    mAdapter?.notifyItemRangeChanged(i, 1)
                                }
                            }
                        }
                    }
                    IntervalUtil.registerInterval(intervalListener!!)
                    mIntervalListenerList.add(intervalListener as IntervalListener)
                }
            }
        }

    }
    /**
     * 时间格式化
     */
    private fun timeFormat(millisUntilFinished: Long): String {
        val h = millisUntilFinished / 1000 / 3600
        val fen = millisUntilFinished / 1000 % 3600 / 60
        val s = millisUntilFinished / 1000 % 60
        val hh = if (h < 10) {
            "0$h"
        } else {
            "" + h
        }
        val ff = if (fen < 10) {
            "0$fen"
        } else {
            "" + fen
        }
        val ss = if (s < 10) {
            "0$s"
        } else {
            "" + s
        }
        return "$hh:$ff:$ss"
    }

    private fun setAdapterData(data: MutableList<ShoppingGuideAllItem>) {
        mData.clear()
        mData.addAll(data)
        if (mAdapter == null) {
            mAdapter = HomeSteadyShoppingGuideAllAdapter(context, mData, R.layout.item_home_steady_shopping_guide_all)
            layoutManager = WrapGridLayoutManager(context, 2, LinearLayoutManager.VERTICAL, false)
            adapter = mAdapter
            analysisCallback?.let(mAdapter!!::setAnalysisCallback)
        } else mAdapter?.notifyDataSetChanged()
    }


    private fun generatePlaceHoldData(): MutableList<ShoppingGuideAllItem> = arrayListOf<ShoppingGuideAllItem>().apply {
        for (i in 0 until 6) {
            // add(ShoppingGuideAllItem().apply { itemType = HOME_STEADY_LAYOUT_DEFAULT })
        }
    }

    /**
     * 设置一审状态
     */
    fun setLicenseStatus(licenseStatus: Int) {
        mAdapter?.setLicenseStatus(licenseStatus)
        mAdapter?.notifyItemChanged(0)
    }

    /**
     * 设置埋点回调
     */
    fun setAnalysisCallback(callback: (action: String, offset: Int, text: String, sku_id: String) -> Unit) {
        analysisCallback = callback
    }

    inner class ShoppingGuideDecoration(var divider: Float) : ItemDecoration() {
        override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: State) {
            super.getItemOffsets(outRect, view, parent, state)
            val position: Int = parent.getChildAdapterPosition(view)
            val spanCount = if (parent.layoutManager is GridLayoutManager) {
                (parent.layoutManager as GridLayoutManager).spanCount
            } else if (parent.layoutManager is LinearLayoutManager) {
                1
            } else 2
            //处理top
            if (position < spanCount) {
                // 第一排
                outRect.top = 0
            } else {
                // 非第一排
                outRect.top = ConvertUtils.dp2px(divider)
            }
            //处理left和right
            if (position % spanCount == spanCount - 1) {
                //最右边
                outRect.right = 0
                outRect.left = 0
            } else {
                //最左边 和中间
                outRect.right = ConvertUtils.dp2px(divider)
                outRect.left = 0
            }

        }
    }

}