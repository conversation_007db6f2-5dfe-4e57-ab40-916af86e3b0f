package com.ybmmarketkotlin.views

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.drawable.Drawable
import android.text.style.ImageSpan
import com.apkfuns.logutils.LogUtils

/**
 * 竖直方向居中的 ImageSpan
 *
 * 在传入bitmap的时候，ImageSpan内部会创建Drawable，创建Drawable的时候，系统会自动根据DPI压缩，因此这里要传入Bitmap的实际想要长款的DPI倍
 *
 */
class CenterVerticalImageSpan : ImageSpan {

    constructor(drawable: Drawable) : super(drawable)
    constructor(bitmap: Bitmap) : super(bitmap)


    override fun getSize(paint: Paint, text: CharSequence?, start: Int, end: Int, fm: Paint.FontMetricsInt?): Int {
        val bounds = drawable.bounds


        LogUtils.d("xyd 居中的Span左= ${bounds.left} ; 上 = ${bounds.top} ; 右 = ${bounds.right} 下 = ${bounds.bottom}  \n  实际宽=${drawable.intrinsicWidth} 实际高=${drawable.intrinsicHeight}")
        fm?.let {
            val fmPaint = paint.getFontMetricsInt()
            var fontHeight = fmPaint.bottom - fmPaint.top
            var drHeight = bounds.bottom - bounds.top

            var top = drHeight / 2 - fontHeight / 4
            var bottom = drHeight / 2 + fontHeight / 4

            fm.ascent = -bottom
            fm.top = -bottom
            fm.bottom = top
            fm.descent = top
        }

        return bounds.right
    }

    override fun draw(canvas: Canvas, text: CharSequence?, start: Int, end: Int, x: Float, top: Int, y: Int, bottom: Int, paint: Paint) {
        val b: Drawable = getDrawable()
        canvas.save()
        var transY: Int = 0
        transY = ((bottom - top) - b.getBounds().bottom) / 2 + top;
        canvas.translate(x, transY.toFloat());
        b.draw(canvas);
        canvas.restore();
    }
}