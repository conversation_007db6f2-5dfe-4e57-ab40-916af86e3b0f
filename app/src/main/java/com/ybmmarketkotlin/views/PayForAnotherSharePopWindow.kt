package com.ybmmarketkotlin.views

import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import com.ybmmarket20.R
import com.ybmmarket20.bean.PayForAnotherBean
import com.ybmmarket20.view.BaseBottomPopWindow

//分享类型-微信
const val SHARED_TYPE_WX = 1
//分享类型-支付宝
const val SHARED_TYPE_AP = 2
//分享类型-复制链接
const val SHARED_TYPE_COPY = 3

/**
 * 他人代付分享微信弹窗
 */
class PayForAnotherSharePopWindow(
//    private val isShowLink: Boolean,
//    private val isShowWx: Boolean,
    private val data: List<PayForAnotherBean>,
    private val listener: DialogItemClick?
) : BaseBottomPopWindow() {
    private lateinit var llLinkUrl: LinearLayout
    private lateinit var llWx: LinearLayout
    private lateinit var llAp: LinearLayout

    init {
        initUi()//isShowWx会在initView()之后赋值 所以空实现父类initView（）
    }

    override fun getLayoutId(): Int {
        return R.layout.layout_pay_for_another_share_pop_window_view
    }

    private fun initUi() {
        contentView.findViewById<View>(R.id.tv_cancel).setOnClickListener { dismiss() }
        data.forEach{bean ->
            when (bean.channelType) {
                SHARED_TYPE_WX -> {
                    llWx = contentView.findViewById<View>(R.id.ll_share_wx) as LinearLayout
                    llWx.visibility = if (bean.showState == 1) View.VISIBLE else View.GONE
                    llWx.setOnClickListener {shareClick(bean)}
                }

                SHARED_TYPE_AP -> {
                    llAp = contentView.findViewById<View>(R.id.ll_share_ap) as LinearLayout
                    llAp.visibility = if (bean.showState == 1) View.VISIBLE else View.GONE
                    llAp.setOnClickListener {shareClick(bean)}
                }

                SHARED_TYPE_COPY -> {
                    llLinkUrl = contentView.findViewById<View>(R.id.ll_share_link_url) as LinearLayout
                    llLinkUrl.visibility = if (bean.showState == 1) View.VISIBLE else View.GONE
                    llLinkUrl.setOnClickListener {shareClick(bean)}
                }
            }
        }
    }

    private fun shareClick(bean: PayForAnotherBean) {
        dismiss()
        listener?.click(bean)
    }

    override fun initView() {
    }

    override fun getLayoutParams(): LinearLayout.LayoutParams? {
        return LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
    }

    interface DialogItemClick {
        fun click(bean: PayForAnotherBean)
    }

}