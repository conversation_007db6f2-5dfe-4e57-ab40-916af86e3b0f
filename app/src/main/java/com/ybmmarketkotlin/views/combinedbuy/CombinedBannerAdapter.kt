package com.ybmmarketkotlin.views.combinedbuy

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ybmmarket20.R
import com.ybmmarket20.bean.GroupPurchaseInfo
import com.ybmmarketkotlin.views.combinedbuy.CombinedBannerAdapter.Holder
import com.youth.banner.adapter.BannerAdapter

/**
 * @desc    组合购banner适配器
 */
class CombinedBannerAdapter(val mContext: Context, var dataList: List<GroupPurchaseInfo>) : BannerAdapter<GroupPurchaseInfo, Holder>(dataList) {

    var mListener: CombinedBuyListener? = null
    var curPageIndex = 0    // 当前第几页
    var banner: BannerCombinedBuyView? = null

    override fun onCreateHolder(parent: ViewGroup, viewType: Int): Holder =
        Holder(LayoutInflater.from(mContext).inflate(R.layout.banner_combined_buy_single, parent, false))


    override fun onBindViewHolder(
        holder: Holder, position: Int,
        payloads: MutableList<Any>
    ) {
        if (payloads.isNullOrEmpty()) {
            super.onBindViewHolder(holder, position, payloads)
        } else {
            payloads.forEach { it ->
                val data = it as GroupPurchaseInfo
                banner?.isAutoLoop(data.mainProduct.selectStatus == 1)
                banner?.setUserInputEnabled(data.mainProduct.selectStatus == 1)
                holder.itemView.findViewById<CombinedBuySingleLayout>(R.id.layout).apply {
                    setNewData(data)
                }
            }
        }
    }

    override fun onBindView(
        holder: Holder,
        data: GroupPurchaseInfo,
        position: Int,
        size: Int
    ) {
        val combinedSingle = holder.itemView.findViewById<CombinedBuySingleLayout>(R.id.layout)
        if(position == 0){
            banner?.isAutoLoop(data.mainProduct.selectStatus == 1)
            banner?.setUserInputEnabled(data.mainProduct.selectStatus == 1)
        }
        combinedSingle.curSubPos = position
        combinedSingle.subCount = dataList.size
        combinedSingle.mListener = mListener
        combinedSingle.banner = banner
        combinedSingle.setNewData(data)
    }

    /**
     * 无限轮播：
     * itemCount(viewCount) = 数据count+2
     */
    fun updateData(data: GroupPurchaseInfo) {
        run breakFor@{
            val combinedPro = data.combinedList?.filter { !it.isMainProduct }
            if (!combinedPro.isNullOrEmpty()) {
                dataList.forEachIndexed { index, bean ->
                    if (combinedPro[0].id == bean.subProducts[0].id) {
                        // 去除无限轮询
                        val adapterPositions = mutableListOf<Int>().apply {
                            // 中间主位置 (realPosition + 1)
                            add(index + 1)
                            // 检查是否是第一个数据项 (需要更新最后一个适配器位置)
                            if (index == 0) {
                                add(realCount + 1)
                            }
                            // 检查是否是最后一个数据项 (需要更新第一个适配器位置)
                            if (index == realCount - 1) {
                                add(0)
                            }
                        }
                        adapterPositions.forEach {
                            notifyItemChanged(it,data)
                        }
                        return@breakFor
                    }
                }
            }
        }
    }

    class Holder(itemView: View) : RecyclerView.ViewHolder(itemView) {}
}