package com.ybmmarketkotlin.views.combinedbuy

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.xyy.canary.utils.DensityUtil
import com.ybmmarket20.R
import com.ybmmarket20.bean.GroupPurchaseInfo
import com.ybmmarket20.bean.RowsBeanCombinedExt
import com.ybmmarket20.utils.SpanUtils
import com.ybmmarket20.utils.UiUtils
import com.ybmmarketkotlin.adapter.GoodsCombinedBuySingleItemAdapter

/**
 * <AUTHOR>
 * @desc    组合购区域
 * @date    2025/5/6
 */
class CombinedBuySingleLayout @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, def: Int = 0) :
    ConstraintLayout(context, attrs, def) {
    private val rlvGoods by lazy { findViewById<RecyclerView>(R.id.rlvGoods) }
    private val tvSettle by lazy { findViewById<TextView>(R.id.tvSettle) }
    private val tvTotalPrice by lazy { findViewById<TextView>(R.id.tvTotalPrice) }
    private val tvDiscount by lazy { findViewById<TextView>(R.id.tvDiscount) }
    private val tvPage by lazy { findViewById<TextView>(R.id.tvPage) }
    var banner: BannerCombinedBuyView? = null

    var mListener: CombinedBuyListener? = null
        set(value) {
            field = value
            adapter.mListener = object : CombinedBuyListener {
                override fun changeNum(bean: RowsBeanCombinedExt, subPos: Int, addFlag: Boolean, preNum: Int) {
                    field?.changeNum(bean, curSubPos, addFlag, preNum)
                }

                override fun changeNumClick(bean: RowsBeanCombinedExt, curSubPosition: Int, addFlag: Boolean, preNum: Int) {
                    field?.changeNumClick(bean, curSubPos, addFlag, preNum)
                }

                override fun jumpToGoodsDetail(bean: RowsBeanCombinedExt) {
                    field?.jumpToGoodsDetail(bean)
                }

                override fun startBanner() {
                    banner?.start()
                }

                override fun stopBanner() {
                    banner?.stop()
                }
            }
        }

    var curSubPos = 0
    var subCount = 0
    val adapter by lazy {
        GoodsCombinedBuySingleItemAdapter(null)
    }

    private var purchaseInfo: GroupPurchaseInfo? = null

    init {
        LayoutInflater.from(context).inflate(R.layout.layout_combined_buy_single, this)
        initView()
    }

    private fun initView() {
        rlvGoods.adapter = adapter
        tvSettle.setOnClickListener {
            // 下单: 轮播重计时
            mListener?.startBanner()
            mListener?.preSettle(curSubPos)
        }
    }

    /**
     * 首次设置商品或变更商品
     */
    fun setNewData(bean: GroupPurchaseInfo) {
        tvPage.text = "${curSubPos+1}/${subCount}"
        purchaseInfo = bean
        purchaseInfo!!.combinedList = mutableListOf(
            bean.mainProduct,
            bean.subProducts[0]
        )
        adapter.setNewData(purchaseInfo!!.combinedList)
        updateTotalData()
    }

    private fun updateTotalData() {
        if (purchaseInfo!!.combinedDiscountSub == null || purchaseInfo!!.combinedDiscountSub == 0.0) {
            tvDiscount.visibility = GONE
        } else {
            tvDiscount.visibility = VISIBLE
            tvDiscount.text = "为您省：¥${UiUtils.transform(purchaseInfo!!.combinedDiscountSub!!)}"
        }
        if(purchaseInfo!!.realPay.isNullOrEmpty()){
            tvSettle.alpha = 0.5f
            tvSettle.isEnabled = false
        }else{
            tvSettle.alpha = 1f
            tvSettle.isEnabled = true
        }
        val priceSplit = UiUtils.transform(purchaseInfo!!.realPay ?: "0.00").split(".")
        tvTotalPrice.text = SpanUtils().append("到手价：")
            .setForegroundColor(Color.parseColor("#666666"))
            .append("¥").setFontSize(DensityUtil.dip2px(context, 12f))
            .setBold()
            .append(priceSplit[0])
            .setBold()
            .setFontSize(DensityUtil.dip2px(context, 18f))
            .apply {
                if (priceSplit.size > 1) {
                    this.append(".${priceSplit[1]}")
                    this.setBold()
                    this.setFontSize(DensityUtil.dip2px(context, 14f))
                }
            }
            .create()
    }
}