package com.ybmmarketkotlin.views

import android.content.Intent
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.ybmmarket20.R
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.cms.MarqueeViewCms
import com.ybmmarketkotlin.bean.ApplyNoticeBean
import com.ybmmarketkotlin.utils.addCountDown
import org.json.JSONObject

fun MarqueeViewCms.createViewList(mutableList: MutableList<ApplyNoticeBean>): List<View> {
    mutableList.let {
        val emptyViewList = mutableListOf<View>()
        it.forEach {
            emptyViewList.add(createViewFromeLayout(it))
        }
        return emptyViewList
    }
}

fun MarqueeViewCms.createViewFromeLayout(bean: ApplyNoticeBean): View {
    val view = View.inflate(context, R.layout.layout_item_apply_notice, null)
    bean.let {

        val ivIcon = view.findViewById<ImageView>(R.id.iv_icon)
        val tvNoticeTitle = view.findViewById<TextView>(R.id.tv_notice_title)
        val tvNoticeSubtitle = view.findViewById<TextView>(R.id.tv_notice_subtitle)
        val btnCommit = view.findViewById<TextView>(R.id.btn_commit)

        ImageUtil.load(context, it.iconUrl, ivIcon)
        tvNoticeTitle?.text = when {
            it.status == 100 -> "待填写退货物流"
            it.status == 101 -> "待客户确认退款"
            it.payType == 1 -> "待支付"
            else -> "待上传电子凭证"
        }
        if (it.payType == 1 || it.status == 100 || it.status == 101) {
            if (bean.countDownNewTime > 0) {
                tvNoticeSubtitle?.apply {
                    addCountDown(
                        expireTime = bean.countDownNewTime * 1000L,
                        preString = "剩余时间："
                    ) {
                        if (it.status == 101) {
                            Intent(IntentCanst.ACTION_CONFIRM_REFUND_STATUS).apply {
                                putExtra("orderId", bean.orderId)
                                putExtra("status", 101)
                            }
                        } else {
                            Intent(IntentCanst.ACTION_ORDER_STATUS)
                        }.also(LocalBroadcastManager.getInstance(context)::sendBroadcast)

                    }
                }
            } else if(it.status == 101) {
                LocalBroadcastManager.getInstance(context).sendBroadcast(Intent(IntentCanst.ACTION_CONFIRM_REFUND_STATUS).apply {
                    putExtra("orderId", bean.orderId)
                    putExtra("status", 101)
                })
            } else {
                tvNoticeSubtitle?.text = "待支付"
            }
            when (it.status) {
                100 -> {
                    btnCommit?.text = "去填写"
                }
                101 -> {
                    btnCommit?.text = "去处理"
                }
                else -> {
                    btnCommit?.text = "去支付"
                }
            }
        } else {
            tvNoticeSubtitle?.text = "请点击右侧完成电子凭证上传"
            btnCommit?.text =  "去上传"
        }

        btnCommit?.setOnClickListener {_->
            if (it.status == 101) {
                RoutersUtils.open("ybmpage://refunddetail/" + bean.refundOrderId + "/" + bean.refundOrderNo)
            } else {
                val routerStr = "ybmpage://orderdetail/${bean.orderId}"
                RoutersUtils.open(routerStr)
                val obj = JSONObject().apply {
                    put("orderNumber", it.orderNo)
                    put("orderStatus", it.status)
                    put("action", routerStr)
                }
                XyyIoUtil.track("page_orderBoard_Click", obj)
            }
        }

    }
    return view
}
