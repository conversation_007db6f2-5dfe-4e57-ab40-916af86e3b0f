package com.ybmmarketkotlin.views

import android.content.Context
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import android.util.AttributeSet
import android.view.View
import android.widget.LinearLayout
import com.ybmmarket20.R

/**
 * 随列表滑动的线段指示器
 */
class RvIndicatorView @JvmOverloads constructor(private val mContext: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : LinearLayout(mContext, attrs, defStyleAttr) {
    private var mIndicatorLayout: View? = null
    private var mIndicatorView: View? = null
    private var mIndicatorHeight = 0
    private var mIndicatorWidth = 0
    private var mIndicatorBg = 0
    private var mIndicatorLayoutWidth = 0
    private var mIndicatorLayoutHeight = 0
    private var mIndicatorLayoutBg = 0
    private fun obtainAttributes(context: Context, attrs: AttributeSet?) {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.RvIndicatorView)
        mIndicatorHeight = ta.getDimension(R.styleable.RvIndicatorView_rv_indicator_height, dp2px(2f).toFloat()).toInt()
        mIndicatorWidth = ta.getDimension(R.styleable.RvIndicatorView_rv_indicator_width, dp2px(20f).toFloat()).toInt()
        mIndicatorBg = ta.getResourceId(R.styleable.RvIndicatorView_rv_indicator_background, R.drawable.indicator_before)
        mIndicatorLayoutHeight = ta.getDimension(R.styleable.RvIndicatorView_rv_indicator_layout_height, dp2px(2f).toFloat()).toInt()
        mIndicatorLayoutWidth = ta.getDimension(R.styleable.RvIndicatorView_rv_indicator_layout_width, dp2px(30f).toFloat()).toInt()
        mIndicatorLayoutBg = ta.getResourceId(R.styleable.RvIndicatorView_rv_indicator_layout_background, R.drawable.indicator_bg)
        ta.recycle()
    }

    private fun initView(context: Context) {
        val layoutView = View.inflate(context, R.layout.layout_rv_indicator_view, this)
        mIndicatorLayout = layoutView.findViewById(R.id.view_indicator_layout)
        mIndicatorView = layoutView.findViewById(R.id.view_indicator)
        val layoutParams = mIndicatorLayout?.layoutParams as ConstraintLayout.LayoutParams
        layoutParams.height = mIndicatorLayoutHeight
        layoutParams.width = mIndicatorLayoutWidth
        mIndicatorLayout?.layoutParams = layoutParams
        mIndicatorLayout?.setBackgroundResource(mIndicatorLayoutBg)
        val mIndicatorParams = mIndicatorView?.layoutParams as ConstraintLayout.LayoutParams
        mIndicatorParams.height = mIndicatorHeight
        mIndicatorParams.width = mIndicatorWidth
        mIndicatorView?.layoutParams = mIndicatorParams
        mIndicatorView?.setBackgroundResource(mIndicatorBg)
    }

    fun setRvOnScrolledListener(recyclerView: RecyclerView) {
        //当前RecyclerView显示区域的高度。水平列表屏幕从左侧到右侧显示范围
        val extent = recyclerView.computeHorizontalScrollExtent()

        //整体的高度，注意是整体，包括在显示区域之外的。
        val range = recyclerView.computeHorizontalScrollRange()

        //已经滚动的距离，为0时表示已处于顶部。
        val offset = recyclerView.computeHorizontalScrollOffset()


        //计算出溢出部分的宽度，即屏幕外剩下的宽度
        val maxEndX = range - extent.toFloat()

        //计算比例
        val proportion = offset / maxEndX
        val layoutWidth = mIndicatorLayout?.width?:0
        val indicatorViewWidth = mIndicatorView?.width?:0
        //可滑动的距离
        val scrollableDistance = layoutWidth - indicatorViewWidth

        //设置滚动条移动
        mIndicatorView?.translationX = scrollableDistance * proportion
    }

    protected fun dp2px(dp: Float): Int {
        val scale = mContext.resources.displayMetrics.density
        return (dp * scale + 0.5f).toInt()
    }

    init {
        obtainAttributes(mContext, attrs)
        initView(mContext)
    }
}