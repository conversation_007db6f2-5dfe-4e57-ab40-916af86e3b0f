package com.ybmmarketkotlin.views

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.graphics.Rect
import android.util.AttributeSet
import android.view.TouchDelegate
import android.view.View
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.SeekBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SeckillBean
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.view.ProductEditLayoutNew
import com.ybmmarketkotlin.utils.TimeUtils

class SeckillTimeView : ConstraintLayout {

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int, defStyleRes: Int) : super(context, attrs, defStyleAttr, defStyleRes) {

    }

    init {
        inflate(context, R.layout.view_seckill_time, this)
        tvTimePrefix = findViewById(R.id.tv_time_prefix)
        ivLightning = findViewById(R.id.iv_lightning)
        tvTime = findViewById(R.id.tv_time)
    }

    private var tvTimePrefix: TextView
    private var ivLightning: ImageView
    var tvTime: TextView

    @SuppressLint("UseCompatLoadingForDrawables")
    fun setCountDownData(rowsBean: RowsBean?) {
        val seckillBean = rowsBean?.actSk
        val el_edit = (this.parent as View).findViewById<ProductEditLayoutNew?>(R.id.el_sec_kill_edit)
        seckillBean.takeIf {
            if (it == null) {
                visibility = View.GONE
                (this.parent as View).findViewById<View>(R.id.tv_sec_kill_btn)?.visibility = View.GONE
                false
            } else {
                visibility = View.VISIBLE
                (this.parent as View).findViewById<View>(R.id.tv_sec_kill_btn)?.visibility = if (el_edit?.productNum ?: 0 <= 0) View.VISIBLE else View.GONE
                true
            }
        }?.let {
            val localDiff: Long = System.currentTimeMillis() - (it.responseLocalTime)
            val leftTime = it.surplusTime - localDiff
            if (it.status == 0) {
                tvTimePrefix.text = "距开始"
//                tvTimePrefix.background = resources.getDrawable(R.drawable.icon_seckill_timeout_prefix, null)
//                this.background = resources.getDrawable(R.drawable.bg_seckill_timeouting, null)
                tvTime.text = TimeUtils.timeFormat(leftTime)
                tvTime.setTextColor(Color.parseColor("#FF2121"))
                val iv_numAdd = el_edit?.findViewById<ImageView?>(R.id.iv_numAdd)
                (this.parent as View).findViewById<TextView>(R.id.tv_sec_kill_btn)?.takeIf { it.visibility == View.VISIBLE }?.apply {
                    text = "即将开始"

                    setBackgroundResource(R.color.color_00b955)
                    val bounds = Rect()
                    getHitRect(bounds)

                    val touchDelegate = TouchDelegate(bounds, iv_numAdd)
                    setTouchDelegate(touchDelegate)
                    setOnClickListener {
                    }
                }
            }

            if (it.status == 1) {
                tvTimePrefix.text = "距结束"
//                tvTimePrefix.background = resources.getDrawable(R.drawable.icon_seckill_timeout_prefix, null)
//                this.background = resources.getDrawable(R.drawable.bg_seckill_timeouting, null)
                tvTime.text = TimeUtils.timeFormat(leftTime)
                tvTime.setTextColor(Color.parseColor("#FF2121"))
                val iv_numAdd = el_edit?.findViewById<ImageView?>(R.id.iv_numAdd)
                (this.parent as View).findViewById<TextView>(R.id.tv_sec_kill_btn)?.takeIf { it.visibility == View.VISIBLE }?.apply {
                    visibility = View.GONE
//                    text = "抢购"
//                    setBackgroundResource(R.color.color_FF6204)
//
//                    val bounds = Rect()
//                    getHitRect(bounds)
//
//                    val touchDelegate = TouchDelegate(bounds, iv_numAdd)
//                    setTouchDelegate(touchDelegate)
//                    setOnClickListener {
//                        visibility = View.GONE
//                        iv_numAdd?.callOnClick()
//                        el_edit?.visibility = View.VISIBLE
//                    }
                }
            }

            if (it.status == 2) {
                tvTimePrefix.text = "距结束"
                tvTimePrefix.background = resources.getDrawable(R.drawable.icon_seckill_ending_prefix, null)
                this.background = resources.getDrawable(R.drawable.bg_seckill_time_end, null)
                tvTime.text = "已结束"
                tvTime.setTextColor(Color.parseColor("#9494A5"))
                (this.parent as View).findViewById<TextView>(R.id.tv_sec_kill_btn)?.apply {
                    visibility = View.VISIBLE
                    text = "已抢光"
                    setBackgroundResource(R.color.color_AAACB9)
                    setOnClickListener {
                    }
                }
            }
            val secKillProgress = (this.parent as View).findViewById<SeekBar?>(R.id.seckill_progress)?: (this.parent.parent as View).findViewById(R.id.seckill_progress)
            secKillProgress?.apply {
                this.visibility = View.VISIBLE
                this.progress = it.percentage
            }
            val secKillDesc = (this.parent as View).findViewById<TextView?>(R.id.seckill_desc)?: (this.parent.parent as View).findViewById(R.id.seckill_desc)
            secKillDesc?.apply {
                this.visibility = View.VISIBLE
                this.text = "已售${it.percentage}%"
            }
        }
    }

}