package com.ybmmarketkotlin.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.ProductDetailCombinedBean
import com.ybmmarket20.network.request.ComodityRequest
import com.ybmmarketkotlin.bean.CommodityGroupRecommondBean
import com.ybmmarketkotlin.bean.SameSpecificationsListResponse
import kotlinx.coroutines.launch

class CommodityViewModel : ViewModel() {

    val spellGroupRecommondBean: MutableLiveData<CommodityGroupRecommondBean> by lazy { MutableLiveData<CommodityGroupRecommondBean>() }

    private val _sameSpecificationsListLiveData: MutableLiveData<BaseBean<SameSpecificationsListResponse>> = MutableLiveData<BaseBean<SameSpecificationsListResponse>>()
    val sameSpecificationsListLiveData: LiveData<BaseBean<SameSpecificationsListResponse>> = _sameSpecificationsListLiveData

    private val _combinedProductLiveData: MutableLiveData<BaseBean<ProductDetailCombinedBean>> = MutableLiveData<BaseBean<ProductDetailCombinedBean>>()
    val combinedProductLiveData: LiveData<BaseBean<ProductDetailCombinedBean>> = _combinedProductLiveData

    fun getGroupRecommendProducts(productId: String) {
        viewModelScope.launch {
            val reult = ComodityRequest().getGroupRecommendProducts(productId)
            spellGroupRecommondBean?.postValue(reult?.data)
        }
    }


    fun getSameSpecificationsList(productId: String) {
        viewModelScope.launch {
            val result = ComodityRequest().getSameSpecificationsList(productId)?:return@launch
            _sameSpecificationsListLiveData.postValue(result)
        }
    }

    fun getCombinedProducts(productId: String,showRecPurchaseType:Int,searchRecPurchaseStrategyCode:String? = null) {
        viewModelScope.launch {
            val result = ComodityRequest().getCombinedProducts(productId,showRecPurchaseType,searchRecPurchaseStrategyCode)?:return@launch
            _combinedProductLiveData.postValue(result)
        }
    }

}