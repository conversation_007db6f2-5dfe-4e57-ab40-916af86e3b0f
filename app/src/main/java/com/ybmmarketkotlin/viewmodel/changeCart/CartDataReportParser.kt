package com.ybmmarketkotlin.viewmodel.changeCart

import com.ybmmarket20.bean.cart.CartBean
import com.ybmmarket20.bean.cart.CartShopList
import com.ybmmarket20.bean.cart.CartShoppingGroupFrontBean
import com.ybmmarket20.bean.cart.CartSortedNewBean
import com.ybmmarket20.xyyreport.page.cart.CartReport
import com.ybmmarket20.xyyreport.page.cart.CartReportDataUtil
import com.ybmmarket20.xyyreport.page.common.goods.CartReportBean
import com.ybmmarket20.xyyreport.page.common.goods.CartReportProd
import com.ybmmarket20.xyyreport.page.common.goods.CartReportProdGroup
import com.ybmmarket20.xyyreport.page.common.goods.CartReportShop
import com.ybmmarket20.xyyreport.paramsInfo.ICartRowsBeanInfo
import com.ybmmarket20.xyyreport.spm.SpmUtil

/**
 * 购物车数据解析
 */
class CartDataReportParser {

    companion object {
        private var cartDataParser: CartDataReportParser? = null
            get() {
                if (field == null) cartDataParser = CartDataReportParser()
                return field
            }

        fun getInstance(): CartDataReportParser = cartDataParser!!
    }

    private fun parseCartBeanForReport(cartBean: CartBean?): CartReportBean {
        return CartReportBean(parseCartShopList(cartBean?.company?.flatMap { it.shop }))
    }

    /**
     * 解析店铺
     */
    private fun parseCartShopList(shopList: List<CartShopList>?): List<CartReportShop>? {
        var shopPosition = 0
        return shopList?.map {
            shopPosition ++
            CartReportShop(
                parseCartProdGroupList(it.shoppingGroupFrontDtos, shopPosition, it.originalShopCode),
                it.originalShopName,
                it.originalShopCode,
                shopPosition
            )
        }
    }

    /**
     * 解析商品组
     */
    private fun parseCartProdGroupList(prodGroupList: List<CartShoppingGroupFrontBean>?, shopPosition: Int, shopCode: String?): List<CartReportProdGroup>? {
        var prodGroupPosition = 0
        return prodGroupList?.map {
            prodGroupPosition ++
            CartReportProdGroup(
                parseCartProdList(it.sorted, shopPosition, prodGroupPosition, shopCode),
                prodGroupPosition
            )
        }
    }

    /**
     * 解析商品
     */
    private fun parseCartProdList(items: List<CartSortedNewBean>?, shopPosition: Int, prodGroupList: Int, shopCode: String?): List<CartReportProd>? {
        var prodPosition = 0
        return items?.map {
            prodPosition ++
            val goodsInfo = it.item
            CartReportProd(
                goodsInfo.name,
                shopCode,
                goodsInfo.shopName,
                goodsInfo.id.toLong(),
                shopPosition,
                prodGroupList,
                prodPosition
            )
        }
    }

    /**
     * 解析购物车数据
     */
    fun parseCartBean(cartBean: CartBean?): CartReportBean {
        CartReportDataUtil.mCartReportShopList = null
        CartReportDataUtil.mCartReportProdGroupList = null
        CartReportDataUtil.mCartReportProdMap = null
        CartReportDataUtil.mScmId = cartBean?.scmId
        val cartReport = parseCartBeanForReport(cartBean)
        cartReport.cartReportCartList?.flatMap {
            it.cartProdGroupList?.apply {
                it.mShopName = SpmUtil.checkReportSpmFormat(it.mShopName)
            }?: listOf()
        }?.apply {
            CartReportDataUtil.mCartReportProdGroupList = this
        }?.flatMap {
            it.cartProdGroupList?: listOf()
        }?.forEach {
            if (CartReportDataUtil.mCartReportProdMap == null) CartReportDataUtil.mCartReportProdMap = mutableMapOf()
            CartReportDataUtil.mCartReportProdMap!![it.getProductId()] = it
        }
        CartReportDataUtil.mCartData = cartReport
        CartReportDataUtil.mCartReportShopList = cartReport.cartReportCartList
        return cartReport
    }
}