package com.ybmmarketkotlin.viewmodel.changeCart

import android.os.*
import java.util.concurrent.ThreadPoolExecutor

// 加购
const val WHAT_CHANGE_CART_ADD = 0
// 选中商品
const val WHAT_CHANGE_CART_SELECT_ITEM = 1
// 选中商品组
const val WHAT_CHANGE_CART_SELECT_GROUP = 2
// 全选
const val WHAT_CHANGE_CART_SELECT_ALL = 3
// 删除商品
const val WHAT_CHANGE_CART_DELETE_ITEM = 4
//获取购物车数据
const val WHAT_CHANGE_CART_GET_CART_DATA = 100

/**
 * 处理消息
 */
class MessageUtils(val service: ThreadPoolExecutor) {

    /**
     * 发送消息
     */
    fun <K, V> sendMessage(runnable: ChangeCartManager.CartRunnable, what: Int, values: CartOperation<K, V>?, factory: Factory = ChangeCartMessageFactory(what, values)) {
        val msg = factory.onCreateMessage()
        runnable.msg = msg
        service.execute(runnable)
    }

    fun submitCartData(runnable: ChangeCartManager.GetCartDataRunnable) {
        try {
            service.execute(runnable)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 队列是否空闲
     */
    fun isEmpty(): Boolean = service.queue.isEmpty()

    /**
     * 清楚队列中所有的消息
     */
    fun removeAllMsg() {
        service.queue.clear()
    }
}

class ChangeCartMessageFactory<K, V>(val messageType: Int, val value: CartOperation<K, V>?): Factory {
    override fun onCreateMessage(): Message {
        val operation : CartOperation<K, V>? = value
        val msg = Message.obtain()
        msg.obj = operation
        msg.what = messageType
        return msg
//        return when(messageType) {
//            WHAT_CHANGE_CART_ADD -> {
//                val operation : CartOperation<K, V>? = value
//                val msg = Message.obtain()
//                msg.obj = operation
//                msg.what = messageType
//                msg
//            }
//            else -> Message.obtain()
//        }
    }

}

interface Factory {
    fun onCreateMessage(): Message
}

/**
 * 操作数据
 */
data class CartOperation<K, V>(
    var value: Map<K, V>?,
    var state: Boolean = false
) {
    override fun equals(other: Any?): Boolean {
        return super.equals(other)
    }
}