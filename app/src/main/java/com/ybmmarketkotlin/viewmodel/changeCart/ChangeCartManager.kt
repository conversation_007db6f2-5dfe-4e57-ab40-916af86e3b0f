package com.ybmmarketkotlin.viewmodel.changeCart

import android.content.Intent
import android.graphics.Typeface
import android.os.Message
import android.os.SystemClock
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import androidx.core.content.ContextCompat
import androidx.lifecycle.MutableLiveData
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.chad.library.adapter.base.entity.MultiItemEntity
import com.ybmmarket20.R
import com.ybmmarket20.bean.AbstractChangeCart
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CartDataBean
import com.ybmmarket20.bean.RangePriceBean
import com.ybmmarket20.bean.cart.CartBeanWraper
import com.ybmmarket20.bean.cart.CartCompanyBean
import com.ybmmarket20.bean.cart.CartItemBean
import com.ybmmarket20.bean.cart.CartShopList
import com.ybmmarket20.bean.cart.CartShoppingGroupFrontBean
import com.ybmmarket20.bean.cart.CartSortedNewBean
import com.ybmmarket20.bean.cart.Level0ItemInvalidBean
import com.ybmmarket20.bean.cart.Level0ItemShopFooterBean
import com.ybmmarket20.bean.cart.Level0ItemShopHeaderBean
import com.ybmmarket20.bean.cart.Level1InvalidGoodBean
import com.ybmmarket20.bean.cart.Level1InvalidGroupGoodBean
import com.ybmmarket20.bean.cart.Level1InvalidGroupHeaderBean
import com.ybmmarket20.bean.cart.Level1InvalidItemGoodsBeanAbs
import com.ybmmarket20.bean.cart.Level1ItemActivityGiftSelectBean
import com.ybmmarket20.bean.cart.Level1ItemActivityGoodBean
import com.ybmmarket20.bean.cart.Level1ItemActivityGoodEndBean
import com.ybmmarket20.bean.cart.Level1ItemActivityGoodGiftBean
import com.ybmmarket20.bean.cart.Level1ItemActivityHeaderBean
import com.ybmmarket20.bean.cart.Level1ItemCommonGoodsBean
import com.ybmmarket20.bean.cart.Level1ItemGoodsBeanAbs
import com.ybmmarket20.bean.cart.Level1ItemGroupFooterBean
import com.ybmmarket20.bean.cart.Level1ItemGroupGoodBean
import com.ybmmarket20.bean.cart.Level1ItemGroupHeaderBean
import com.ybmmarket20.bean.cart.Level1ItemSubShopHeaderBean
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.common.util.Abase
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.db.info.HandlerGoodsDao
import com.ybmmarket20.network.request.CartRequest
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.xyyreport.page.cart.CartReportDataUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import java.util.concurrent.LinkedBlockingDeque
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit

/**
 * 购物车
 */
class ChangeCartManager private constructor() {

    var msgUtils: MessageUtils
    var singleService: ThreadPoolExecutor =
        ThreadPoolExecutor(1, 1, 0, TimeUnit.MILLISECONDS, LinkedBlockingDeque())
    val uiLoadingLiveData = MutableLiveData<UiData>()
    val uiCartDataLiveData = MutableLiveData<CartBeanWraper>()

    init {
        msgUtils = MessageUtils(singleService)
    }

    companion object {
        private var manager: ChangeCartManager? = null
            get() {
                if (field == null) manager = ChangeCartManager()
                return field
            }

        fun getInstance(): ChangeCartManager = manager!!
    }

    /**
     * 发送空消息触发IdelHandler获取购物车数据
     */
    fun getCartData() {
        msgUtils.submitCartData(GetCartDataRunnable())
    }

    /**
     * 获取购物车数据
     */
    private fun getCartDataWork(merchantId: String) = runBlocking {
        val result = CartRequest().getCartData(merchantId)
        val cartBeanWraper = CartBeanWraper()
        cartBeanWraper.apply {
            result?.data?.let {
                CartDataReportParser.getInstance().parseCartBean(it)
                // 1. 构造一级店铺头部数据
                // 1.1 构造二级 自营店铺商品数据

                // 1.2 构造二级 活动头部
                // 1.3 构造二级 活动商品头部
                // 1.4 构造二级 活动商品中间
                // 1.5 构造二级 活动商品尾部

                // 1.6 构造二级 套餐商品头部
                // 1.7 构造二级 套餐商品
                // 1.8 构造二级 套餐尾部

                // 2. 构造一级店铺尾部数据
                // 3.构造一级失效头部数据
                it.company?.forEach { cartCompanyBean ->
                    val Level0ItemShopHeaderBean = Level0ItemShopHeaderBean().apply {
                        shopName = cartCompanyBean?.companyName
                        orgId = cartCompanyBean?.orgId
                        if (cartCompanyBean.isSelfCompany()) {
                            isThirdCompany = false
                            shopCode = cartCompanyBean?.mainShopCode

                            val ybmShop =
                                cartCompanyBean.shop?.filter { it.shopType == "ybm" }?.firstOrNull()
                            originalShopCode = ybmShop?.originalShopCode
                            isHaveVoucher = ybmShop?.isHaveVoucher == 1
                            if (isHaveVoucher) {
                                skuids = getShopSkuIds(ybmShop).toString()
                            }

                            showReturnVoucherInfo = ybmShop?.returnVoucherInfo?.isMatch == 1
                            returnVoucherTips = ybmShop?.returnVoucherInfo?.text
                            returnVoucherJumpUrl = ybmShop?.returnVoucherInfo?.action
                            returnVoucherUrlText = "去凑单"
                            shopDiscounts = if(ybmShop?.marketingTipsList?.isNotEmpty() == true) ybmShop.marketingTipsList.first() else null

                        } else {
                            isThirdCompany = true
                            val popShop = cartCompanyBean.shop.firstOrNull()
                            originalShopCode = popShop?.originalShopCode
                            shopCode = popShop?.shopCode
                            isHaveVoucher = popShop?.isHaveVoucher == 1
                            if (isHaveVoucher) {
                                skuids = getShopSkuIds(popShop).toString()
                            }

                            showReturnVoucherInfo = popShop?.returnVoucherInfo?.isMatch == 1
                            returnVoucherTips = popShop?.returnVoucherInfo?.text
                            returnVoucherJumpUrl = popShop?.returnVoucherInfo?.action
                            returnVoucherUrlText = "去凑单"
                            shopDiscounts = if(popShop?.marketingTipsList?.isNotEmpty() == true) popShop.marketingTipsList.first() else null
                        }
                        activityId = cartCompanyBean?.activityId
                        activityType = cartCompanyBean?.activityType

                        //
//                            var showReturnVoucherInfo :Boolean = false
//                            var returnVoucherTips :String? = null
//                            var returnVoucherJumpUrl :String? = null
//                            var returnVoucherUrlText :String? = null

                        selected = cartCompanyBean?.selectStatus == 1
                        shopJumpUrl = cartCompanyBean?.shopJumpUrl

                        showFreightIcon = cartCompanyBean?.freightIconShowStatus == 1
                        showFreightTips = cartCompanyBean?.freightTipsShowStatus == 1
                        freightTips = cartCompanyBean?.freightTips
                        freightJumpUrl = cartCompanyBean?.freightJumpUrl
                        freightUrlText = cartCompanyBean?.freightUrlText ?: ""

                        // 构造二级数据
                        addSubItemForCompany(cartCompanyBean, this)
                    }
                    // shopheader
                    this.cartEntityList.add(Level0ItemShopHeaderBean)
                    // shopfooter
                    this.cartEntityList.add(Level0ItemShopFooterBean().apply {
                        payAmount = cartCompanyBean.payAmount
                        productVarietyNum = cartCompanyBean.productVarietyNum
                        productTotalNum = cartCompanyBean.productTotalNum
                    })
                }
                it.novalidGroup?.let {
                    this.cartEntityList.add(Level0ItemInvalidBean().apply {
                        title = it.title
                        productTotalNum = it.productTotalNum
                    })
                    // 构造二级数据
                    addSubItemForInvalid(it, this.cartEntityList)
                }
            }
        }
        cartBeanWraper.apply {
            discountsStr =
                "促销减:¥${UiUtils.transform(result?.data?.rePrice?.toString())} 用券减:¥${
                    UiUtils.transform(
                        UiUtils.transform(result?.data?.voucherDiscountAmount?.toString())
                    )
                }"
            totalAmount = "总计：¥${UiUtils.transform(result?.data?.payAmount?.toString())}"
            isSelected = result?.data?.selectStatus == 1
            commitText =
                if (result?.data?.hasVouchersNotReceived == 1) CartBeanWraper.CLOSE_AN_ACCOUNT_COUPON else CartBeanWraper.CLOSE_AN_ACCOUNT
            selectNum = result?.data?.selectNum ?: 0
            varietyNum = result?.data?.varietyNum ?: 0
            promoAmountDto = result?.data?.promoAmountDto
            crossStoreVoucherDto = result?.data?.crossStoreVoucherDto
            canSettle = result?.data?.canSettle ?: 0
            unsatisfiedStartPriceList = result?.data?.unsatisfiedStartPriceList
            unsatisfiedFreeShippingList = result?.data?.unsatisfiedFreeShippingList
            specialTipsShow = result?.data?.specialProductTipsShowStatus == 1
            bizSource = result?.data?.bizSource?:0
            needToBePerfectedActList = result?.data?.needToBePerfectedActList?: arrayListOf()
            shopInfoSxpList = result?.data?.shopInfoSxpList ?: arrayListOf()
        }
        LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(
            Intent(
                IntentCanst.ACTION_SHOPNUMBER
            )
        )
        if (msgUtils?.isEmpty() == true) {
            withContext(Dispatchers.Main) {
                uiCartDataLiveData.value = cartBeanWraper
            }
        }
    }

    private fun addSubItemForInvalid(
        it: CartShoppingGroupFrontBean,
        level0ItemInvalidBean: MutableList<MultiItemEntity>
    ) {
        it.sorted?.forEach {

            val headItem = it.item
            if (it.itemType == 3) {
                // 添加失效套餐头部
                level0ItemInvalidBean.add(Level1InvalidGroupHeaderBean().apply {
                    price = "${headItem?.name}: ¥${headItem?.price}"
                    origPrice = "原价: ¥${headItem?.origPrice}"
                    packageId = "${headItem?.packageId ?: ""}"
                })
                it.subItemList?.forEach { cattItemBean ->
                    // 添加失效套餐商品
                    level0ItemInvalidBean.add(Level1InvalidGroupGoodBean("${cattItemBean.packageId}").apply {
                        generateInvalidGoodsData(cattItemBean, true)
                    })
                }
            } else {
                // 添加失效单个商品
                level0ItemInvalidBean.add(Level1InvalidGoodBean().apply {
                    generateInvalidGoodsData(headItem)
                })
            }

        }

    }

    private fun getShopSkuIds(ybmShop: CartShopList?): StringBuilder {
        val skuidsStringBuilder = StringBuilder()
        ybmShop?.shoppingGroupFrontDtos?.forEach {
            it?.sorted?.forEach {
                if (it.itemType == 3) {
                    it.subItemList.forEach {
                        skuidsStringBuilder.append(it.skuId).append(",")
                    }
                } else {
                    skuidsStringBuilder.append(it.item.skuId).append(",")
                }
            }
        }
        skuidsStringBuilder.takeIf { it.endsWith(",") }?.apply { deleteCharAt(this.lastIndex) }
        return skuidsStringBuilder
    }

    private fun Level1InvalidItemGoodsBeanAbs.generateInvalidGoodsData(
        itemBean: CartItemBean,
        isInvalidGroup: Boolean = false
    ) {
        name = SpannableStringBuilder("${itemBean.name} / ${itemBean.spec}").apply {
            itemBean.spec?.let {
                setSpan(
                    AbsoluteSizeSpan(12, true),
                    this.length - it.length,
                    this.length,
                    Spanned.SPAN_INCLUSIVE_EXCLUSIVE
                )
            }
        }
        imageUrl = itemBean.imageUrl
        markerUrl = itemBean.markerUrl
        groupGoodsNum = if (isInvalidGroup) "X${itemBean.amount}" else ""
        skuid = "${itemBean?.skuId}"

        invalidStatus = itemBean.stockTitle ?: "失效"
        invalidContent = itemBean.loseTagText
//        invalidContent = when (itemBean.skuStatus) {
//            2 -> "该商品已售罄"
//            4 -> "该商品已下架"
//            91 -> "暂无购买权限"
//            92 -> "该商品已限购"
//            95 -> "该商品超出经营范围"
//            105 -> "价格签署协议可见"
//            else -> ""
//        }

    }

    private fun addSubItemForCompany(
        originData: CartCompanyBean?,
        level0Item: Level0ItemShopHeaderBean
    ) {
        // 提取自营店铺的商品出来
        originData?.shop?.filter { it.shopType == "ybm" }?.firstOrNull()?.let { cartshoppingBean ->
            cartshoppingBean.shoppingGroupFrontDtos?.forEach {
                it.shopCode = cartshoppingBean.shopCode
                addSubShopItem(it, level0Item)
            }
        }
        originData?.shop?.filter { it.shopType != "ybm" }?.forEach { cartshoppingBean ->
            val level1ItemSubShopHeaderBean = Level1ItemSubShopHeaderBean().apply {
                companyName = cartshoppingBean.shopName
                shopCode = cartshoppingBean.shopCode
                shopJumpUrl =
                    if (cartshoppingBean?.appLinkUrl?.startsWith("ybmpage") == false) "ybmpage://commonh5activity?url=${cartshoppingBean?.appLinkUrl}" else cartshoppingBean?.appLinkUrl
                isHaveVoucher = cartshoppingBean.isHaveVoucher == 1
                selected = cartshoppingBean.selectStatus == 1

                if (isHaveVoucher) {
                    val skuidsStringBuilder = StringBuilder()
                    cartshoppingBean.shoppingGroupFrontDtos.forEach {

                        it?.shopCode = cartshoppingBean.shopCode
                        it?.sorted?.forEach {
                            if (it.itemType == 3) {
                                it.subItemList.forEach {
                                    skuidsStringBuilder.append(it.skuId).append(",")
                                }
                            } else {
                                skuidsStringBuilder.append(it.item.skuId).append(",")
                            }
                        }
                    }
                    skuidsStringBuilder.takeIf { it.endsWith(",") }
                        ?.apply { deleteCharAt(this.lastIndex) }
                    skuids = skuidsStringBuilder.toString()
                }


            }
            // 添加自然人店铺头
            if (originData.isSelfCompany()) level0Item.addSubItem(level1ItemSubShopHeaderBean)
            // 提取自然人或pop店铺商品出来
            cartshoppingBean.shoppingGroupFrontDtos.forEach {
                addSubShopItem(it, level0Item, originData.isSelfCompany())
            }

        }


    }

    private fun addSubShopItem(
        cartShoppingGroupFrontBean: CartShoppingGroupFrontBean,
        level0Item: Level0ItemShopHeaderBean,
        isPersonalShop: Boolean = false
    ) {
        if (!TextUtils.isEmpty(cartShoppingGroupFrontBean.title)) {
            // 提取活动商品出来
            // 组合活动添加活动头、单品活动添加单品
//             todo 把这里得条件换掉
            when (cartShoppingGroupFrontBean.combinationType) {
//            when (cartShoppingGroupFrontBean.sorted?.size) {
                1 -> extractCommonBeanWithActivy(level0Item, cartShoppingGroupFrontBean)
                else -> extractActivityBean(level0Item, cartShoppingGroupFrontBean)
            }

        } else {
            // 提取非活动商品出来
            cartShoppingGroupFrontBean?.sorted?.forEach {
                // 非活动的单品或者套餐
                when (it.itemType) {
                    3 -> {
                        //  套餐商品
                        // 添加套餐头
                        // 添加套餐商品
                        // 添加套餐尾
                        extractGroupBean(level0Item, cartShoppingGroupFrontBean)
                    }
                    else -> {
                        // 非活动单品
                        createGoodsBean(it, level0Item, false, false, isPersonalShop, combinationType = cartShoppingGroupFrontBean.combinationType,cartShoppingGroupFrontBean)
                    }
                }
            }

            //没有赠品时 但是可以选 这个时候要在最后加上
            determineNeedGiftSelectInEnd(level0Item, cartShoppingGroupFrontBean)
        }
    }


    /**
     * 提取可购套餐数据
     */
    private fun extractGroupBean(
        level0Item: Level0ItemShopHeaderBean,
        cartShoppingGroupFrontBean: CartShoppingGroupFrontBean
    ) {
        // 套餐商品
        // 添加套餐头
        val headItem = cartShoppingGroupFrontBean.sorted?.firstOrNull()?.item

        level0Item.addSubItem(Level1ItemGroupHeaderBean().apply {
            selected = headItem?.status == 1
            price = "${headItem?.name}: ¥${UiUtils.transform(headItem?.price?.toString())}"
            origPrice = "原价: ¥${UiUtils.transform(headItem?.origPrice?.toString())}"
            packageId = "${headItem?.packageId ?: ""}"
            realPrice = headItem?.price?.toFloat() ?: 0.00f
        })
        //套餐品中是否包含阶梯价品
        var rangePriceBeanTemp: RangePriceBean? = null
        // 添加套餐商品
        cartShoppingGroupFrontBean.sorted?.firstOrNull()?.subItemList?.forEach {
            if (it.rangePriceBean != null) rangePriceBeanTemp = it.rangePriceBean
            level0Item.addSubItem(Level1ItemGroupGoodBean().apply {
                generateBaseGoodsData(this, it)
                packageProductQty = it.packageProductQty
                packageId = "${it.packageId}"
            })
        }
        // 添加套餐尾
        level0Item.addSubItem(Level1ItemGroupFooterBean().apply {
            subtotal = "小计 ¥${UiUtils.transform(headItem?.realPayAmount?.toString())}"
            amount = "${headItem?.amount ?: 0}"
            packageId = headItem?.packageId?.toString()
            canSplit = headItem?.isSplit == 1
            mediumPackageNum = headItem?.mediumPackageNum ?: 1
            realPrice = headItem?.price?.toFloat() ?: 0.00f
            rangePriceBean = rangePriceBeanTemp
        })
    }

    /**
     * 提取单品促销数据
     */
    private fun extractCommonBeanWithActivy(
        level0Item: Level0ItemShopHeaderBean,
        cartShoppingGroupFrontBean: CartShoppingGroupFrontBean
    ) {
        val commonGoodsBean = Level1ItemCommonGoodsBean().apply {
            generateBaseGoodsData(this, cartShoppingGroupFrontBean.sorted?.firstOrNull()?.item)
            activityBean = Level1ItemActivityHeaderBean().apply {
                title = cartShoppingGroupFrontBean.title
                titleUrl = cartShoppingGroupFrontBean.titleUrl
                titleUrlText = cartShoppingGroupFrontBean.titleUrlText
                type = cartShoppingGroupFrontBean.type
                combinationType = cartShoppingGroupFrontBean.combinationType
                activityTypeText = cartShoppingGroupFrontBean.activityTypeText
            }
        }
        
        level0Item.addSubItem(commonGoodsBean)
    }

    private fun generateBaseGoodsData(
        baseGoods: Level1ItemGoodsBeanAbs,
        originData: CartItemBean?
    ) {
        baseGoods.apply {
            rangePriceBean = originData?.rangePriceBean
            gift = originData?.isGift?: false
            skuid = originData?.skuId?.toString()
            imageUrl = originData?.imageUrl
            markerUrl = originData?.markerUrl
            val nameStringBuilder = SpannableStringBuilder("${originData?.name}")
            spec = originData?.spec
            unitPrice = originData?.price ?: 0.0
            spec?.let {
                nameStringBuilder.append("/")
                nameStringBuilder.append(spec)
                nameStringBuilder.setSpan(
                    AbsoluteSizeSpan(12, true),
                    nameStringBuilder.length - it.length - 1,
                    nameStringBuilder.length,
                    Spannable.SPAN_INCLUSIVE_INCLUSIVE
                )
                nameStringBuilder.setSpan(
                    ForegroundColorSpan(
                        ContextCompat.getColor(
                            YBMAppLike.getAppContext(),
                            R.color.color_9494A6
                        )
                    ),
                    nameStringBuilder.length - it.length - 1,
                    nameStringBuilder.length,
                    Spannable.SPAN_INCLUSIVE_INCLUSIVE
                )
                nameStringBuilder.setSpan(
                    StyleSpan(Typeface.NORMAL),
                    nameStringBuilder.length - it.length - 1,
                    nameStringBuilder.length,
                    Spannable.SPAN_INCLUSIVE_INCLUSIVE
                )
            }
            name = nameStringBuilder
            if (!originData?.stockTitle.isNullOrEmpty() && originData?.valid == 1) {
                invalidStatus = originData?.stockTitle
            } else {
                invalidStatus = null
            }
            val priceStringBuilder = if (originData?.limitedTimeSupplement?.costPrice != null){
                //限时加补用限时加补的价格
                SpannableStringBuilder("¥${UiUtils.transform(originData?.limitedTimeSupplement?.costPrice ?: 0.00)}")
            }else{
                SpannableStringBuilder("¥${UiUtils.transform(originData?.price ?: 0.00)}")
            }
            originData?.sku?.productUnit?.let {
                priceStringBuilder.append("/")
                priceStringBuilder.append(originData?.sku?.productUnit)
                priceStringBuilder.setSpan(
                    AbsoluteSizeSpan(10, true),
                    priceStringBuilder.length - it.length - 1,
                    priceStringBuilder.length,
                    Spannable.SPAN_INCLUSIVE_INCLUSIVE
                )
                priceStringBuilder.setSpan(
                    ForegroundColorSpan(
                        ContextCompat.getColor(
                            YBMAppLike.getAppContext(),
                            R.color.color_9494A6
                        )
                    ),
                    priceStringBuilder.length - it.length - 1,
                    priceStringBuilder.length,
                    Spannable.SPAN_INCLUSIVE_INCLUSIVE
                )
                priceStringBuilder.setSpan(
                    StyleSpan(Typeface.NORMAL),
                    priceStringBuilder.length - it.length - 1,
                    priceStringBuilder.length,
                    Spannable.SPAN_INCLUSIVE_INCLUSIVE
                )
            }
            price = priceStringBuilder

            effect = originData?.nearEffect
            tagList = originData?.tagList
            if (UiUtils.transform(originData?.showPriceAfterDiscount ?: "0.00").toDouble() > 0) {
                showPriceAfterDiscount =
                    "折后约¥${UiUtils.transform(originData?.showPriceAfterDiscount)}"
            }
            dataTagList = originData?.dataTagList
            subtotal = if (originData?.limitedTimeSupplement?.subTotal != null){
                    //限时补价
                        SpannableStringBuilder("小计 ¥${UiUtils.transform(originData?.limitedTimeSupplement?.subTotal?.toString() ?: "")}")
                    }else{
                        SpannableStringBuilder("小计 ¥${UiUtils.transform(originData?.realPayAmount?.toString() ?: "")}")
                    }
            amount = "${originData?.amount ?: 0}"
            var showpurchaseLimitStr =
                originData?.getPromoQty() ?: 0 > 0 && originData?.getNormalQty() ?: 0 > 0 && originData?.getSku() != null
            purchaseLimitStr =
                if (showpurchaseLimitStr) "此商品为限购商品，超出${originData?.promoQty}${originData?.sku?.productUnit}的部分将按原价购买" else null
            originData?.showPriceAfterDiscount?.takeIf { showpurchaseLimitStr }?.let {
                purchaseLimitStr =
                    "此商品为限购商品，超出${originData?.promoQty}${originData?.sku?.productUnit}的部分将按照原价购买，超出部分也参与折后价计算"
            }
            actPurchaseTip = originData?.actPurchaseTip
            seckillRemainingTime = originData?.seckillRemainingTime ?: 0
            responseLocalTime = SystemClock.elapsedRealtime()
            selected = originData?.status == 1
            mediumPackageNum = originData?.mediumPackageNum ?: 1
            canSplit = originData?.isSplit == 1
            tagList = originData?.tagList
            tagWholeOrderList = originData?.tagWholeOrderList
            tagTitle = originData?.tagTitle
            specialtyCouponTag = originData?.specialtyCouponTag
            dataTagList = originData?.dataTagList
            limitedTimeSupplement = originData?.limitedTimeSupplement?.apply { responseLocalTime = System.currentTimeMillis() }
        }
    }

    /**
     *  提取组合促销头数据、促销中间商品数据、促销尾部数据
     */
    private fun extractActivityBean(
        level0Item: Level0ItemShopHeaderBean,
        cartShoppingGroupFrontBean: CartShoppingGroupFrontBean
    ) {
        level0Item.addSubItem(Level1ItemActivityHeaderBean().apply {
            title = cartShoppingGroupFrontBean.title
            titleUrl = cartShoppingGroupFrontBean.titleUrl
            titleUrlText = cartShoppingGroupFrontBean.titleUrlText
            type = cartShoppingGroupFrontBean.type
            combinationType = cartShoppingGroupFrontBean.combinationType
            activityTypeText = cartShoppingGroupFrontBean.activityTypeText
            shopCode = cartShoppingGroupFrontBean.shopCode
            activityId = cartShoppingGroupFrontBean.activityId ?: ""
            activityType = cartShoppingGroupFrontBean.activityType ?: ""
        })

        // 添加活动非最后一个商品
        cartShoppingGroupFrontBean?.sorted?.dropLast(1)?.forEach {
            createGoodsBean(it, level0Item, true, combinationType = cartShoppingGroupFrontBean.combinationType, cartShoppingGroupFrontBean = cartShoppingGroupFrontBean)
        }
        // 添加活动最后一个商品
        cartShoppingGroupFrontBean?.sorted?.lastOrNull()?.let {
            createGoodsBean(it, level0Item, isActivity = true, isLast = true, combinationType = cartShoppingGroupFrontBean.combinationType, cartShoppingGroupFrontBean = cartShoppingGroupFrontBean)
        }

        //没有赠品时 但是可以选 这个时候要在最后加上
        determineNeedGiftSelectInEnd(level0Item, cartShoppingGroupFrontBean)
    }

    //没有赠品时 但是可以选 这个时候要在最后加上
    private fun determineNeedGiftSelectInEnd(level0Item: Level0ItemShopHeaderBean, cartShoppingGroupFrontBean: CartShoppingGroupFrontBean) {
        if (level0Item.subItems.last() is Level1ItemGoodsBeanAbs && level0Item.subItems.last() !is Level1ItemActivityGoodGiftBean && cartShoppingGroupFrontBean.isCanGoToGiftPool) {// 有选择赠品样式且之前没加过
            val content = if (cartShoppingGroupFrontBean.isGiveUpGift) {
                "已放弃赠品"
            } else {
                if (cartShoppingGroupFrontBean.giftPoolActHasSelectedNum > 0) {
                    "已选赠品"
                } else {
                    "选择赠品"
                }
            }

            level0Item.addSubItem(Level1ItemActivityGiftSelectBean().apply {
                this.content = content
                this.cartShoppingGroupFrontBean = cartShoppingGroupFrontBean
            })
        }
    }

    private fun createGoodsBean(
            it: CartSortedNewBean,
            level0Item: Level0ItemShopHeaderBean,
            isActivity: Boolean = false,
            isLast: Boolean = false,
            isPersonalShop: Boolean = false,
            combinationType: Int = 1,
            cartShoppingGroupFrontBean: CartShoppingGroupFrontBean
    ) {
        if(isActivity && it.item.isGift) {
            if (level0Item.subItems.last() !is Level1ItemActivityGoodGiftBean &&  cartShoppingGroupFrontBean.isCanGoToGiftPool){// 有选择赠品样式且之前没加过
                val content = if (cartShoppingGroupFrontBean.isGiveUpGift){
                    "已放弃赠品"
                }else{
                    if (cartShoppingGroupFrontBean.giftPoolActHasSelectedNum > 0){
                        "已选赠品"
                    }else{
                        "选择赠品"
                    }
                }

                level0Item.addSubItem(Level1ItemActivityGiftSelectBean().apply {
                    this.content = content
                    this.cartShoppingGroupFrontBean = cartShoppingGroupFrontBean
                })
            }

            level0Item.addSubItem(Level1ItemActivityGoodGiftBean().apply {
                gift = true
                generateBaseGoodsData(this, it.item)
                productUnit = it.item.sku.productUnit
                priceDesc = it.item.priceDesc
                priceNum = it.item.price
                level0Item.activityId
            })
        }
        else if (isActivity && isLast) level0Item.addSubItem(Level1ItemActivityGoodEndBean().apply {
            generateBaseGoodsData(this, it.item)
        })
        else if (isActivity && !isLast) level0Item.addSubItem(Level1ItemActivityGoodBean().apply {
            generateBaseGoodsData(this, it.item)
            this.combinationType = combinationType
        })
        else level0Item.addSubItem(Level1ItemCommonGoodsBean().apply {
            this.isPersonalShop = isPersonalShop
            generateBaseGoodsData(this, it.item)
            activityBean = Level1ItemActivityHeaderBean().apply {
                title = it.item.title
                titleUrl = it.item.titleUrl
                titleUrlText = it.item.titleUrlText
                type = it.item.type
                this.combinationType = it.item.combinationType
                activityTypeText = it.item.activityTypeText
            }
        })
    }

    /**
     * 发送加购消息
     */
    fun changeCartData(paramsMap: Map<String, String>) {
        uiLoadingLiveData.value =
            UiData(WHAT_CHANGE_CART_ADD, paramsMap["skuId"] ?: paramsMap["packageId"])
        val cartOption = CartOperation(paramsMap)
        msgUtils.sendMessage(CartRunnable(null), WHAT_CHANGE_CART_ADD, cartOption)
    }

    /**
     * 加购
     */
    fun changeCartDataWork(paramsMap: Map<String, String>) = runBlocking {
        val p = CartReportDataUtil.addQtDataToParams(Abase.getCurrentActivity(), paramsMap)
        val result: BaseBean<CartDataBean>? = CartRequest().changeCart(p?: mapOf())
        if (result?.isSuccess == true) {
            val skuid = paramsMap["skuId"]
            val packageId = paramsMap["packageId"]
            if (!skuid.isNullOrEmpty()) {
                HandlerGoodsDao.getInstance().updateItem(skuid.toLong(), result.data.qty, false)
            }
            if (!packageId.isNullOrEmpty()) {
                HandlerGoodsDao.getInstance().updateItem(packageId.toLong(), result.data.qty, true)
            }
        }
        result
    }

    /**
     * 发送修改店铺和全选的选中状态消息
     */
    fun changeSelectStatusAll(
        paramsMap: Map<String, String>,
        check: Boolean,
        isShop: Boolean,
        isSubShop: Boolean
    ) {
        uiLoadingLiveData.value = UiData(
            if (isShop) WHAT_CHANGE_CART_SELECT_GROUP else WHAT_CHANGE_CART_SELECT_ALL,
            if (isShop) paramsMap["orgId"] else null,
            check,
            isSubShop
        )
        val cartOption = CartOperation(paramsMap, check)
        msgUtils.sendMessage(CartRunnable(null),
            if (isShop) WHAT_CHANGE_CART_SELECT_GROUP else WHAT_CHANGE_CART_SELECT_ALL,
            cartOption
        )
    }

    /**
     * 修改店铺的选中状态
     */
    private fun changeSelectStatusAllWork(paramMap: Map<String, String>, check: Boolean) =
        runBlocking {
            return@runBlocking if (check) {
                CartRequest().selectAllShopItem(paramMap)
            } else {
                CartRequest().cancelAllShopItem(paramMap)
            }
        }

    /**
     * 修改商品的选中状态
     */
    fun changeGoodsSelectStatus(paramsMap: Map<String, String>, check: Boolean, isGroup: Boolean) {
        uiLoadingLiveData.value = UiData(
            WHAT_CHANGE_CART_SELECT_ITEM,
            paramsMap[if (isGroup) "packageId" else "skuId"],
            check
        )
        val cartOption = CartOperation(paramsMap, check)
        msgUtils.sendMessage(CartRunnable(null), WHAT_CHANGE_CART_SELECT_ITEM, cartOption)
    }

    /**
     * 修改商品的选中状态
     */
    private fun changeGoodsSelectStatusWork(paramMap: Map<String, String>, check: Boolean) =
        runBlocking {
            return@runBlocking if (check) {
                CartRequest().selectItem(paramMap)
            } else {
                CartRequest().cancelItem(paramMap)
            }
        }

    /**
     * 请求逻辑
     */
    inner class CartRunnable(var msg: Message?) : Runnable {
        override fun run() {
            var result: BaseBean<out AbstractChangeCart>? = null
            try {
                result = when (msg?.what) {
                    //加购
                    WHAT_CHANGE_CART_ADD -> {
                        (msg?.obj as CartOperation<String, String>).value?.let { changeCartDataWork(it) }
                    }

                    //全选、选择店铺
                    WHAT_CHANGE_CART_SELECT_ALL,
                    WHAT_CHANGE_CART_SELECT_GROUP -> {
                        val cartOperation = msg?.obj as CartOperation<String, String>
                        cartOperation.value?.let { changeSelectStatusAllWork(it, cartOperation.state) }
                    }

                    //单选
                    WHAT_CHANGE_CART_SELECT_ITEM -> {
                        val cartOperation = msg?.obj as CartOperation<String, String>
                        cartOperation.value?.let {
                            changeGoodsSelectStatusWork(
                                it,
                                cartOperation.state
                            )
                        }
                    }
                    else -> BaseBean()
                }
            } catch (e: Exception) {
                e.printStackTrace()
                msgUtils.removeAllMsg()
            }

            if (result?.data == null) {
                // 选中不会返回data
            } else if (result.data?.businessState != 200) {
                //网络请求失败（如：售罄）
                msgUtils.removeAllMsg()
            }
            if (msgUtils.isEmpty()) {
                msgUtils.submitCartData(GetCartDataRunnable())
            }
        }
    }

    /**
     * 获取购物车数据
     */
    inner class GetCartDataRunnable: Runnable {
        override fun run() {
            getCartDataWork(SpUtil.getMerchantid())
        }
    }

}

data class UiData(
    val msgType: Int, //消息类型（what）
    val token: String?, //id
    var status: Boolean = false, //选中状态
    var isSubShop: Boolean = false //是否是子店铺
)
