package com.ybmmarketkotlin.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.EmptyBean
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.network.ResponseIntercepter
import kotlinx.coroutines.launch

class UnregisterViewModel : ViewModel() {
    val unregisterStatus: MutableLiveData<Boolean> by lazy { MutableLiveData<Boolean>() }
    val commitBean: MutableLiveData<Boolean> by lazy { MutableLiveData<Boolean>() }
    val cancelDropAccountLiveData: MutableLiveData<Boolean> by lazy { MutableLiveData<Boolean>() }


    fun findUnregisterStatus(merchantId: String) {
        viewModelScope.launch {
            val bean = NetworkService.instance.findCancelAnAccountStatus(merchantId)
            bean?.data?.let {
                unregisterStatus.postValue(it)
            }
        }
    }


    fun commitUnregister(merchantId: String) {
        viewModelScope.launch {
            val bean = NetworkService.instance.cancelAnAccount(merchantId)
            if (bean.isSuccess) {
                commitBean.postValue(true)
            }
        }
    }
    fun cancelDropAccount(merchantId: String) {
        viewModelScope.launch {
            val bean = NetworkService.instance.cancelDropAccount(merchantId)
            if (bean.isSuccess) {
                ToastUtils.showShort("已撤销")
                cancelDropAccountLiveData.postValue( true)
            }
        }
    }
}