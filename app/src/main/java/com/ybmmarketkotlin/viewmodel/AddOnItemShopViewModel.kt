package com.ybmmarketkotlin.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CouponInfoBean
import com.ybmmarket20.bean.VoucherListBean
import com.ybmmarket20.network.NetworkService
import com.ybmmarketkotlin.bean.ShopListBean
import com.ybmmarketkotlin.bean.VoucherShopListTabBean
import kotlinx.coroutines.launch

class AddOnItemShopViewModel : ViewModel() {
    val couponInfoBeanLiveData = MutableLiveData<CouponInfoBean>()
    val voucherShopListTabBeanLiveData = MutableLiveData<VoucherShopListTabBean>()
    val shopListBeanLiveData = MutableLiveData<ShopListBean>()
    val cartVoucherBeanLiveData = MutableLiveData<VoucherListBean>()

    private val _voucherShopListTabBeanLiveData = MutableLiveData<BaseBean<VoucherShopListTabBean>>()
    val voucherShopListTabBeanLiveData2: LiveData<BaseBean<VoucherShopListTabBean>> = _voucherShopListTabBeanLiveData

    fun getCouponTemplate(merchantId: String, templateIds: String): Unit {
        viewModelScope.launch {
            val couponTemplate = NetworkService.instance.getCouponTemplate(merchantId, templateIds)
            couponTemplate?.data?.takeIf { it.size > 0 }?.get(0)?.let {
                couponInfoBeanLiveData.postValue(it)
            }
        }
    }

    fun getVoucherShopListTab(merchantId: String, templateIds: String) {
        viewModelScope.launch {
            val voucherShopListTabBean = NetworkService.instance.getVoucherShopListTab(merchantId, templateIds)
            voucherShopListTabBean?.data?.let {
                voucherShopListTabBeanLiveData.postValue(it)
            }
            _voucherShopListTabBeanLiveData.postValue(voucherShopListTabBean)
        }
    }

    fun getVoucherShopListBean(params: Map<String, String>, isFirst: Boolean) {
        viewModelScope.launch {
            val bean = NetworkService.instance.getVoucherShopListBean(params)
            bean?.data?.let {
                shopListBeanLiveData.postValue(it.apply { it.isFirst = isFirst })
            }
        }
    }

    fun getCartVoucherBean(merchantId: String, templateIds: String) {
        viewModelScope.launch {
            val bean = NetworkService.instance.getCartVoucherBean(merchantId, templateIds)
            bean?.data?.list?.takeIf { it.size > 0 }?.get(0)?.let {
                cartVoucherBeanLiveData.postValue(it)
            }
        }
    }
}