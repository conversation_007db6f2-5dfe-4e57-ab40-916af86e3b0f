package com.ybmmarketkotlin.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.network.request.PayNoticeRequest
import com.ybmmarketkotlin.bean.ApplyNoticeBean
import kotlinx.coroutines.launch

class PayNoticeViewModel : ViewModel() {

    private val _payNoticeBeanLiveData: MutableLiveData<BaseBean<List<ApplyNoticeBean>>> = MutableLiveData<BaseBean<List<ApplyNoticeBean>>>()
    val payNoticeBeanLiveData: LiveData<BaseBean<List<ApplyNoticeBean>>> = _payNoticeBeanLiveData

    fun getPayNotice(merchantId: String) {
        viewModelScope.launch {
            val result = PayNoticeRequest().getPayNotice(merchantId)
            _payNoticeBeanLiveData.postValue(result)
        }
    }

    /**
     * 删除指定订单
     */
    fun removeOrder(orderId: String, status: Int) {
        val payNoticeBaseBean = _payNoticeBeanLiveData.value as BaseBean<List<ApplyNoticeBean>>
        if (!payNoticeBaseBean.isSuccess) return
        val list = payNoticeBaseBean.data
        val resultList = list.filterNot { it.status == status && it.orderId == orderId }
        _payNoticeBeanLiveData.postValue(BaseBean.newSuccessBaseBean(resultList))
    }


}