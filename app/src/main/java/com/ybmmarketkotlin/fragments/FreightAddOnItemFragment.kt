package com.ybmmarketkotlin.fragments

import com.google.gson.reflect.TypeToken
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.bean.NetError
import com.ybm.app.utils.UiUtils.getColor
import com.ybmmarket20.R
import com.ybmmarket20.adapter.GoodsListAdapter
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.FlowDataAnalysisSId
import com.ybmmarket20.bean.FreightAddOnItemListBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RefreshFragment
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.repertory.getSId
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.addAnalysisRequestParams
import com.ybmmarket20.utils.analysis.flowDataPageCommoditySearch
import com.ybmmarket20.view.MyDecoration
import kotlinx.android.synthetic.main.fragment_freight_add_on_item.*
import java.lang.reflect.Type

/**
 * 凑单包邮
 */
class FreightAddOnItemFragment : RefreshFragment<RowsBean, FreightAddOnItemListBean<RowsBean>>() {
    var adapter: GoodsListAdapter?=null
    private var minPrice:String?="-1"
    private var maxPrice:String?="-1"
    private var isLoadData = false
    private val sptype:String="13"
    private var spid:String=""
    private var sid:String?="-1"
    override fun initTitle() {
        createSid()
        loadData()
        rg_price_range.setOnCheckedChangeListener { group, checkedId ->
            when(checkedId){
                R.id.class_a->{//0-5
                    minPrice="0"
                    maxPrice="5"
                    spid="1-0-5元"
                    onRefresh()
                }
                R.id.class_b->{//5-10
                    minPrice="5"
                    maxPrice="10"
                    spid="2-5-10元"
                    onRefresh()
                }
                R.id.class_c->{//10-15
                    minPrice="10"
                    maxPrice="15"
                    spid="3-10-15元"
                    onRefresh()
                }
                R.id.class_d->{//15-30
                    minPrice="15"
                    maxPrice="30"
                    spid="4-15-30元"
                    onRefresh()
                }
                R.id.class_e->{//30-50
                    minPrice="30"
                    maxPrice="50"
                    spid="5-30-50元"
                    onRefresh()
                }
                R.id.class_f->{//50以上
                    minPrice="50"
                    maxPrice="-1"
                    spid="6-50元以上"
                    onRefresh()
                }
            }
        }
        //设置分割线颜色margin
        recyclerView.addItemDecoration(MyDecoration().setMargin(ConvertUtils.dp2px(5f).toFloat()).setColor(getColor(R.color.colors_f5f5f5)))
    }

    override fun getRequestParams(): RequestParams {
        return RequestParams().apply {
            put("merchantId", SpUtil.getMerchantid())
            if (!(minPrice=="-1"&&maxPrice=="-1")){
                put("minPrice", minPrice)
                put("maxPrice", maxPrice)
            }
            if (isLoadData) addAnalysisRequestParams(this, mFlowData)
        }
    }

    override fun onResponseSuccess(content: String?, obj: BaseBean<FreightAddOnItemListBean<RowsBean>>?, t: FreightAddOnItemListBean<RowsBean>?) {
        super.onResponseSuccess(content, obj, t)
        t?.let {
            if(!isLoadData) {
                isLoadData = true
                try {
                    mFlowData.sId=sid
                    mFlowData.spId=spid
                    mFlowData.spType=sptype
                    flowDataPageCommoditySearch(mFlowData)
                    ((getAdapter(rows) as GoodsListAdapter)).setFlowData(mFlowData)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }
    private fun createSid(){
        getSId(object : BaseResponse<FlowDataAnalysisSId>() {
            override fun onSuccess(content: String, obj: BaseBean<FlowDataAnalysisSId?>, flowDataAnalysisSId: FlowDataAnalysisSId?) {
                super.onSuccess(content, obj, flowDataAnalysisSId)
                sid=flowDataAnalysisSId?.sid
            }

            override fun onFailure(error: NetError) {
                super.onFailure(error)
            }
        })
    }
    override fun getUrl(): String {
        return AppNetConfig.FREIGHT_ADD_ON_ITEM_GOODS_LIST
    }

    override fun getAdapter(rows: MutableList<RowsBean>?): YBMBaseAdapter<RowsBean> {
        if (adapter == null) {
            adapter = GoodsListAdapter(R.layout.item_goods,rows,false,false)
        }
        adapter?.setOnListItemClickListener {
            RoutersUtils.open("ybmpage://productdetail?" + IntentCanst.PRODUCTID + "=" + it.id)
        }
        return adapter as GoodsListAdapter
    }

    override fun getType(): Type {
        return object : TypeToken<BaseBean<FreightAddOnItemListBean<RowsBean>>>() {}.type
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_freight_add_on_item
    }

    override fun getEmptyImg(): Int = R.drawable.icon_empty

    override fun getEmptyMsg(): String = getString(R.string.no_data)

    override fun getStartPage(): Int {
        return super.getStartPage()
    }

//    override fun getTitle(): String {
//        return when (arguments?.getInt("freightAddOnItem")) {
//            FREIGHT_ADD_ON_ITEM_ALL -> "全部"
//            FREIGHT_ADD_ON_ITEM_SALE -> "仅促销"
//            else -> "仅包邮"
//        }
//    }
}