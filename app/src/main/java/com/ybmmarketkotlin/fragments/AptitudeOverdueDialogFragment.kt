package com.ybmmarketkotlin.fragments

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.AptitudeDetailListBean
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.view.DividerLine
import kotlinx.android.synthetic.main.layout_fragment_dialog_aptitude_overdue.view.*
import java.lang.reflect.Type


/**
 * <AUTHOR> Brin
 * @date : 2021/1/20 - 18:09
 * @Description :
 * @version
 */
@SuppressLint("ValidFragment")
class AptitudeOverdueDialogFragment : DialogFragment() {


    private lateinit var layoutView: View
    private var adapter: YBMBaseAdapter<AptitudeDetailListBean>? = null
    private var list: List<AptitudeDetailListBean> = emptyList()
    private var licenseStatus: Int = 0


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.apply {
            licenseStatus = getInt("licenseStatus", 0)
            val listStr = getString("dataList")
            list = if (listStr == null) emptyList()
            else {
                val listType: Type = object : TypeToken<List<AptitudeDetailListBean>>() {}.type
                Gson().fromJson(listStr, listType)?: emptyList()
            }
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        dialog?.setCanceledOnTouchOutside(false)
        layoutView = inflater.inflate(R.layout.layout_fragment_dialog_aptitude_overdue, container)
        initView(layoutView.rv_list)
        layoutView.btn_left.setOnClickListener {
            activity?.finish()
        }
        layoutView.btn_right.setOnClickListener {
            dismiss()
            RoutersUtils.open("ybmpage://aptitudebasicinfo?licenseStatus=$licenseStatus")
        }
        return layoutView
    }


    private fun initView(rvList: RecyclerView) {
        adapter = object : YBMBaseAdapter<AptitudeDetailListBean>(R.layout.item_aptitude_overdue, list) {
            override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: AptitudeDetailListBean?) {
                baseViewHolder?.apply {
                    setText(R.id.tv_aptitude_name, t?.credentialName)
                    setText(R.id.tv_aptitude_status, "需更新")
                }

            }
        }
        rvList.adapter = adapter
        rvList.layoutManager = LinearLayoutManager(context)
        rvList.addItemDecoration(DividerLine().apply {
            setColor(resources.getColor(R.color.colors_f5f5f5))
            setSize(UiUtils.sp2px(1))
        })
    }

    companion object {
        fun getInstance(licenseStatus: Int, dataList: ArrayList<AptitudeDetailListBean>): AptitudeOverdueDialogFragment {

            val filterDataList: ArrayList<AptitudeDetailListBean> = dataList.filter {
                it.status == 1 || it.status == 2
            } as ArrayList<AptitudeDetailListBean>

            val frag = AptitudeOverdueDialogFragment()
            val args = Bundle()
            args.putInt("licenseStatus", licenseStatus)
            val filterDataListStr = Gson().toJson(filterDataList)
            args.putString("dataList", filterDataListStr)
            frag.arguments = args
            return frag
        }
    }


}