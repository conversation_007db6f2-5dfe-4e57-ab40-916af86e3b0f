package com.ybmmarketkotlin.fragments

import android.app.Activity
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentTransaction
import com.ybm.app.bean.NetError
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CommonDialog
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.responseDialogAnalysisClose
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.SpUtil
import com.ybmmarketkotlin.bean.CustomDialogBean
import com.ybmmarketkotlin.bean.convertToCustomDialogBean

/**
 * 弹窗碎片管理器
 */
const val DIALOG_TAG_CUSTOM_IMAGE = "custom_image"
const val DIALOG_TAG_CUSTOM_COUPON = "custom_coupon"

object DialogFragmentManager {
    fun showDialog(activity: Activity?, dialogTag: String, commonDialog: CommonDialog?) {
        if (activity == null || activity !is FragmentActivity) {
            return
        }
        if (commonDialog?.sceneType == null) return
        if (commonDialog.couponDialogData == null) {
            getIsHaveData(activity, dialogTag, commonDialog)
        } else {
            val newDialogFragment = CustomCouponDialogFragment.newInstance(commonDialog.pageId, commonDialog.sceneType, commonDialog.couponDialogData.convertToCustomDialogBean())
            showDialogFragment(newDialogFragment, activity, dialogTag, commonDialog.sceneType)
        }
    }

    /**
     * 加这个接口是因为
     * 明天就要上线了 产品要求没数据不显示dialog，后端实现不了，所以客户端这已经写好的逻辑要大改，
     * 为了减少已经写好的DialogFragment改动 所以请求了两次这个接口，先获取有没有数据 没数据就不创建了 有数据的话会创建DialogFragment 里面会再获取数据
     * emmmm。。。
     */
    private fun getIsHaveData(activity: Activity?, tag: String, commonDialog: CommonDialog) {
        val params = RequestParams()
        params.put("merchantId", SpUtil.getMerchantid())
        params.put("pageId", commonDialog.pageId)
        HttpManager.getInstance().post(AppNetConfig.DIALOG_DETAIL, params, object : BaseResponse<CustomDialogBean>() {
            override fun onFailure(error: NetError) {
            }

            override fun onSuccess(content: String, baseBean: BaseBean<CustomDialogBean>?, data: CustomDialogBean?) {
                if (baseBean != null && baseBean.isSuccess) {

                    var newDialogFragment: DialogFragment? = null
                    if (tag == DIALOG_TAG_CUSTOM_IMAGE && data?.detail?.imageDtos != null && data.detail.imageDtos!!.isNotEmpty()) {
                        newDialogFragment = CustomImageDialogFragment.newInstance(commonDialog.pageId, commonDialog.sceneType)
                    } else if (tag == DIALOG_TAG_CUSTOM_COUPON && data?.detail?.couponDtos != null && data.detail.couponDtos!!.isNotEmpty()) {
                        newDialogFragment = CustomCouponDialogFragment.newInstance(commonDialog.pageId, commonDialog.sceneType)
                    }
                    if (newDialogFragment == null) {
                        return
                    }
                    showDialogFragment(newDialogFragment, activity, tag, commonDialog.sceneType)
                }
            }
        })
    }

    public fun showDialogFragment(newDialogFragment: DialogFragment, activity: Activity?, tag: String, sceneType: String) {
        val ft: FragmentTransaction = (activity as FragmentActivity).supportFragmentManager.beginTransaction()
        val prevFragment = activity.supportFragmentManager.findFragmentByTag(tag)
        prevFragment?.let {
            ft.remove(it)
        }
        newDialogFragment.show(ft, tag)
        newDialogFragment.dialog?.setOnDismissListener {
            responseDialogAnalysisClose(sceneType)
        }
    }
}
