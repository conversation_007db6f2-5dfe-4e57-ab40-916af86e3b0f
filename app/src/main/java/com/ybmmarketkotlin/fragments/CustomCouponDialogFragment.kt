package com.ybmmarketkotlin.fragments

import android.graphics.Color
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.util.SparseArray
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.google.android.exoplayer.extractor.mp4.Track
import com.google.gson.Gson
import com.luck.picture.lib.tools.ScreenUtils
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.bean.NetError
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.activity.COUPON_STATUS_UNRECEIVED
import com.ybmmarket20.activity.COUPON_STATUS_UNUSED
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CouponRowBean
import com.ybmmarket20.bean.HomeAlertBean
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.responseDialogAnalysisAutoGetCoupon
import com.ybmmarket20.common.responseDialogAnalysisAutoGetCouponExposure
import com.ybmmarket20.common.responseDialogAnalysisAutoGetRedEnvelopeExposure
import com.ybmmarket20.common.responseDialogAnalysisClick
import com.ybmmarket20.common.responseDialogAnalysisClose
import com.ybmmarket20.common.responseDialogAnalysisGetCoupon
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.DateTimeUtil
import com.ybmmarket20.utils.ImageLoader
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.StringUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.utils.ifNotNull
import com.ybmmarket20.utils.setDialogCouponTitleTag
import com.ybmmarket20.view.homesteady.callback.IHomeDialogCustomCallback
import com.ybmmarket20.xyyreport.spm.TrackData
import com.ybmmarketkotlin.bean.CouponDialogDataNewBean
import com.ybmmarketkotlin.bean.CustomDialogBean
import com.ybmmarketkotlin.bean.CustomDialogDetailBean
import kotlinx.android.synthetic.main.layout_fragment_custom_coupon_dialog.view.iv_custom_close
import kotlinx.android.synthetic.main.layout_fragment_custom_coupon_dialog.view.iv_top
import kotlinx.android.synthetic.main.layout_fragment_custom_coupon_dialog.view.ll_custom_coupon
import kotlinx.android.synthetic.main.layout_fragment_custom_coupon_dialog.view.ll_custom_coupon_parent
import kotlinx.android.synthetic.main.layout_fragment_custom_coupon_dialog.view.rtv_more
import kotlinx.android.synthetic.main.layout_fragment_custom_coupon_dialog.view.rv_custom_coupon

class CustomCouponDialogFragment(val alertBean: HomeAlertBean? = null, val couponBean: CouponDialogDataNewBean? = null, val callback: (()->Unit)? = null, val dialogCallback: IHomeDialogCustomCallback? = null) : DialogFragment() {
    private var pageId: String = ""
    private var sceneType: String = ""
    private var adapter: CouponDialogAdapter? = null
    private var list: ArrayList<CouponRowBean> = ArrayList()
    private lateinit var layoutView: View
    private var receiveCouponCenterUrl: String = ""
    private var topPicAction: String = ""
    private var topPicUrl: String = ""
    private var topPicColor: String? = ""
    private var topPicIsHyaline: String? = ""
    private var topTrackData: TrackData? = null
    private var couponBgColor: String? = ""
    private var couponBgIsHyaline: String? = ""
    private var customDialogBean: CustomDialogBean? = null
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        dialog?.setCanceledOnTouchOutside(false)
        layoutView = inflater.inflate(R.layout.layout_fragment_custom_coupon_dialog, container)
        layoutView.iv_top.setOnClickListener {//头图点击事件
            if (!TextUtils.isEmpty(topPicAction)) {
                RoutersUtils.open(topPicAction)
                responseDialogAnalysisClick(sceneType, topPicAction)
                setTrackDataSpmE(topTrackData, alertBean?.cms?.detail?.trackData)
                dialogCallback?.onDialogImageClick(topTrackData)
                dismiss()
            }
        }
        initRecyclerView(layoutView.rv_custom_coupon)
        layoutView.iv_custom_close.setOnClickListener {//关闭按钮
            dismiss()
            callback?.invoke()
            responseDialogAnalysisClose(sceneType)
        }
        layoutView.rtv_more.setOnClickListener {//点击领取更多跳转到领券中心
            RoutersUtils.open(receiveCouponCenterUrl)
            dismiss()
        }
        pageId = arguments?.getString("pageId") ?: ""
        sceneType = arguments?.getString("sceneType") ?: ""
        val couponDataJson = arguments?.getString("couponData")
        if (couponDataJson != null) {
            customDialogBean = Gson().fromJson<CustomDialogBean>(couponDataJson, CustomDialogBean::class.java)
        }
        //上一层为传入数据就请求
        if (couponBean != null) {
            customDialogBean = CustomDialogBean(
                CustomDialogDetailBean(
                    couponBean.imageDtos,
                    couponBean.couponDtos,
                    couponBean.receiveCouponCenterUrl,
                    couponBean.couponColorDto)
            )
            setCouponData(customDialogBean)
            setParams()
        } else if (customDialogBean == null) {
            getData()
        } else {
            setCouponData(customDialogBean)
            setParams()
        }
        return layoutView
    }

    private fun setParams() {
        val parent = layoutView.ll_custom_coupon_parent
        val topImage = layoutView.iv_top
        val screenWidth = ScreenUtils.getScreenWidth(context)
        val width = screenWidth - ScreenUtils.dip2px(context, 85f)
        val parentLp = parent.layoutParams as FrameLayout.LayoutParams
        parentLp.width = width
//        parentLp.height = (width/(29f/38f)).toInt()
        val topLp = topImage.layoutParams as LinearLayout.LayoutParams
        topLp.height = (width/(290f/89f)).toInt()
        if (adapter?.itemCount!! > 3) {
            val customCouponView = layoutView.ll_custom_coupon
            val customCouponLp = customCouponView.layoutParams as LinearLayout.LayoutParams
            customCouponLp.height = ScreenUtils.dip2px(context, 300f)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //页面曝光埋点
        XyyIoUtil.track(XyyIoUtil.PAGE_HOMEPAGE_COUPON_POPUP)
    }

    private fun getData() {
        val params = RequestParams()
        params.put("merchantId", SpUtil.getMerchantid())
        params.put("pageId", pageId)
        HttpManager.getInstance()
            .post(AppNetConfig.DIALOG_DETAIL, params, object : BaseResponse<CustomDialogBean>() {
                override fun onFailure(error: NetError) {
                }

                override fun onSuccess(
                    content: String,
                    baseBean: BaseBean<CustomDialogBean>?,
                    data: CustomDialogBean?
                ) {
                    if (baseBean != null && baseBean.isSuccess) {
                        setCouponData(data)
                        setParams()
                    }
                }
            })
    }

    /**
     * 设置优惠券数据
     */
    private fun setCouponData(data: CustomDialogBean?) {
        if (data?.detail?.couponDtos != null && data.detail.couponDtos!!.isNotEmpty()) {
            list.clear()
            //原逻辑限制只展示3条，现在全部展示
            list.addAll(data.detail.couponDtos!!)
            adapter?.setNewData(list)
            receiveCouponCenterUrl = data.detail.receiveCouponCenterUrl ?: ""
//            layoutView.rtv_more.visibility =
//                if (list.size >= 3 && !TextUtils.isEmpty(receiveCouponCenterUrl)) View.VISIBLE else View.GONE
            //该按钮不显示
            layoutView.rtv_more.visibility = View.GONE
            couponBgColor = data.detail.couponColorDto?.couponColor
            couponBgIsHyaline = data.detail.couponColorDto?.isHyaline
            if (data.detail.imageDtos != null && data.detail.imageDtos!!.isNotEmpty()) {//取头图数据
                topPicAction = data.detail.imageDtos!![0].action
                topPicUrl = data.detail.imageDtos!![0].imgUrl
                topPicColor = data.detail.imageDtos!![0].imageColor
                topPicIsHyaline = data.detail.imageDtos!![0].isHyaline
                topTrackData = data.detail.imageDtos!![0].trackData
                layoutView.iv_top.visibility =
                    if (TextUtils.isEmpty(topPicUrl)) View.GONE else View.VISIBLE
                ImageLoader.loadImage(context, layoutView.iv_top, topPicUrl)
                //背景颜色设置
                if ("false" == topPicIsHyaline) {//false不透明，为false时，imageColor必有值
                    layoutView.iv_top.setBackgroundColor(getColor(topPicColor))
                }
            }
            if ("false" == couponBgIsHyaline) {////false不透明，为false时，couponColor必有值
                layoutView.ll_custom_coupon.setBackgroundColor(getColor(couponBgColor))
            } else {
                layoutView.ll_custom_coupon.setBackgroundColor(getColor("#ffffff"))
            }
        }
    }

    private fun initRecyclerView(rv: RecyclerView) {
        adapter = CouponDialogAdapter(list)
        val emptyView =
            layoutInflater.inflate(R.layout.layout_empty_view, rv.parent as ViewGroup, false)
        adapter?.emptyView = emptyView
        rv.layoutManager = WrapLinearLayoutManager(context)
        rv.adapter = adapter
    }

    /**
     * 领取优惠券
     */
    fun receiveCoupon(couponRowBean: CouponRowBean?) {
        if (couponRowBean == null) return
        val params = RequestParams()
        val couponId: String = couponRowBean.templateId + ""
        params.put("voucherTemplateId", couponId)
        params.put("merchantId", SpUtil.getMerchantid())
        HttpManager.getInstance().post(
            AppNetConfig.RECEIVE_USABLE_VOUCHER,
            params,
            object : BaseResponse<BaseBean<*>>() {
                override fun onSuccess(
                    content: String?,
                    obj: BaseBean<BaseBean<*>>?,
                    baseBean: BaseBean<*>?
                ) {
                    if (obj != null && obj.isSuccess) {
                        couponRowBean.state = COUPON_STATUS_UNUSED
                        adapter?.notifyDataSetChanged()
                    }
                }
            })
    }

    companion object {
        fun newInstance(pageId: String?, sceneType: String?, customDialogBean: CustomDialogBean? = null): CustomCouponDialogFragment {
            val frag = CustomCouponDialogFragment()
            val args = Bundle()
            args.putString("pageId", pageId)
            args.putString("sceneType", sceneType)
            if (customDialogBean != null) {
                args.putString("couponData", Gson().toJson(customDialogBean))
            }
            frag.arguments = args
            return frag
        }

        fun newInstanceWithData(alertBean: HomeAlertBean?, couponBean: CouponDialogDataNewBean?, callback: (()->Unit)? = null, dialogCallback: IHomeDialogCustomCallback? = null): CustomCouponDialogFragment
                = CustomCouponDialogFragment(alertBean, couponBean, callback, dialogCallback)
    }

    private fun getColor(color: String?): Int {
        return try {
            Color.parseColor(color)
        } catch (e: Exception) {
            Color.parseColor("#00000000")
        }
    }

    inner class CouponDialogAdapter(val list: ArrayList<CouponRowBean>): BaseMultiItemQuickAdapter<CouponRowBean, YBMBaseHolder>(list) {

        private val traceProductData = SparseArray<String>()

        init {
            //券
            addItemType(1, R.layout.item_fragment_custom_coupon_dialog)
            //红包
            addItemType(2, R.layout.item_fragment_red_envelope_dialog)
        }

        override fun convert(holder: YBMBaseHolder, item: CouponRowBean?) {
            if (item == null) return
            when(item.itemType) {
                1 -> handleCoupon(holder, item)
                2 -> handleRedEnvelope(holder, item)
            }
        }

        //处理券
        private fun handleCoupon(ybmBaseHolder: YBMBaseHolder, bean: CouponRowBean) {
            ifNotNull(ybmBaseHolder, bean) { holder, bean ->
                if (traceProductData[ybmBaseHolder.bindingAdapterPosition] == null) {
                    traceProductData.put(ybmBaseHolder.bindingAdapterPosition, "${ybmBaseHolder.bindingAdapterPosition}")
                    if (sceneType == "3") {
                        responseDialogAnalysisAutoGetCouponExposure(sceneType, bean.templateId)
                    }
                }

                val tvPriceUnit = holder.getView<TextView>(R.id.tv_PriceUnit)
                val tvDiscountUnit = holder.getView<TextView>(R.id.tv_discount_unit)
                val tvCouponFullReduceMax = holder.getView<TextView>(R.id.tv_coupon_full_reduce_max)
                val tvCouponTitle = holder.getView<TextView>(R.id.tv_coupon_title)
                // val tvCouponLimt = holder.getView<TextView>(R.id.tv_coupon_limit)

                //券名
                setDialogCouponTitleTag(
                        mContext,
                        tvCouponTitle,
                        R.drawable.shape_dialog_coupon_type_tag_bg,
                        R.color.white,
                        bean.voucherTitle
                                ?: "",
                        bean.voucherTypeDesc ?: ""
                )
                //使用范围 (这里ui上是使用时间)
                //tvCouponLimt.text = bean.voucherInstructions
                //使用门槛
                holder.setText(R.id.tv_coupon_full_reduce, bean.minMoneyToEnableDesc)

                // 最高减信息展示
                if (!bean.maxMoneyInVoucherDesc.isNullOrEmpty()) {
                    tvCouponFullReduceMax.visibility = View.VISIBLE
                    tvCouponFullReduceMax.text = bean.maxMoneyInVoucherDesc
                } else {
                    tvCouponFullReduceMax.visibility = View.GONE
                }

                //券有效期
                holder.setVisible(
                        R.id.tv_coupon_date,
                        (bean.validDate.toInt() != 0 && bean.expireDate.toInt() != 0) || !TextUtils.isEmpty(bean.validDayStr)
                )
                holder.setVisible(
                        R.id.tv_coupon_limit,
                        (bean.validDate.toInt() != 0 && bean.expireDate.toInt() != 0) || !TextUtils.isEmpty(bean.validDayStr)
                )
                if (bean.validDate.toInt() != 0 && bean.expireDate.toInt() != 0) {
                    val time = (DateTimeUtil.getCouponDateTime2(bean.validDate)
                            + "-" + DateTimeUtil.getCouponDateTime2(bean.expireDate))
                    holder.setText(R.id.tv_coupon_date, time)
                } else if (!TextUtils.isEmpty(bean.validDayStr)) {
                    holder.setText(R.id.tv_coupon_date, bean.validDayStr)
                }

                val isMinMoney = !TextUtils.isEmpty(bean.minMoneyToEnableDesc)
                holder.setVisible(R.id.tv_coupon_full_reduce, isMinMoney)

                //未开始的券不展示去使用的按钮
                if (System.currentTimeMillis() >= bean.validDate && bean.state == COUPON_STATUS_UNUSED) {
                    holder.setVisible(R.id.tv_coupon_immediate_use, true)
                } else {
                    holder.setVisible(R.id.tv_coupon_immediate_use, false)
                }
                //立即领取按钮展示
                holder.setVisible(
                        R.id.tv_coupon_get_it_now,
                        bean.state == COUPON_STATUS_UNRECEIVED
                )

                if (bean.discount > 0) {
                    tvPriceUnit.visibility = View.GONE
                    tvDiscountUnit.visibility = View.VISIBLE
                    holder.setText(R.id.tv_coupon_amount, bean.discountStr)
                    tvDiscountUnit.text = "折"
                } else {
                    tvPriceUnit.visibility = View.VISIBLE
                    tvDiscountUnit.visibility = View.GONE
                    holder.setText(
                            R.id.tv_coupon_amount,
                            UiUtils.transformInt(bean.moneyInVoucher)
                    )
                    tvPriceUnit.text = "¥"
                }
                holder.setOnClickListener(R.id.tv_coupon_immediate_use) {//立即使用
                    setTrackDataSpmE(bean.trackData, alertBean?.cms?.detail?.trackData)
                    dialogCallback?.onDialogCouponClick(bean.trackData)
                    if (sceneType == "3") {
                        responseDialogAnalysisAutoGetCoupon(sceneType, bean.templateId)
                    }
                    val router = if (TextUtils.isEmpty(bean.appUrl)) {
                        "ybmpage://couponavailableactivity/${bean.templateId}"
                    } else {
                        bean.appUrl ?: ""
                    }
                    if (router.startsWith("ybmpage")) {
                        // 弹窗点击埋点
                        XyyIoUtil.track(XyyIoUtil.ACTION_HOMEPAGE_COUPON_POPUP_CLICK)
                        RoutersUtils.open(router)
                        dismiss()
                    }
                }
                holder.setOnClickListener(R.id.tv_coupon_get_it_now) {//立即领取
                    setTrackDataSpmE(bean.trackData, alertBean?.cms?.detail?.trackData)
                    dialogCallback?.onDialogCouponClick(bean.trackData)
                    // 弹窗点击埋点
                    XyyIoUtil.track(XyyIoUtil.ACTION_HOMEPAGE_COUPON_POPUP_CLICK)
                    receiveCoupon(bean)
                    responseDialogAnalysisGetCoupon(sceneType, "")
                    if (sceneType == "3") {
                        responseDialogAnalysisAutoGetCoupon(sceneType, bean.templateId)
                    }
                }
            }
        }

        //处理红包
        private fun handleRedEnvelope(holder: YBMBaseHolder, item: CouponRowBean) {
            holder.setText(R.id.tv_red_envelope_title, item.templateName)
            if (!TextUtils.isEmpty(item.validDateToString) && !TextUtils.isEmpty(item.expireDateToString)) {
                holder.setText(R.id.tv_red_envelope_time, "${item.validDateToString}-${item.expireDateToString}")
            } else {
                holder.setText(R.id.tv_red_envelope_time, item.validDayStr)
            }
            val builder = SpannableStringBuilder("￥").run {
                setSpan(AbsoluteSizeSpan(12, true), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
//                var tempStr = item.redPacketOriginMoney
//                val originMoney: String = if (null != tempStr && tempStr.indexOf(".") > 0) {
//                    tempStr = tempStr.replace("0+?\$".toRegex(), "")
//                    tempStr = tempStr.replace("[.]\$".toRegex(), "")
//                    tempStr
//                } else ""
                append(StringUtil.removeZeroAfterDot(UiUtils.transform(item.redPacketOriginMoney)))
            }
            holder.setText(R.id.tv_red_envelope_gold, builder)
            if (traceProductData[holder.bindingAdapterPosition] == null) {
                traceProductData.put(holder.bindingAdapterPosition, "${holder.bindingAdapterPosition}")
                responseDialogAnalysisAutoGetRedEnvelopeExposure(sceneType, item.marketingId, item.templateId)
            }
        }
    }

    private fun setTrackDataSpmE(trackData: TrackData?, spmTrackData: TrackData?) {
        trackData?.spmEntity?.spmE = spmTrackData?.spmEntity?.spmE
    }
}