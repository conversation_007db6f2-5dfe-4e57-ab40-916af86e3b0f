package com.ybmmarketkotlin.fragments

import android.graphics.Typeface
import androidx.recyclerview.widget.RecyclerView
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.RelativeSizeSpan
import android.text.style.StrikethroughSpan
import android.text.style.StyleSpan
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.common.BaseFragment
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.ImageLoader
import com.ybmmarket20.utils.MathUtils
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.handleAmount
import com.ybmmarket20.view.ProductEditLayout
import com.ybmmarket20.view.ProductEditLayout.AddCartListener
import com.ybmmarket20.view.ProductEditLayout3
import com.ybmmarketkotlin.bean.ComboRowsBean
import kotlinx.android.synthetic.main.fragment_commodity_combo.*

class ComboFragment : BaseFragment() {
    private var packageId: String = ""
    private var packagePrice: String = ""
    private var subtotalPrice: String = ""
    private var skuNum: String = ""
    private var skuList: ArrayList<ComboRowsBean> = ArrayList()
    override fun initData(content: String) {
        val bundle = arguments
        if (bundle != null) {
            skuList = bundle.getParcelableArrayList("skuList") ?: ArrayList()
            packageId = bundle.getString("packageId", "")
            packagePrice = bundle.getString("packagePrice", "")
            subtotalPrice = bundle.getString("subtotalPrice", "")
            skuNum = bundle.getString("skuNum", "")
        }
        setPriceData()
        initRecyclerView()
        initAddToCart()
    }

    private fun initAddToCart() {
        el_edit.setOnAddCartListener(object : AddCartListener {
            override fun onPreAddCart(params: RequestParams): RequestParams {
                return params
            }

            override fun onAddCartSuccess() {}
        })
        //加减按钮
        el_edit.bindPackage(packageId.toLong(), 0, ProductEditLayout.FROMPAGE_COMMODITY_COMBO)
        //改变加减框的样式
        el_edit.modifyViewStyle(R.color.white, R.drawable.icon_sku_plus, R.drawable.icon_sku_minus, R.color.color_292933, R.drawable.shape_bg_commodity_combo)
        el_edit.setAnimationEnable(false)
    }

    private fun setPriceData() {
        val startSpannableString = SpannableString(" 套餐价 ")
        val startSizeSpan = RelativeSizeSpan(0.75f)
        startSpannableString.setSpan(startSizeSpan, 1, 4, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
        val boldStyleSpan = StyleSpan(Typeface.BOLD)
        val priceSpannableString = handleAmount(packagePrice)
        priceSpannableString.setSpan(boldStyleSpan, 0, priceSpannableString.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
        val numSpannableString = SpannableString("（共" + skuNum + "种） ")
        val numSizeSpan = RelativeSizeSpan(0.68f)
        numSpannableString.setSpan(numSizeSpan, 0, numSpannableString.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
        val endSpannableString = SpannableString("原价" + MathUtils.getFormatRmb(subtotalPrice))
        val endSizeSpan = RelativeSizeSpan(0.68f)
        val strikethroughSpan = StrikethroughSpan()
        endSpannableString.setSpan(endSizeSpan, 0, endSpannableString.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
        endSpannableString.setSpan(strikethroughSpan, 0, endSpannableString.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
        val builder = SpannableStringBuilder()
        builder.append(startSpannableString).append(priceSpannableString).append(numSpannableString).append(endSpannableString)
        tv_combo_price.text = builder
    }

    private fun initRecyclerView() {
        val adapter: YBMBaseAdapter<*> = object : YBMBaseAdapter<ComboRowsBean>(R.layout.item_commodity_combo_list_view, skuList) {
            override fun bindItemView(ybmBaseHolder: YBMBaseHolder, bean: ComboRowsBean) {
                ybmBaseHolder.setText(R.id.tv_name, bean.commonName)
                    .setText(R.id.tv_specification, bean.spec)
                    .setText(R.id.tv_count, "X" + bean.productNumber)
                    .setText(R.id.tv_price, bean.fob?.let { handleAmount(it) })
                val tvPrice = ybmBaseHolder.getView<TextView>(R.id.tv_price)
                val ivPic = ybmBaseHolder.getView<ImageView>(R.id.iv_pic)
                var imageUrl = bean.imageUrl ?: ""
                if (!imageUrl.startsWith("http")) {
                    imageUrl = AppNetConfig.LORD_IMAGE + imageUrl
                }
                ImageLoader.loadImage(mContext, ivPic, imageUrl, R.drawable.jiazaitu_min)
                setPriceStatus(tvPrice, bean)
                ybmBaseHolder.getConvertView().setOnClickListener {
                    RoutersUtils.open("ybmpage://productdetail?" + IntentCanst.PRODUCTID + "=" + bean.skuId)
                }
            }
        }
        val emptyView = notNullActivity.layoutInflater.inflate(R.layout.layout_empty_view, rv_combo.parent as ViewGroup, false)
        val tvError = emptyView.findViewById<TextView>(R.id.tv)
        tvError.text = "暂无套餐数据"
        adapter.emptyView = emptyView
        rv_combo.isNestedScrollingEnabled = false
        rv_combo.adapter = adapter
        val linearLayoutManager = WrapLinearLayoutManager(context)
        linearLayoutManager.orientation = WrapLinearLayoutManager.HORIZONTAL
        rv_combo.layoutManager = linearLayoutManager
        rv_combo.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                riv_combo.setRvOnScrolledListener(recyclerView)
            }
        })
        //列表数据少于等于两个 需要居中显示，不显示指示器
        riv_combo.visibility = if (skuList.size <= 2) View.GONE else View.VISIBLE
    }

    private fun setPriceStatus(tv_price: TextView, bean: ComboRowsBean) {
        var priceStatusText = ""
        when (bean.showPriceType()) {
            0 -> {
            }
            1 -> {
                priceStatusText = "暂无购买权限"
                setLimitUi(priceStatusText, tv_price, el_edit)
            }
            2 -> {
                priceStatusText = "价格签署协议可见"
                setLimitUi(priceStatusText, tv_price, el_edit);
            }
            3 -> {
                priceStatusText = "价格认证资质可见"
                setLimitUi(priceStatusText, tv_price, el_edit);
            }
        }
    }

    private fun setLimitUi(priceStatusText: String, tvPrice: TextView, elEdit: ProductEditLayout3) {
        tvPrice.text = priceStatusText
        tvPrice.textSize = 10f
        elEdit.visibility = View.INVISIBLE
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_commodity_combo
    }

    override fun initTitle() {}
    override fun getParams(): RequestParams? {
        return null
    }

    override fun getUrl(): String? {
        return ""
    }
}