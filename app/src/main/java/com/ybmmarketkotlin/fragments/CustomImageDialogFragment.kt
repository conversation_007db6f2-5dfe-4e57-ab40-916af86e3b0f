package com.ybmmarketkotlin.fragments

import android.graphics.Bitmap
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.fragment.app.DialogFragment
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.request.animation.GlideAnimation
import com.bumptech.glide.request.target.SimpleTarget
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.bean.NetError
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.HomeAlertBean
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.responseDialogAnalysisClick
import com.ybmmarket20.common.responseDialogAnalysisClose
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.ImageLoader
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.homesteady.callback.IHomeDialogCustomCallback
import com.ybmmarketkotlin.bean.CustomDialogBean
import com.ybmmarketkotlin.bean.ImageDialogBean
import kotlinx.android.synthetic.main.layout_fragment_custom_image_dialog.view.iv_custom_close
import kotlinx.android.synthetic.main.layout_fragment_custom_image_dialog.view.rv_custom_image
import kotlin.collections.set

class CustomImageDialogFragment(val alertBean: HomeAlertBean? = null, val imageDtos: List<ImageDialogBean>? = null, val callback: (()->Unit)? = null, val dialogCallback: IHomeDialogCustomCallback? = null) : DialogFragment() {
    private var pageId: String = ""
    private var sceneType: String = ""
    private var list: ArrayList<ImageDialogBean> = ArrayList()
    private var adapter: YBMBaseAdapter<*>? = null
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        dialog?.setCanceledOnTouchOutside(false)
        val view = inflater.inflate(R.layout.layout_fragment_custom_image_dialog, container)
        view.iv_custom_close.setOnClickListener {
            dismiss()
            callback?.invoke()
            responseDialogAnalysisClose(sceneType)
        }
        initRecyclerView(view.rv_custom_image)
        if (imageDtos != null) {
            if (imageDtos.isNotEmpty()) {
                list.clear()
                list.addAll(imageDtos)
                adapter?.setNewData(list)
            }
        } else {
            getData()
        }
        return view
    }

    private fun initRecyclerView(rv: RecyclerView) {
        adapter = object : YBMBaseAdapter<ImageDialogBean>(R.layout.item_fragment_custom_image_dialog, list) {
            override fun bindItemView(ybmBaseHolder: YBMBaseHolder, bean: ImageDialogBean?) {
                val ivPic = ybmBaseHolder.getView<ImageView>(R.id.iv_image)
                var imageUrl = bean?.imgUrl ?: ""
                if (!imageUrl.startsWith("http")) {
                    imageUrl = AppNetConfig.LORD_IMAGE + imageUrl
                }
                /**
                 *  显示图片组件宽度固定，高度按图片宽高比算出 组件高度=图片高*组件宽/图片宽
                 */
                ImageLoader.loadBitmap(mContext, object : SimpleTarget<Bitmap?>() {
                    override fun onResourceReady(bitmap: Bitmap?, glideAnimation: GlideAnimation<in Bitmap?>?) {
                        val rvWidth = rv.width
                        val rvHeight = rv.height
                        val width = bitmap?.width ?: 1
                        val height = bitmap?.height ?: 0
                        val ivWidth = rvWidth
                        val ivHeight = height * ivWidth / width
                        if (ivHeight != 0) {
                            ivPic.layoutParams.height = ivHeight
                            ivPic.layoutParams.width = ivWidth
                        }
                        ImageLoader.loadImage(mContext, ivPic, imageUrl)
                    }
                }, imageUrl)

                ybmBaseHolder.getConvertView().setOnClickListener {
                    click(bean?.action ?: "",bean,ybmBaseHolder.adapterPosition)
                    alertBean?.cms?.detail?.trackData?.spmEntity?.let {
                        bean?.trackData?.spmEntity?.spmE = it.spmE
                    }
                    dialogCallback?.onDialogImageClick(bean?.trackData)
                }

            }
        }
        val emptyView = layoutInflater.inflate(R.layout.layout_empty_view, rv.parent as ViewGroup, false)
        adapter?.emptyView = emptyView
        rv.layoutManager = WrapLinearLayoutManager(context)
        rv.adapter = adapter
    }

    //执行点击
    private fun click(action: String, bean: ImageDialogBean?, position: Int) {
        if (TextUtils.isEmpty(action)) {
            return
        }
        // 广告弹窗跳转点击埋点
        val contentMap = HashMap<String, String>()
        contentMap["action"] = action
        XyyIoUtil.track(XyyIoUtil.ACTION_HOMEPAGE_AD_POPUP_CLICK, contentMap)
        RoutersUtils.open(action)
        responseDialogAnalysisClick(sceneType, action)
        dismiss()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        pageId = arguments?.getString("pageId") ?: ""
        sceneType = arguments?.getString("sceneType") ?: ""
        //页面曝光埋点
        XyyIoUtil.track(XyyIoUtil.PAGE_HOMEPAGE_AD_POPUP)
    }

    private fun getData() {
        val params = RequestParams()
        params.put("merchantId", SpUtil.getMerchantid())
        params.put("pageId", pageId)
        HttpManager.getInstance().post(AppNetConfig.DIALOG_DETAIL, params, object : BaseResponse<CustomDialogBean>() {
            override fun onFailure(error: NetError) {
            }

            override fun onSuccess(content: String, baseBean: BaseBean<CustomDialogBean>?, data: CustomDialogBean?) {
                if (baseBean != null && baseBean.isSuccess) {
                    if (data?.detail?.imageDtos != null && data.detail.imageDtos!!.isNotEmpty()) {
                        list.clear()
                        list.addAll(data.detail.imageDtos!!)
                        adapter?.setNewData(list)
                    }
                }
            }
        })
    }

    companion object {
        fun newInstance(pageId: String?, sceneType: String?): CustomImageDialogFragment {
            val frag = CustomImageDialogFragment()
            val args = Bundle()
            args.putString("pageId", pageId)
            args.putString("sceneType", sceneType)
            frag.arguments = args
            return frag
        }

        fun newInstanceWithData(alertBean: HomeAlertBean?, imageDtos: List<ImageDialogBean>, callback: (()->Unit)? = null, dialogCallback: IHomeDialogCustomCallback? = null): CustomImageDialogFragment
            = CustomImageDialogFragment(alertBean, imageDtos, callback, dialogCallback)
    }
}