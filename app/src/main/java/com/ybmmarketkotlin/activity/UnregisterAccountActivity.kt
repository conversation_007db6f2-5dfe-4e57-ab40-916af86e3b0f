package com.ybmmarketkotlin.activity

import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarketkotlin.viewmodel.UnregisterViewModel
import kotlinx.android.synthetic.main.activity_unregister_account.*

@Router("unregister")
class UnregisterAccountActivity : BaseActivity() {
    override fun getContentViewId() = R.layout.activity_unregister_account

    private lateinit var mViewModel: UnregisterViewModel
    private val dialg by lazy {
        AlertDialogEx(this).apply {
            setCanceledOnTouchOutside(false)
            setCancelButton("取消") { dialog, button ->
            }
            setConfirmButton("确认") { dialog, button ->
                mViewModel.commitUnregister(SpUtil.getMerchantid())
            }
        }
    }


    override fun initData() {
        setTitle("账号注销")

        mViewModel = ViewModelProvider(this).get(UnregisterViewModel::class.java)
        mViewModel.unregisterStatus.observe(this, Observer {
            // ture : 已申请冻结，false :未申请冻结
            if (it) {
                gp_gone.visibility = View.GONE
                btn_commit?.isEnabled = false
                btn_commit?.text = "已提交申请"
            } else {
                gp_gone.visibility = View.VISIBLE
                btn_commit?.isEnabled = (check_unregister?.isChecked ?: false)
                btn_commit?.text = "申请注销"

                var spanStr = SpannableString("申请注销及标识你自愿放弃账号内所有虚拟资产并同意《药帮忙账号注销须知》")
                //设置文字的单击事件
                spanStr.setSpan(object : ClickableSpan() {
                    override fun updateDrawState(ds: TextPaint) {
                        super.updateDrawState(ds)
                        //设置文件颜色
                        ds.color = UiUtils.getColor(R.color.color_00B377)
                        ds.isUnderlineText = false
                    }

                    override fun onClick(widget: View) {
                        RoutersUtils.open("ybmpage://commonh5activity?url=" + AppNetConfig.UNREGISTER_PRICACE)
                    }
                }, 24, 24 + 11, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                tv_unregister_direction?.text = spanStr
                tv_unregister_direction?.movementMethod = LinkMovementMethod.getInstance()
            }
        })
        mViewModel.commitBean.observe(this, Observer {
            if (it) {
                mViewModel.unregisterStatus.postValue(true)
                //ToastUtils.showLong("您的申请已提交成功，我们将在15个工作日为您处理")
                RoutersUtils.logoutApp()
            }
        })

        mViewModel.findUnregisterStatus(SpUtil.getMerchantid())

        check_unregister?.setOnCheckedChangeListener { _, isChecked ->
            btn_commit?.isEnabled = isChecked
        }
        btn_commit?.setOnClickListener {
            dialg.show("", "确认要申请注销账号吗？")
        }
    }

}