package com.ybmmarketkotlin.activity

import android.app.Activity
import android.content.Intent
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.widget.TextView
import com.apkfuns.logutils.LogUtils
import com.github.mzule.activityrouter.annotation.Router
import com.luck.picture.lib.tools.DoubleUtils
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.EmptyBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.TimeCountUtil
import kotlinx.android.synthetic.main.activity_login_vertification.*
import kotlinx.android.synthetic.main.common_header_items.*

@Router("login_vertification")
class LoginVertificationActivity : BaseActivity() {
    var mobile: String? = null
    var merchantId: String? = null

    companion object {
        fun getStartIntent(mcontext: Activity, mobile: String, merchantId: String): Intent = Intent(mcontext, LoginVertificationActivity::class.java).apply {
            putExtra("mobile", mobile)
            putExtra("merchantId", merchantId)
        }
    }

    override fun getContentViewId(): Int = R.layout.activity_login_vertification

    override fun initData() {
        setTitle("账号登录验证")
        mobile = intent.getStringExtra("mobile")
        merchantId = intent.getStringExtra("merchantId")

        // LogUtils.e("xyd 接收到得消息为 mobile = ${mobile}  merchantId = ${merchantId}")
        tv_login_account?.text = "账号：${mobile}"

        iv_back.setOnClickListener {
            onBackPressed()
        }

        btn_send_vertifycode?.setOnClickListener {
            sendVertifycode()
        }

        et_vetification_code?.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {

            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                btn_confirm?.isEnabled = !TextUtils.isEmpty(s)
            }

            override fun afterTextChanged(s: Editable?) {

            }

        })

        btn_confirm?.setOnClickListener {
            if (!DoubleUtils.isFastDoubleClick()) {
                confirmVertifyCode()
            }
        }
    }

    private fun confirmVertifyCode() {
        val params = RequestParams()
        params.put("mobile", mobile)
        params.put("merchantId", merchantId)
        params.put("code", et_vetification_code?.text?.trim()?.toString() ?: "")
        HttpManager.getInstance().post(AppNetConfig.CHECKCRAWLERCODE, params, object : BaseResponse<EmptyBean?>() {
            override fun onSuccess(content: String?, obj: BaseBean<EmptyBean?>?, t: EmptyBean?) {
                if (obj?.isSuccess == true) {
                    val intent = Intent().apply {
                        putExtra(IntentCanst.LOGINVERTIFICATIONRESULT, true)
                    }
                    setResult(Activity.RESULT_OK, intent)
                    finish()
                }
            }
        })
    }

    private fun sendVertifycode() {
        val params = RequestParams()
        params.put("mobile", mobile)
        HttpManager.getInstance().post(AppNetConfig.LOGIN_CRAWLERCODE, params, object : BaseResponse<EmptyBean?>() {
            override fun onSuccess(content: String?, obj: BaseBean<EmptyBean?>?, t: EmptyBean?) {
                if (obj?.isSuccess == true) {
                    TimeCountUtil(btn_send_vertifycode, 60000, 1000)
                        .setCountdownListener(object : TimeCountUtil.CountdownListener {
                            override fun onCountdownIngListener(view: TextView?, secondsNum: String) {
                                view?.text = "已发送(${secondsNum})"
                                view?.isEnabled = false
                            }

                            override fun onCountdownFinishListener(view: TextView?) {
                                view?.text = "重新获取"
                                view?.isEnabled = true
                            }
                        }).start()
                }
            }
        })
    }

    override fun onBackPressed() {
        RoutersUtils.open("ybmpage://login")
        finish()
    }
}