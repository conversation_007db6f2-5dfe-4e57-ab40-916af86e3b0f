package com.ybmmarketkotlin.activity

import android.net.Uri
import android.text.TextUtils
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import com.apkfuns.logutils.LogUtils
import com.github.mzule.activityrouter.annotation.Router
import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SearchResultBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.AdapterUtils.getAfterDiscountPrice
import com.ybmmarket20.utils.AdapterUtils.notifyAndControlLoadmoreStatus
import com.ybmmarket20.utils.StringUtil
import com.ybmmarketkotlin.adapter.GoodListAdapterNew
import kotlinx.android.synthetic.main.activity_push_recommend_product.*

@Router("push_recommend_product")
class PushRecommendProductActiviy : BaseActivity() {

    private lateinit var adapter: GoodListAdapterNew
    private var rows: MutableList<RowsBean>? = null
    private lateinit var loadMoreParams: RequestParams
    private var requestUrl: String? = null

    override fun getContentViewId() = R.layout.activity_push_recommend_product


    override fun initData() {
        val title = intent.getStringExtra("push_recommend_title") ?: "策略推荐"
        var undecoderequestUrr = intent.getStringExtra("requestUrl") ?: ""
        var fullUrl = StringUtil.decodeBase64ToString(undecoderequestUrr)
        requestUrl = fullUrl
        LogUtils.d("xyd requestUrl decodeurl = " + requestUrl)

        setTitle(title)

        adapter = GoodListAdapterNew(R.layout.item_goods_new, rows)
        val emptyView = layoutInflater.inflate(R.layout.layout_empty_view, null)
        emptyView.layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        adapter.emptyView = emptyView
        cv_list.adapter = adapter
        cv_list.layoutManager = LinearLayoutManager(this)
        adapter.setOnLoadMoreListener({ getRecommendData(false) }, cv_list)
        smartrefresh.setOnRefreshListener { getRecommendData(true) }
        smartrefresh.autoRefresh()
    }

    private fun getRecommendData(isFirst: Boolean) {

        var params: RequestParams = getParams(isFirst)
        HttpManager.getInstance().post(
            getUrlWithoutparams(requestUrl),
            params,
            object : BaseResponse<SearchResultBean>() {
                override fun onSuccess(
                    content: String?,
                    obj: BaseBean<SearchResultBean>?,
                    rowsBeans: SearchResultBean?
                ) {
                    if (isFirst) {
                        smartrefresh.finishRefresh()
                    }
                    if (obj != null && obj.isSuccess() && rowsBeans != null) {
                        updateSearchData(isFirst, rowsBeans)
                    }
                }

                override fun onFailure(error: NetError?) {
                    super.onFailure(error)
                    if (isFirst) {
                        smartrefresh.finishRefresh(false)
                    }
                }
            })

    }

    /**
     * 请求搜索后的数据更新
     *
     * @param rowsBeans
     */
    private fun updateSearchData(isFirst: Boolean, rowsBeans: SearchResultBean) {
        loadMoreParams = rowsBeans.requestParams
        notifyAndControlLoadmoreStatus(rowsBeans.rows, adapter, isFirst, rowsBeans.isEnd)
        // 请求并更新折后价
        getAfterDiscountPrice(rowsBeans.rows, adapter)
    }

    private fun getUrlWithoutparams(rawUri: String?): String {
        var requestUriString: String
        if (TextUtils.isEmpty(rawUri)) {
            ToastUtils.showShort("请求地址有误")
            requestUriString = ""
        } else {
            val parsedUri = Uri.parse(rawUri)
            requestUriString =
                Uri.Builder().scheme(parsedUri.scheme).authority(parsedUri.encodedAuthority)
                    .path(parsedUri.path).toString()
            LogUtils.d("xyd requestUriString = " + requestUriString)
        }
        return requestUriString
    }

    /**
     * 获取加载入参
     */
    private fun getParams(isFirst: Boolean): RequestParams {
        var requestParams: RequestParams
        if (isFirst) {
            requestParams = RequestParams().apply {
                val parsedUri = Uri.parse(requestUrl)
                val iterator = parsedUri.queryParameterNames.iterator()
                while (iterator.hasNext()) {
                    var key = iterator.next()
                    put(key, parsedUri.getQueryParameter(key))
                }
            }
        } else {
            requestParams = loadMoreParams
        }
        return requestParams
    }

}