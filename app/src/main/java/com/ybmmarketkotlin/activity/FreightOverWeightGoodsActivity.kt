package com.ybmmarketkotlin.activity

import android.view.View
import android.view.ViewGroup
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.github.mzule.activityrouter.annotation.Router
import com.luck.picture.lib.tools.DoubleUtils
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.bean.NetError
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybm.app.view.CommonRecyclerView
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.FreightOverWeightListBean
import com.ybmmarket20.bean.OverWeightBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.handleSubtotalYuanAmount
import kotlinx.android.synthetic.main.activity_freight_over_weight_goods.*
import java.util.*

/**
 * 超重商品
 */
@Router("freightoverweightgoods")
class FreightOverWeightGoodsActivity : BaseActivity() {
    private var mAdapter: YBMBaseAdapter<OverWeightBean>? = null
    private val list: MutableList<OverWeightBean?> = ArrayList()
    override fun getContentViewId(): Int {
        return R.layout.activity_freight_over_weight_goods
    }

    override fun initData() {
        setTitle("超重商品")
        initRecyclerView()

    }

    private fun initRecyclerView() {
        mAdapter = object : YBMBaseAdapter<OverWeightBean>(R.layout.item_freight_over_weight_goods_list_view, list) {
            override fun bindItemView(ybmBaseHolder: YBMBaseHolder, bean: OverWeightBean) {
                ybmBaseHolder.setText(R.id.tv_title, bean.name)
                        .setText(R.id.tv_single_price, "¥"+bean.price)
                        .setText(R.id.tv_specification,"/"+bean.spec)
                        .setText(R.id.tv_num, "x"+bean.amount)
                        .setText(R.id.tv_price, handleSubtotalYuanAmount("小计:¥"+bean.subtotal))
                ImageHelper.with(mContext).load(AppNetConfig.LORD_IMAGE + bean.imageUrl).placeholder(R.drawable.jiazaitu_min).diskCacheStrategy(DiskCacheStrategy.SOURCE).into(ybmBaseHolder.getView(R.id.iv_pic))
                ybmBaseHolder.getConvertView().setOnClickListener { v: View? ->  //点击查看详情
                    if (!DoubleUtils.isFastDoubleClick()) {
                        RoutersUtils.open("ybmpage://productdetail?" + IntentCanst.PRODUCTID + "=" + bean.skuId)
                    }
                }
            }
        }
        val emptyView: View = layoutInflater.inflate(R.layout.layout_empty_view, cv_list.parent as ViewGroup, false)
        cv_list.setEmptyView(emptyView)
        mAdapter?.setEnableLoadMore(false)
        cv_list.setListener(object : CommonRecyclerView.Listener {
            override fun onRefresh() {
                getData()
            }
            override fun onLoadMore() {}
        })
        cv_list.isNestedScrollingEnabled = false
        mAdapter?.setNewData(list)
        cv_list.setAdapter(mAdapter)
        cv_list.layoutManager = WrapLinearLayoutManager(this)
    }

     private fun  getData() {
            showProgress()
            val params = RequestParams()
            params.put("merchantId", SpUtil.getMerchantid())
            HttpManager.getInstance().post(AppNetConfig.FREIGHT_OVER_WEIGHT_GOODS_LIST, params, object : BaseResponse<FreightOverWeightListBean>() {
                override fun onFailure(error: NetError) {
                    dismissProgress()
                    cv_list?.setRefreshing(false)
                }

                override fun onSuccess(content: String, baseBean: BaseBean<FreightOverWeightListBean>?, data: FreightOverWeightListBean?) {
                    dismissProgress()
                    cv_list?.setRefreshing(false)
                    if (baseBean!=null&&baseBean.isSuccess) {
                        tv_num.text = getString(R.string.str_freight_over_weight_goods_list_num,data?.totalCount)
                        if (data?.specialProducts!= null && data.specialProducts!!.size > 0) {
                            list.clear()
                            list.addAll(data?.specialProducts!!)
                            mAdapter?.setNewData(list)
                        }
                        mAdapter?.setEnableLoadMore(false)
                    }
                }
            })
        }
}