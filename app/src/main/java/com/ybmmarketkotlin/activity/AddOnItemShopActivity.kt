package com.ybmmarketkotlin.activity

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Typeface
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.util.SparseArray
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.apkfuns.logutils.LogUtils
import com.flyco.tablayout.listener.CustomTabEntity
import com.flyco.tablayout.listener.OnTabSelectListener
import com.github.mzule.activityrouter.annotation.Router
import com.luck.picture.lib.tools.StringUtils
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.RowsPriceDiscount
import com.ybmmarket20.bean.TabEntity
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.*
import com.ybmmarket20.utils.analysis.AnalysisConst
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.utils.analysis.shopListShopClick
import com.ybmmarketkotlin.bean.ShopItemBean
import com.ybmmarketkotlin.bean.ShopListBean
import com.ybmmarketkotlin.viewmodel.AddOnItemShopViewModel
import kotlinx.android.synthetic.main.activity_product_add_on_item.*
import kotlinx.android.synthetic.main.activity_product_add_on_item.ctl_coupon_to_use
import kotlinx.android.synthetic.main.activity_product_add_on_item.tv_coupon_amount
import kotlinx.android.synthetic.main.activity_product_add_on_item.tv_coupon_full_reduce
import kotlinx.android.synthetic.main.activity_product_add_on_item.tv_coupon_full_reduce_max
import kotlinx.android.synthetic.main.activity_product_add_on_item.tv_coupon_subtitle
import kotlinx.android.synthetic.main.activity_product_add_on_item.tv_coupon_title
import kotlinx.android.synthetic.main.activity_product_add_on_item.tv_coupon_to_use_amount
import kotlinx.android.synthetic.main.activity_product_add_on_item.tv_coupon_to_use_amount_des
import kotlinx.android.synthetic.main.activity_product_add_on_item.tv_coupon_to_use_date
import kotlinx.android.synthetic.main.activity_product_add_on_item.tv_coupon_to_use_des
import kotlinx.android.synthetic.main.activity_product_add_on_item.tv_discount_unit
import kotlinx.android.synthetic.main.activity_product_add_on_item.tv_rice_unit
import org.json.JSONObject
import java.util.ArrayList

/**
 *  优惠券凑单页 店铺维度
 */
@Router("YBMCollectOrdersVC")
class AddOnItemShopActivity : BaseActivity() {
    override fun getContentViewId() = R.layout.activity_product_add_on_item

    private lateinit var mViewModel: AddOnItemShopViewModel

    private lateinit var shopItemAdapter: ShopItemAdapter
    var shopList: MutableList<ShopItemBean?> = mutableListOf()
    var tabData: ArrayList<CustomTabEntity> = arrayListOf()
    private lateinit var loadMoreParams: RequestParams
    private lateinit var recyclerPool: RecyclerView.RecycledViewPool

    private var shopType = ""           // self 自营 、 pop POP
    private var templateId: String? = null

    private lateinit var broadcastReceiver: BroadcastReceiver

    override fun initData() {

        templateId = intent.getStringExtra("templateId") ?: ""
        mViewModel = ViewModelProvider(this).get(AddOnItemShopViewModel::class.java)

        initReceiver()

        mViewModel.couponInfoBeanLiveData.observe(this, Observer {
            tv_coupon_to_use_des.text = it.voucherInstructions
            //券名称
            tv_coupon_subtitle.text = it.voucherTitle
            //店铺名
            setTitleAndTag(
                mySelf, it.voucherType, it.shopName
                    ?: "", it.voucherTypeDesc ?: "", tv_coupon_title
            )
            //券有效期
            val time = (DateTimeUtil.getCouponDateTime2(it.validDate)
                    + "-" + DateTimeUtil.getCouponDateTime2(it.expireDate))
            tv_coupon_to_use_date.text = time
            tv_coupon_full_reduce.text = it.minMoneyToEnableDesc
            if (it.voucherState == 1) {
                tv_rice_unit.visibility = View.GONE
                tv_discount_unit.visibility = View.VISIBLE
                val amount = UiUtils.transform2Int(it.discount)
                tv_coupon_amount.text = StringUtil.setDotAfterSize(amount, 19)
                tv_discount_unit.text = "折"
            } else {
                tv_rice_unit.visibility = View.VISIBLE
                tv_discount_unit.visibility = View.GONE
                tv_coupon_amount.text = UiUtils.transformInt(it.moneyInVoucher)
                tv_rice_unit.text = "¥"
            }

            if (!TextUtils.isEmpty(it.maxMoneyInVoucherDesc)) {
                tv_coupon_full_reduce_max.visibility = View.VISIBLE
                tv_coupon_full_reduce_max.text = it.maxMoneyInVoucherDesc
            } else {
                tv_coupon_full_reduce_max.visibility = View.GONE
            }

        })
        mViewModel.voucherShopListTabBeanLiveData.observe(this, Observer { bean ->
            //region
            if (bean.havePopShop && bean.haveSelfShop) {
                ctl_coupon_to_use?.visibility = View.VISIBLE
                tabData.clear()
                tabData.add(TabEntity("自营"))
                tabData.add(TabEntity("合作商家"))
                ctl_coupon_to_use?.setTabData(tabData)
                ctl_coupon_to_use?.setCurrentTab(0)
                shopType = "self"
                ctl_coupon_to_use?.setOnTabSelectListener(object : OnTabSelectListener {
                    override fun onTabSelect(position: Int) {
                        when (position) {
                            0 -> shopType = "self"
                            1 -> shopType = "pop"
                        }
                        getData(true)
                    }

                    override fun onTabReselect(position: Int) {
                        rv_shop_list?.scrollToPosition(0)
                    }

                })
            } else {
                ctl_coupon_to_use?.visibility = View.GONE
                if (bean.haveSelfShop) shopType = "self"
                if (bean.havePopShop) shopType = "pop"
            }
            getData(true)

            shopItemAdapter = ShopItemAdapter(R.layout.item_shop_list, shopList)

            val emptyView = layoutInflater.inflate(R.layout.layout_empty_view, null)
            emptyView.layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
            shopItemAdapter.emptyView = emptyView
            shopItemAdapter.setOnLoadMoreListener({ getData(false) }, rv_shop_list)

            recyclerPool = RecyclerView.RecycledViewPool()
            rv_shop_list.adapter = shopItemAdapter
            rv_shop_list.layoutManager = LinearLayoutManager(this)

            smartrefresh.setOnRefreshListener { getData(true) }
            smartrefresh.autoRefresh()
            //endregion

            rel_search?.setOnClickListener {
                LogUtils.e("xyd shopcodes = ${bean?.shopCodeList?.joinToString(separator = ";")}")
                RoutersUtils.open("ybmpage://searchproduct?shopCodes=${bean?.shopCodeList?.joinToString(separator = ";")}&&title=${bean.searchRemindText?:""}&&isExcludePt=1")
            }

        })
        mViewModel.shopListBeanLiveData.observe(this, Observer {
            if (it.isFirst) {
                smartrefresh.finishRefresh()
                shopItemAdapter.resetTraceShop()
                dismissProgress()
            }
            updateShopListData(it.isFirst, it)

        })
        mViewModel.cartVoucherBeanLiveData.observe(this, Observer { bean ->
            val amount = "小计: ¥${StringUtil.DecimalFormat2Double(bean.selectSkuAmount)}"
            val desc = if (bean.noEnoughMoney <= 0) {
                //已经满足优惠条件,可立减¥xxx
                "${resources.getString(R.string.coupon_amount_des1)}${StringUtil.DecimalFormat2Double(bean.moneyInVoucher)}"
            } else {
                //再买¥xxx 可使用优惠券
                "${resources.getString(R.string.buy_again_with_symbol)}${StringUtil.DecimalFormat2Double(bean.noEnoughMoney)}${resources.getString(R.string.can_use_coupon)}"
            }
            tv_coupon_to_use_amount.text = amount
            tv_coupon_to_use_amount_des.text = desc
        })
        templateId?.let {
            mViewModel.getCouponTemplate(SpUtil.getMerchantid(), templateId!!)
            mViewModel.getCartVoucherBean(SpUtil.getMerchantid(), templateId!!)
            mViewModel.getVoucherShopListTab(SpUtil.getMerchantid(), templateId!!)
        }
        //跳转到购物车
        tv_coupon_to_cart?.setOnClickListener {
            RoutersUtils.open("ybmpage://main?tab=2&name=凑单页&id=${RoutersUtils.encodeRAWUrl("ybmpage://YBMCollectOrdersVC?templateId=${templateId}")}")
        }

    }


    /**
     * 初始化广播接收器
     */
    private fun initReceiver() {
        broadcastReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent?) {
                intent?.takeIf { it.action == IntentCanst.ACTION_ADD_PRODUCT || it.action == IntentCanst.ACTION_ADD_PRODUCT_FROM_DETAIL_TO_COUPON }?.let {
                    mViewModel.getCartVoucherBean(SpUtil.getMerchantid(), templateId!!)
                }
            }
        }
        LocalBroadcastManager.getInstance(applicationContext).registerReceiver(broadcastReceiver, IntentFilter().apply {
            addAction(IntentCanst.ACTION_ADD_PRODUCT)
            addAction(IntentCanst.ACTION_ADD_PRODUCT_FROM_DETAIL_TO_COUPON)  // 接收详情页加购消息更新小计
        })
    }

    override fun onDestroy() {
        super.onDestroy()
        LocalBroadcastManager.getInstance(applicationContext).unregisterReceiver(broadcastReceiver)
    }

    private fun getData(isFirst: Boolean) {
        if (TextUtils.isEmpty(shopType)) {
            smartrefresh.finishRefresh()
            return
        }
        if (isFirst) {
            rv_shop_list.smoothScrollToPosition(0)
            showProgress()
        }
        mViewModel.getVoucherShopListBean(getRequestParams(isFirst).paramsMap, isFirst)
    }

    private fun updateShopListData(isFirst: Boolean, shopListBean: ShopListBean) {
        loadMoreParams = shopListBean.requestParams
        AdapterUtils.notifyAndControlLoadmoreStatus(shopListBean.dataList, shopItemAdapter, isFirst, shopListBean.isEnd)
        // 请求并更新折后价
        getAndUpdateDiscoutPrice(shopListBean.shopListInfo, shopItemAdapter)

    }

    private fun getAndUpdateDiscoutPrice(shopInfoList: MutableList<ShopItemBean>, shopItemAdapter: ShopItemAdapter) {

        // 如果更新的列表为null则返回
        if (shopInfoList == null || shopItemAdapter?.data == null) return

        // 组建获取折后价的入参
        val rowsIds = StringBuffer()
        shopInfoList?.forEach {
            it?.productInfo?.forEach {
                rowsIds.append(it.id).append(",")
            }
        }
        if (TextUtils.isEmpty(rowsIds)) return
        rowsIds.deleteCharAt(rowsIds.length - 1)

        // 获取折后价
        val requestParams = RequestParams()
        requestParams.put("skuIds", rowsIds.toString())
        requestParams.url = AppNetConfig.LIST_PRODUCT_DISCOUNT
        HttpManager.getInstance().post(requestParams, object : BaseResponse<List<RowsPriceDiscount?>?>() {
            override fun onSuccess(content: String?, obj: BaseBean<List<RowsPriceDiscount?>?>?, rowsPriceDiscounts: List<RowsPriceDiscount?>?) {
                if (obj != null && !rowsPriceDiscounts.isNullOrEmpty()) {

                    rowsPriceDiscounts.forEach {
                        it?.let {
                            var skuid = it.skuId
                            var price = it.price
                            for (shoplistItem in shopItemAdapter.data as MutableList<ShopItemBean>) {
                                for (rowItem in shoplistItem.productInfo ?: mutableListOf()) {
                                    if (skuid == rowItem.id) {
                                        rowItem.showPriceAfterDiscount = price
                                    }
                                }
                            }
                        }
                    }
                    shopItemAdapter.notifyDataSetChanged()

                }
            }
        })


    }

    private fun getRequestParams(isFirst: Boolean): RequestParams {
        var requestParams: RequestParams
        if (isFirst) {
            requestParams = RequestParams().apply {
                put("shopPropertyCode", shopType)
                put("voucherTemplateId", templateId)
                put("merchantId", SpUtil.getMerchantid())
            }
        } else {
            requestParams = loadMoreParams
        }
        return requestParams
    }

    inner class ShopItemAdapter : YBMBaseAdapter<ShopItemBean?> {

        private val traceShopData = SparseArray<String>()

        constructor(layoutResId: Int, data: MutableList<ShopItemBean?>) : super(layoutResId, data)

        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: ShopItemBean?) {

            t?.let {
                // 店铺信息
                baseViewHolder?.setText(R.id.tv_shopname, it.showName)
                baseViewHolder?.getView<ImageView>(R.id.iv_shop_logo)
                    ?.let { iv -> ImageUtil.loadRoundCornerImage(this@AddOnItemShopActivity, AppNetConfig.CDN_HOST + t.appLogo, iv, 4) }
                baseViewHolder?.getView<TextView>(R.id.tv_onsale)?.text = getActivityStr(it.shelvesDesc, it.shelves.toString())
                baseViewHolder?.getView<TextView>(R.id.tv_sold)?.text = getActivityStr(
                    it.salesVolumeDesc, it.salesVolume
                        ?: ""
                )
                baseViewHolder?.getView<TextView>(R.id.tv_pingkage)?.text = it.freightTips


                val tvCoupon = baseViewHolder?.getView<TextView>(R.id.tv_coupon)
                val tvActive = baseViewHolder?.getView<TextView>(R.id.tv_active)

                tvCoupon?.visibility = View.GONE
                tvActive?.visibility = View.GONE

                it.activityInfo?.forEach {
                    when (it.activityType) {
                        1 -> baseViewHolder?.getView<TextView>(R.id.tv_coupon)?.apply {
                            visibility = View.VISIBLE
                            text = it.activityContent
                            val drawable =
                                ContextCompat.getDrawable(mContext, R.drawable.icon_shop_coupon)?.apply { setBounds(0, 0, minimumWidth, minimumHeight) }
                            StringUtils.modifyTextViewDrawable(this, drawable, 0)
                            compoundDrawablePadding = UiUtils.dp2px(4)
                        }

                        4 -> baseViewHolder?.getView<TextView>(R.id.tv_active)?.apply {
                            visibility = View.VISIBLE
                            text = it.activityContent
                            val drawable = ContextCompat.getDrawable(mContext, R.drawable.icon_shop_cu)?.apply { setBounds(0, 0, minimumWidth, minimumHeight) }
                            StringUtils.modifyTextViewDrawable(this, drawable, 0)
                            compoundDrawablePadding = UiUtils.dp2px(4)
                        }
                    }

                }
                baseViewHolder?.setOnClickListener(R.id.cl_shop_info) {
                    RoutersUtils.open(t.newAppLink)
                    shopListShopClick(AnalysisConst.ShopList.SHOP_LIST_SHOP_CLICK, t.shopPatternCode, t.orgId)
                }

                var rvShopProduct = baseViewHolder?.getView<RecyclerView>(R.id.rv_shop_product)
                val shopProductItemAdapter = ShopProductItemAdapter(R.layout.item_shop_product_list, t.productInfo?.take(3)?.toMutableList())
                rvShopProduct?.setRecycledViewPool(recyclerPool)
                rvShopProduct?.adapter = shopProductItemAdapter
                rvShopProduct?.layoutManager = GridLayoutManager(mContext, 3)
                baseViewHolder?.let { holder ->
                    if (traceShopData.get(holder.adapterPosition) == null) {
                        XyyIoUtil.track(AnalysisConst.ShopList.SHOP_LIST_SHOP_EXPOSURE, JSONObject().apply {
                            put("storetype", it.shopPatternCode)
                            put("id", it.orgId)
                        })
                        traceShopData.put(holder.adapterPosition, t.orgId)
                    }
                }
            }
        }

        private fun getActivityStr(shelvesDesc: String?, toString: String): CharSequence? {
            val activityStr = SpannableStringBuilder()
            activityStr.append(shelvesDesc?.replace("xxx", toString, true))
            val startIndex = shelvesDesc?.indexOf("xxx") ?: 0
            val endIndex = startIndex + toString.length
            activityStr.setSpan(ForegroundColorSpan(UiUtils.getColor(R.color.text_00B377)), startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            return activityStr
        }

        /**
         * 清空埋点缓存数据
         */
        fun resetTraceShop() {
            traceShopData.clear()
        }

    }

    inner class ShopProductItemAdapter : YBMBaseAdapter<RowsBean> {
        constructor(layoutResId: Int, data: MutableList<RowsBean>?) : super(layoutResId, data)

        override fun bindItemView(baseViewHolder: YBMBaseHolder, t: RowsBean?) {
            val ivIcon: ImageView? = baseViewHolder.getView<ImageView>(R.id.iv_product)
            val tvPriceAfterDiscount = baseViewHolder.getView<TextView>(R.id.tv_price_after_discount)
            val tvPrice = baseViewHolder.getView<TextView>(R.id.tv_price)

            ivIcon?.let { ImageUtil.load(baseViewHolder.getConvertView().context, AppNetConfig.LORD_IMAGE + t?.imageUrl, ivIcon) }
            baseViewHolder.setOnClickListener(R.id.ll_product_item, { RoutersUtils.open("ybmpage://productdetail/${t?.productId}") })

            /*
             *
             *   0 : 正常展示价格
             *   1 ： 展示 "暂无购买权限"
             *   2 :  展示  "价格签署协议可见"
             *   3 :  展示  "价格认证资质可见"
             *  */
            when (t?.showPriceType()) {

                0 -> {
                    tvPrice?.typeface = Typeface.DEFAULT_BOLD
                    tvPrice?.textSize = 10f
                    tvPrice?.text = t.showPriceStr
                    tvPriceAfterDiscount?.visibility = View.VISIBLE
                    // 正常展示价格中的 折后价
                    if (!TextUtils.isEmpty(t.showPriceAfterDiscount)) {
                        tvPriceAfterDiscount?.text = t.showPriceAfterDiscount
                        tvPriceAfterDiscount?.visibility = View.VISIBLE
                    } else {
                        tvPriceAfterDiscount?.visibility = View.GONE
                    }
                }
                -1 -> {
                    tvPrice?.typeface = Typeface.DEFAULT
                    tvPrice?.textSize = 13f
                    tvPrice?.text = t.showPriceStr
                    tvPriceAfterDiscount?.visibility = View.GONE
                }
            }

        }

    }

}