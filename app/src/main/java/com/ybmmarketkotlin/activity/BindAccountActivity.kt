package com.ybmmarketkotlin.activity

import android.app.Activity
import android.content.Intent
import android.view.View
import android.widget.EditText
import android.widget.TextView
import butterknife.Bind
import butterknife.OnClick
import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.LoginInfo
import com.ybmmarket20.bean.RegisterStatusBean
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.ViewOnClickListener
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.TimeCountUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.view.ButtonObserver
import kotlinx.android.synthetic.main.activity_bind_account.*
import kotlinx.android.synthetic.main.common_header_items.*

/**
 * <AUTHOR>
 * @desc    微信授权->账号绑定
 * @date 2025/6/18
 */
class BindAccountActivity : BaseActivity() {
    var accessToken: String? = null
    var openId: String? = null
    var unionid: String? = null
    var mobile: String? = ""
    var checkTime: String = ""

    @Bind(R.id.etVCode)
    internal lateinit var etVCode: EditText

    @Bind(R.id.etMobile)
    internal lateinit var etMobile: EditText

    @Bind(R.id.tvGetVCode)
    internal lateinit var tvGetVCode: TextView

    @Bind(R.id.btnBind)
    internal lateinit var btnBind: ButtonObserver

    companion object {
        fun getStartIntent(
            mcontext: Activity,
            accessToken: String,
            openId: String,
            unionid: String,
            mobile: String? = "",
            checkTime: String? = ""
        ): Intent = Intent(mcontext, BindAccountActivity::class.java).apply {
            putExtra("accessToken", accessToken)
            putExtra("openId", openId)
            putExtra("unionid", unionid)
            putExtra("mobile", mobile)
            putExtra("checkTime", checkTime)
        }
    }

    override fun getContentViewId(): Int = R.layout.activity_bind_account

    override fun initData() {
        setTitle("绑定账号")
        accessToken = intent.getStringExtra("accessToken")
        openId = intent.getStringExtra("openId")
        unionid = intent.getStringExtra("unionid")
        mobile = intent.getStringExtra("mobile")
        checkTime = intent.getStringExtra("checkTime") ?: ""
        btnBind.observer(etVCode, etMobile)
        btnBind.setOnItemClickListener { btnBind.isEnabled = it }
        if(!mobile.isNullOrEmpty()){
            etMobile.isEnabled = false
            etMobile.setText(mobile)
        }
        iv_back.setOnClickListener {
            onBackPressed()
        }

        tvGetVCode.setOnClickListener {
            sendVertifycode()
        }
    }

    @OnClick(R.id.btnBind)
    fun clickView(view: View?) {
        when (view?.id) {
            R.id.btnBind -> {
                bindAccount()
            }
        }
    }

    private fun bindAccount() {
        showProgress()
        val params = RequestParams()
        params.put("phone", etMobile.text?.trim()?.toString() ?: "")
        params.put("code", etVCode.text?.trim()?.toString() ?: "")
        params.put("openid", openId)
        params.put("accessToken", accessToken)
        HttpManager.getInstance().post(AppNetConfig.BIND_ACCOUNT, params, object : BaseResponse<LoginInfo?>() {
            override fun onSuccess(content: String?, obj: BaseBean<LoginInfo?>?, t: LoginInfo?) {
                dismissProgress()
                if (obj?.isSuccess == true) {
                    if (t?.status == 1) {
                        // intent中 协议时间、手机号为空 ：设置：绑定成功，结束
                        if (checkTime.isNullOrEmpty() && mobile.isNullOrEmpty()) {
                            val intent = Intent().apply {
                                putExtra(IntentCanst.BIND_ACCOUNT, true)
                            }
                            setResult(RESULT_OK, intent)
                            finish()
                        } else {
                            // 登录微信绑定成功，回去登录
                            val intent = Intent().apply {
                                putExtra(IntentCanst.BIND_ACCOUNT, true)
                            }
                            setResult(RESULT_OK, intent)
                            finish()
                        }
                    }else{
                        showDialogBind()
                    }
                }
            }

            override fun onFailure(error: NetError?) {
                dismissProgress()
            }
        })
    }

    private fun sendVertifycode() {
        showProgress()
        val params = RequestParams()
        params.put("phone", etMobile.text?.trim()?.toString() ?: "")
        HttpManager.getInstance().post(AppNetConfig.BIND_ACCOUNT_VCODE, params, object : BaseResponse<RegisterStatusBean?>() {
            override fun onSuccess(content: String?, obj: BaseBean<RegisterStatusBean?>?, t: RegisterStatusBean?) {
                dismissProgress()
                if (obj?.isSuccess == true) {
                    if (t?.status == 1) {
                        TimeCountUtil(tvGetVCode, 60000, 1000)
                            .setCountdownListener(object : TimeCountUtil.CountdownListener {
                                override fun onCountdownIngListener(view: TextView?, secondsNum: String) {
                                    view?.text = "已发送(${secondsNum})"
                                    view?.isEnabled = false
                                }

                                override fun onCountdownFinishListener(view: TextView?) {
                                    view?.text = "重新获取"
                                    view?.isEnabled = true
                                }
                            }).start()
                    } else {
                        showDialogBind()
                    }
                }
            }

            override fun onFailure(error: NetError?) {
                dismissProgress()
            }
        })
    }

    var dialogBind: AlertDialogEx? = null
    private fun showDialogBind() {
        dialogBind = AlertDialogEx(this)
            .setTitle(title)
            .setMessage("账号未注册，是否注册新账号？")
            .setCanceledOnTouchOutside(false)
            .setCancelButton("取消", ViewOnClickListener { dialog: AlertDialogEx?, button: Int ->
                dialog!!.dismiss()
            })
            .setCancelButtonTextColor(UiUtils.getColor(R.color.text_color_333333))
            .setConfirmButton("注册", ViewOnClickListener { dialog: AlertDialogEx?, button: Int ->
                RoutersUtils.open("ybmpage://register/${accessToken}/${openId}")
                dialog!!.dismiss()
            })
            .setCorner()
        dialogBind?.show()
    }

    override fun onDestroy() {
        dialogBind?.dismiss()
        super.onDestroy()
    }
}