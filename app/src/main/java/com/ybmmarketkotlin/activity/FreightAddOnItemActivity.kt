package com.ybmmarketkotlin.activity

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import android.view.View
import butterknife.OnClick
import com.github.mzule.activityrouter.annotation.Router
import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.FreightAddOnItemSubtotal
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.StringUtil
import com.ybmmarket20.view.FreightTipDialog
import com.ybmmarketkotlin.fragments.FreightAddOnItemFragment
import kotlinx.android.synthetic.main.activity_freight_add_on_item.*

/**
 * 凑单包邮 去掉顶部的tab标签页实现
 */
//const val FREIGHT_ADD_ON_ITEM_ALL= 1
//const val FREIGHT_ADD_ON_ITEM_SALE= 2
//const val FREIGHT_ADD_ON_ITEM_POST= 3
const val ACTIVITY_TYPE_FROM_CART = "activity_type_from_cart"
const val ACTIVITY_TYPE_FROM_PAYMENT = "activity_type_from_payment"

@Deprecated("一仓卖全国需求删除入口")
@Router("freightaddonitem", "freightaddonitem/:activityType")
class FreightAddOnItemActivity : BaseActivity() {
    private var freightAddOnItemFragment: FreightAddOnItemFragment = FreightAddOnItemFragment()
    private lateinit var broadcastReceiver: BroadcastReceiver
    private var isFromCart: Boolean = false//是否来自购物车凑单
    private var isAddGoods: Boolean = false//是否加购商品了
    override fun getContentViewId(): Int {
        return R.layout.activity_freight_add_on_item
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setLeft(this::onClick)
    }

    override fun initData() {
        setTitle("凑单包邮")
        setRigthText({
            //包邮感叹号点击
            FreightTipDialog(this).showTip()
        }, "运费规则")
        addFragment()
        initReceiver()
        val activityType = intent.getStringExtra("activityType");
        isFromCart = ACTIVITY_TYPE_FROM_CART == activityType
    }

    override fun onResume() {
        super.onResume()
        getSubtotalData()
    }

    /**
     * 添加fragment
     */
    private fun addFragment() {
        supportFragmentManager.beginTransaction().apply {
            add(R.id.fl_container, freightAddOnItemFragment)
            commit()
        }
    }

    /**
     * 初始化广播接收器
     */
    private fun initReceiver() {
        broadcastReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent?) {
                intent?.let {
                    when {
                        IntentCanst.ACTION_ADD_PRODUCT == it.action -> {//列表页加购消息更新小计
                            getSubtotalData()
                            isAddGoods = true
                        }
                        // 接收详情页加购消息更新小计
                        IntentCanst.ACTION_ADD_PRODUCT_FROM_DETAIL_TO_COUPON == it.action -> {
                            getSubtotalData()
                            isAddGoods = true
                        }
                    }
                }
            }
        }
        LocalBroadcastManager.getInstance(applicationContext).registerReceiver(broadcastReceiver, IntentFilter().apply {
            addAction(IntentCanst.ACTION_ADD_PRODUCT)
            addAction(IntentCanst.ACTION_ADD_PRODUCT_FROM_DETAIL_TO_COUPON)
        })
    }

    /**
     * 获取金额数据
     */
    private fun getSubtotalData() {
        val merchantId = SpUtil.getMerchantid()
        val params = RequestParams().apply {
            put("merchantId", merchantId)
        }
        HttpManager.getInstance().post(if (isFromCart) AppNetConfig.FREIGHT_CART_ADD_ON_ITEM_AMOUNT_SUBTOTAL else AppNetConfig.FREIGHT_SETTLE_ADD_ON_ITEM_AMOUNT_SUBTOTAL, params, object : BaseResponse<FreightAddOnItemSubtotal>() {
            override fun onSuccess(content: String?, baseBean: BaseBean<FreightAddOnItemSubtotal>?, data: FreightAddOnItemSubtotal?) {
                super.onSuccess(content, baseBean, data)
                if (baseBean != null && baseBean.isSuccess && data != null) {
                    tv_amount.text = getString(R.string.str_freight_add_on_item_subtotal, StringUtil.DecimalFormat2Double(data.subtotal))
                    tv_amount_des.text = data.freightTips
                    tv_amount_des.visibility = if (data.freightTipsShowStatus == 1) View.VISIBLE else View.GONE
                }
            }

            override fun onFailure(error: NetError) {}
        })
    }

    @OnClick(R.id.tv_to_cart, R.id.iv_back)
    fun onClick(v: View?) {
        when (v?.id) {
            //返回
            R.id.iv_back -> {
                if (isAddGoods) {
                    setResult(Activity.RESULT_OK)
                }
                finish()
            }
            //去购物车
            R.id.tv_to_cart -> {
                setResult(Activity.RESULT_OK)
                finish()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        LocalBroadcastManager.getInstance(applicationContext).unregisterReceiver(broadcastReceiver)
    }
}