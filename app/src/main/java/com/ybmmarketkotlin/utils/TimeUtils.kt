package com.ybmmarketkotlin.utils

import java.text.SimpleDateFormat
import java.util.*

object TimeUtils {

    /**
     * @param millisUntilFinished 剩余时间
     *
     * @return dd天 HH:MM:mm
     *
     */
    fun timeFormat(millisUntilFinished: Long): String {
        val d = millisUntilFinished / 1000 / 3600 / 24
        val h = millisUntilFinished / 1000 / 3600 % 24
        val m = millisUntilFinished / 1000 / 60 % 60
        val s = millisUntilFinished / 1000 % 60

        val dd = if (d > 0) {
            "${d}天"
        } else {
            ""
        }
        val hh = if (h < 10) {
            "0$h"
        } else {
            "" + h
        }
        val mm = if (m < 10) {
            "0$m"
        } else {
            "" + m
        }
        val ss = if (s < 10) {
            "0$s"
        } else {
            "" + s
        }
        return "$dd$hh:$mm:$ss"
    }

    /**
     * @param millisUntilFinished 剩余时间
     *
     * @return dd天 HH小时MM分
     *
     */
    fun timeFormatToMinute(millisUntilFinished: Long): String {
        val d = millisUntilFinished / 1000 / 3600 / 24
        val h = millisUntilFinished / 1000 / 3600 % 24
        val m = millisUntilFinished / 1000 / 60 % 60
        val s = millisUntilFinished / 1000 % 60

        val dd = if (d > 0) {
            "${d}天"
        } else {
            ""
        }
        val hh = if (h > 0) {
            "${h}小时"
        } else {
            ""
        }
        var mm = if (m > 0) {
            "${m}分"
        } else {
            ""
        }
        if (m == 0L && s > 0L) {
            mm = "1分"
        }

        val ss = if (s > 0) {
            "${s}秒"
        } else {
            ""
        }
        return "$dd$hh$mm"
    }

    /**
     * @param millisUntilFinished 剩余时间
     *
     * @return  HH小时MM分
     *
     */
    fun timeFormatToMinute1(millisUntilFinished: Long): String {
        val d = millisUntilFinished / 1000 / 3600
        val m = millisUntilFinished / 1000 / 60 % 60
        val s = millisUntilFinished / 1000 % 60


        val hh = if (d > 0) {
            "${d}小时"
        } else {
            ""
        }
        var mm = if (m > 0) {
            "${m}分"
        } else {
            ""
        }
        if (m == 0L && s > 0L) {
            mm = "1分"
        }

        return "$hh$mm"
    }


    fun timeFormatToMinuteWithUnit(millisUntilFinished: Long): String {
        val d = millisUntilFinished / 1000 / 3600 / 24
        val h = millisUntilFinished / 1000 / 3600 % 24
        val m = millisUntilFinished / 1000 / 60 % 60
        val s = millisUntilFinished / 1000 % 60

        val dd = if (d > 0) {
            "${d}天"
        } else {
            ""
        }
        val hh = if (h > 0) {
            "${h}小时"
        } else {
            ""
        }
        var mm = if (m > 0) {
            "${m}分"
        } else {
            ""
        }

        val ss = if (s > 0) {
            "${s}秒"
        } else {
            ""
        }
        return "$dd$hh$mm$ss"
    }

    /**
     * 运营位倒计时 时间格式化
     */
    fun timeFormatForOP(millisUntilFinished: Long): OPTime {

        val h = millisUntilFinished / 1000 / 3600
        val m = millisUntilFinished / 1000 / 60 % 60
        val s = millisUntilFinished / 1000 % 60

        val hh = if (h < 10) {
            "0$h"
        } else {
            "$h"
        }
        val mm = if (m < 10) {
            "0$m"
        } else {
            "$m"
        }
        val ss = if (s < 10) {
            "0$s"
        } else {
            "$s"
        }
        return OPTime(hh, mm, ss, h)
    }

    /**
     * 格式化时间戳
     */
    fun getFormatTimeFromTimeStampForYMD(time: Long): String {
        val date = Date(time)
        val strDateFormat = "yyyy/MM/dd"
        val sdf = SimpleDateFormat(strDateFormat)
        return sdf.format(date)
    }

    fun getFormatTime(time: Long): String {
        val date = Date(time)
        val strDateFormat = "yyyy.MM.dd HH:mm:ss"
        val sdf = SimpleDateFormat(strDateFormat)
        return sdf.format(date)
    }

    fun getFormatTime1(time: Long): String {
        val date = Date(time)
        val strDateFormat = "yyyy-MM-dd HH:mm:ss"
        val sdf = SimpleDateFormat(strDateFormat)
        return sdf.format(date)
    }

    fun getYear(time: Long): String = SimpleDateFormat("yyyy").format(Date(time))

    fun getMonth(time: Long): String = SimpleDateFormat("MM").format(Date(time))

    fun getDateFormatTimestamp(timestamp: Long): Date {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = timestamp
        return calendar.time
    }

    fun isSameData(timestamp1: Long, timestamp2: Long): Boolean {
        val date1 = getDateFormatTimestamp(timestamp1)
        val date2 = getDateFormatTimestamp(timestamp2)
        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val dateStr1 = sdf.format(date1)
        val dateStr2 = sdf.format(date2)
        return dateStr1 == dateStr2
    }

}

data class OPTime(
    val hour: String,
    val minute: String,
    val second: String,
    val hourLong: Long
)