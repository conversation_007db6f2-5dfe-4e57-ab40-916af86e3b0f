package com.ybmmarketkotlin.utils

import android.graphics.Rect
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.widget.ScrollView
import androidx.core.widget.NestedScrollView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybmmarket20.R
import java.lang.Exception

/**
 * 列表item可见性曝光埋点工具，目前仅支持recyclerview
 * 使用方法：调用initExposureSetting方法进行注册，需要recyclerview设置adapter之后再进行注册
 * 注意事项：1、工具会自动解注册，注册后无需关注
 * 规则：https://wiki.int.ybm100.com/pages/viewpage.action?pageId=457875186
 */
object ExposureManager : View.OnAttachStateChangeListener,
    RecyclerView.OnScrollListener() {

    private const val logEnable = false

    private val registerMap: MutableMap<Int, ExposureTarget> = mutableMapOf()

    // 可见性的百分比，item超过这个比例才会认为是可见
    private const val visiblePercent = 0.8f

    private val handler: Handler = Handler(Looper.getMainLooper())

    private var eatResetTargetCount = 0


    fun registerExposureListener(recyclerView: RecyclerView?, exposureCallback: ExposureCallback?) {
        if (checkParams(recyclerView, exposureCallback)) {
            initExposureSetting(recyclerView!!, exposureCallback!!)
        }
    }

    fun increaseEatResetTargetCount() {
        eatResetTargetCount++
    }


    private fun checkParams(
        recyclerView: RecyclerView?,
        exposureCallback: ExposureCallback?
    ): Boolean {
        if (recyclerView == null) {
            log("recyclerView为空")
            return false
        }
        if (exposureCallback == null) {
            log("exposureCallback为空")
            return false
        }

        if (recyclerView.layoutManager !is LinearLayoutManager) {
            log("layoutManager必须为LinearLayoutManager")
            return false
        }
        return true
    }

    private fun log(msg: String) {
        if (logEnable) {
            Log.e("guan", "================================${msg}================================")
        }
    }


    private fun initExposureSetting(
        recyclerView: RecyclerView,
        exposureCallback: ExposureCallback
    ) {


        // 重置监听
        recyclerView.removeOnAttachStateChangeListener(this)
        recyclerView.removeOnScrollListener(this)
        val tagObject = recyclerView.getTag(R.id.exposure_view_data_changed_id)
        if (tagObject != null && tagObject is RecyclerView.AdapterDataObserver) {
            try {
                recyclerView.adapter?.unregisterAdapterDataObserver(tagObject)
            } catch (ignore: Exception) {

            }
        }

        if (recyclerView.isAttachedToWindow) {
            initExposureSettingInternal(recyclerView, exposureCallback)
        } else {
            // 先把callback挂在view上，等待view注册到window上再缓存到registerMap里
            recyclerView.setTag(R.id.exposure_view_callback_id, exposureCallback)
        }


        recyclerView.addOnAttachStateChangeListener(this)
        recyclerView.addOnScrollListener(this)


    }

    private fun initExposureSettingInternal(
        recyclerView: RecyclerView,
        exposureCallback: ExposureCallback
    ) {
        val exposureTarget = ExposureTarget(recyclerView, exposureCallback)
        registerMap[recyclerView.hashCode()] = exposureTarget

        try {
            val adapterDataObserver = object : RecyclerView.AdapterDataObserver() {
                override fun onChanged() {
                    super.onChanged()
                    if (eatResetTargetCount > 0) {
                        exposureTarget.alreadyExposureIndexList.clear()
                        log("guan adapterDataObserver onChanged")
                        detectTargetExposure(exposureTarget)
                        eatResetTargetCount--
                    }
                }
            }
            recyclerView.adapter?.registerAdapterDataObserver(adapterDataObserver)
            recyclerView.setTag(R.id.exposure_view_data_changed_id, adapterDataObserver)
        } catch (ignore: Exception) {
        }
        detectAllTargetExposure()
    }


    private fun unregisterExposureListener(recyclerView: RecyclerView) {
        recyclerView.removeOnAttachStateChangeListener(this)
        recyclerView.setTag(R.id.exposure_view_callback_id, null)
        registerMap.remove(recyclerView.hashCode())
    }

    override fun onViewAttachedToWindow(attachedView: View) {
        log(
            "onViewAttachedToWindow:${attachedView.hashCode()}"
        )
        if (attachedView is RecyclerView) {
            val tagData = attachedView.getTag(R.id.exposure_view_callback_id)
            if (tagData is ExposureCallback) {
                initExposureSettingInternal(attachedView, tagData)
            }
        }
    }

    override fun onViewDetachedFromWindow(detachedView: View) {
        log(
            "onViewDetachedFromWindow:${detachedView.hashCode()}"
        )
        if (detachedView is RecyclerView) {
            registerMap.remove(detachedView.hashCode())
        }
    }

    override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
        super.onScrollStateChanged(recyclerView, newState)
        log(
            "onScrollStateChanged recyclerView:${recyclerView.hashCode()} newState:${newState}"
        )
        if (newState == RecyclerView.SCROLL_STATE_IDLE) {
            detectAllTargetExposure()
        }
    }

    private fun detectAllTargetExposure() {
        handler.post {
            // 静止时，检查所有监听的recyclerView是否静止
            if (checkAllTargetScrollState()) {
                //判断所有recyclerView符合展示条件的
                registerMap.values.forEach {
                    detectTargetExposure(it)
                }
            }
        }
    }

    private fun detectTargetExposure(it: ExposureTarget) {
        val currentVisibleItemIndex = getVisibleItemIndex(it.recyclerView)
        val changeVisibleItemIndex =
            currentVisibleItemIndex.filterNot { index ->
                it.alreadyExposureIndexList.contains(index)
            }
        it.alreadyExposureIndexList.clear()
        it.alreadyExposureIndexList.addAll(currentVisibleItemIndex)
        if (changeVisibleItemIndex.isNotEmpty()) {
            it.exposureCallback.callback(changeVisibleItemIndex)
        }
    }


    /**
     * 检测View在列表中是否可见
     */
    private fun checkViewExposure(view: View): Boolean {
        var isVisible: Boolean
        val localVisibleRect = Rect().also {
            isVisible = view.getLocalVisibleRect(it)
        }


        // Activity切换会导致window可见性变化，这种情况由外部处理，框架仅处理列表内部的显示隐藏
//        if (view.windowVisibility != View.VISIBLE) {
//            return false
//        }

        // 一些异常场景
        if (!isVisible || localVisibleRect.height() <= 0 || localVisibleRect.width() <= 0) {
            return false
        }
        // 当item在window上，但是被其他view覆盖或者滚动出屏幕时
        if (localVisibleRect.top < 0
            || localVisibleRect.left < 0
            || view.height < localVisibleRect.top
            || view.width < localVisibleRect.left
        ) {
            return false
        }

        // 判断可见部分的百分比是否达标
        val visibleArea = localVisibleRect.height() * localVisibleRect.width()
        val totalArea = (view.height * view.width).toFloat()
        log(
            "checkViewExposure visibleArea:${visibleArea} totalArea:${totalArea}"
        )
        if (visibleArea / totalArea < visiblePercent) {
            return false
        }
        return true
    }


    // 获取recyclerView可见item的index列表
    private fun getVisibleItemIndex(recyclerView: RecyclerView): List<Int> {
        val visibleIndexList = mutableListOf<Int>()
        (recyclerView.layoutManager as LinearLayoutManager).let { manager ->
            val firstIndex = manager.findFirstVisibleItemPosition()
            val lastIndex = manager.findLastVisibleItemPosition()
            if (firstIndex < 0 || lastIndex < 0) {
                return@let
            }
            for (index in firstIndex until lastIndex + 1) {
                val view = manager.findViewByPosition(index) ?: continue
                if (checkViewExposure(view)) {
                    visibleIndexList.add(index)
                }
            }
        }
        return visibleIndexList
    }

    private fun checkAllTargetScrollState(): Boolean {
        registerMap.values.forEach {
            if (it.recyclerView.scrollState != RecyclerView.SCROLL_STATE_IDLE) {
                return false
            }
        }
        return true
    }

    override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
        super.onScrolled(recyclerView, dx, dy)

        // 滚动时，要移除掉已经滚动出屏幕的index
        registerMap.values.forEach {
            if (it.alreadyExposureIndexList.isEmpty()) {
                return
            }
//            if (it.recyclerView == recyclerView) {
//                return
//            }
            if (it.recyclerView.windowVisibility != View.VISIBLE) {
                return
            }
            Rect().let { rect ->
                it.recyclerView.getGlobalVisibleRect(rect)

                log(
                    "onScrolled recyclerView:${recyclerView.hashCode()} " +
                            "top:${rect.top}," +
                            "left:${rect.left}," +
                            "right:${rect.right}," +
                            "bottom:${rect.bottom}"
                )
                if (it.lastGlobalRect == null) {
                    return@forEach
                }
                // 全局绝对位置相等，即认为没有变化
                if (it.lastGlobalRect != rect) {
                    return@forEach
                }
                it.lastGlobalRect = rect
                // 获取当前展示的item
                val visibleItemIndexList = getVisibleItemIndex(it.recyclerView)
                log("visibleItemIndexList:${visibleItemIndexList.joinToString()}")
                log(
                    "alreadyExposureIndexList:${it.alreadyExposureIndexList.joinToString()}"
                )
                // 移除已经不展示的item
                it.alreadyExposureIndexList.removeAll { index ->
                    !visibleItemIndexList.contains(index)
                }
            }
        }
    }
}


data class ExposureTarget(
    val recyclerView: RecyclerView,
    val exposureCallback: ExposureCallback,
    val alreadyExposureIndexList: MutableList<Int> = mutableListOf(),
    var lastGlobalRect: Rect? = null
)


interface ExposureCallback {
    fun callback(indexList: List<Int>)
}
