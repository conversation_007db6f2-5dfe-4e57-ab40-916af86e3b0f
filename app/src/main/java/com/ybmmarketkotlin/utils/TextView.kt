package com.ybmmarketkotlin.utils

import IconTextSpan
import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.Paint
import android.graphics.drawable.GradientDrawable
import android.os.Handler
import android.os.Message
import android.os.SystemClock
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.util.SparseArray
import android.view.View
import android.widget.TextView
import androidx.annotation.MainThread
import androidx.core.util.forEach
import com.ybmmarket20.R
import com.ybmmarket20.bean.TagBean
import com.ybmmarket20.utils.UiUtils
import java.util.*

/**
 * <AUTHOR> Brin
 * @date : 2020/12/1 - 14:21
 * @Description :
 * @version
 */
@JvmOverloads
fun TextView.tagStyle(tag: TagBean?,cornerRadius:Int = 1): TextView {
    tag?.let {
        text = tag.text

        val textColorInt: Int = try {
            Color.parseColor(tag.textColor)
        } catch (e: Exception) {
            Color.parseColor("#00B377")
        }
        setTextColor(textColorInt)

        if (tag.uiStyle == 2) {
            setBackgroundResource(R.drawable.icon_bg_promotion_coupon)

        } else {
            val bgColorInt: Int = try {
                Color.parseColor(tag.bgColor)
            } catch (e: Exception) {
                Color.parseColor("#1900B377")
            }

            val borderColorInt: Int? = try {
                Color.parseColor(tag.borderColor)
            } catch (e: Exception) {
                null
            }

            val gradientDrawable = GradientDrawable()
            gradientDrawable.setColor(bgColorInt)
            gradientDrawable.cornerRadius = UiUtils.dp2px(cornerRadius).toFloat()
            borderColorInt?.let {
                gradientDrawable.setStroke(UiUtils.dp2px(1), borderColorInt)
            }
            background = gradientDrawable
        }
    }
    return this
}

fun TextView.TextWithPrefixTag(prefixTags: List<TagBean?>?, contentText: String?, maxTagNum: Int = 2,tagTextSize:Int=10,mRadiusSize:Int = 1,rightMargin:Int = 3): TextView {
    val tempContentText = contentText ?: ""
    val spannableStringBuilder = SpannableStringBuilder()
    spannableStringBuilder.append(tempContentText)

    prefixTags?.take(maxTagNum)?.reversed()?.forEach {
        spannableStringBuilder.insert(0, " ")

        spannableStringBuilder.setSpan(
            IconTextSpan(context, it?.text ?: "").apply {
                this.mTextSize = UiUtils.dp2px(tagTextSize).toFloat()
                this.mText = it?.text
                this.bgColorInt = try {
                    Color.parseColor(it?.bgColor)
                } catch (e: Exception) {
                    Color.parseColor("#1900B377")
                }
                this.txColorInt = UiUtils.getColorInt(it?.textColor)
                this.strokeColorInt = UiUtils.getColorInt(it?.borderColor)
                this.mRadius = mRadiusSize.dp
                this.mRightMargin = rightMargin.dp
//                this.strokeColorInt = UiUtils.getColorInt("#00b377")
            },
            0,
            1,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
    }

    this.text = spannableStringBuilder

    return this
}

/**
 *
 * @receiver TextView
 * @param prefixTags List<TagBean?>?
 * @param contentSpannable SpannableStringBuilder 内容的SpannableStringBuild
 * @param maxTagNum Int
 * @return TextView
 */
fun TextView.TextWithPrefixTagContentSpannable(
        prefixTags: List<TagBean?>?,
        contentSpannable: SpannableStringBuilder,
        maxTagNum: Int = 2
): TextView {

    prefixTags?.take(maxTagNum)?.reversed()?.forEach {
        contentSpannable.insert(0, " ")

        contentSpannable.setSpan(IconTextSpan(context, it?.text ?: "").apply {
            this.mTextSize = UiUtils.dp2px(10).toFloat()
            this.mText = it?.text
            this.bgColorInt = try {
                Color.parseColor(it?.bgColor)
            } catch (e: Exception) {
                Color.parseColor("#1900B377")
            }
            this.txColorInt = UiUtils.getColorInt(it?.textColor)
            this.strokeColorInt = UiUtils.getColorInt(it?.borderColor)
//                this.strokeColorInt = UiUtils.getColorInt("#00b377")
        }, 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
    }

    this.text = contentSpannable

    return this
}

fun TextView.textWithSuffixTag(prefixTags: List<TagBean?>?, contentText: String?, maxTagNum: Int = 2, textSize: Int = 10): TextView {
    val tempContentText = contentText ?: ""
    val spannableStringBuilder = SpannableStringBuilder()
    spannableStringBuilder.append(tempContentText)
    spannableStringBuilder.append("  ")

    prefixTags?.take(maxTagNum)?.reversed()?.forEach {

        spannableStringBuilder.setSpan(
            IconTextSpan(context, it?.text ?: "").apply {
                this.mTextSize = UiUtils.dp2px(textSize).toFloat()
                this.mText = it?.text
                this.bgColorInt = try {
                    Color.parseColor(it?.bgColor)
                } catch (e: Exception) {
                    Color.parseColor("#1900B377")
                }
                this.txColorInt = UiUtils.getColorInt(it?.textColor)
                this.strokeColorInt = UiUtils.getColorInt(it?.borderColor)
//                this.strokeColorInt = UiUtils.getColorInt("#00b377")
            },
            spannableStringBuilder.length - 1,
            spannableStringBuilder.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
    }

    this.text = spannableStringBuilder

    return this
}

fun TextView.textWithSuffixTag(contentText: SpannableStringBuilder?, text : String): TextView {
    val tempContentText = contentText ?: ""
    val spannableStringBuilder = SpannableStringBuilder()
    spannableStringBuilder.append(" ")
    spannableStringBuilder.setSpan(
        IconTextSpan(context, text).apply {
            this.mTextSize = UiUtils.dp2px(10).toFloat()
            this.mText = text
            this.bgColorInt = try {
                Color.parseColor("#F94632")
            } catch (e: Exception) {
                Color.parseColor("#F94632")
            }
            this.txColorInt = UiUtils.getColorInt("#FEFFFF")
        },
        spannableStringBuilder.length - 1,
        spannableStringBuilder.length,
        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
    )
    spannableStringBuilder.append(" ")
    spannableStringBuilder.append(tempContentText)

    this.text = spannableStringBuilder

    return this
}



fun TextView.addCountDown(
    expireTime: Long? = 0L,
    preString: String? = "",
    endString: String? = "",
    endLister: () -> Unit
): TextView {
    if (expireTime ?: 0 < 0) return this
    val intervalValue = 1000L
    expireTime?.takeIf { expireTime > 0 }?.let {
        text = "${preString ?: ""} ${TimeUtils.timeFormatToMinute(it)}${endString ?: ""}"
        val localRealtime = SystemClock.elapsedRealtime()
        // 添加倒计时组件中
        TimeCountDown.get().addCountView(this, object : TimeCountDown.OnIntervalListener {
            override fun onNext() {
                if ((expireTime - SystemClock.elapsedRealtime() + localRealtime) < intervalValue) {
                    text = "${preString ?: ""} ${TimeUtils.timeFormatToMinute(0L)}${endString ?: ""}"
                    TimeCountDown.get().removeCountView(this@addCountDown)
                    // 接受倒计时通知，更新内容
                    endLister.invoke()
                } else {
                    text = "${preString ?: ""} ${TimeUtils.timeFormatToMinute(expireTime - SystemClock.elapsedRealtime() + localRealtime)}${endString ?: ""}"
                }
            }
        })
    }
    // 在recyclerview中的复用问题未解决
    addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
        override fun onViewAttachedToWindow(v: View) {
            // LogUtils.e("xyd attached")
        }

        override fun onViewDetachedFromWindow(v: View) {
            // LogUtils.e("xyd detached")
            TimeCountDown.get().removeCountView(this@addCountDown)
        }

    })
    return this
}
// 格式；HH小时MM分
fun TextView.addCountDown1(
    expireTime: Long? = 0L,
    preString: String? = "",
    endString: String? = "",
    endLister: () -> Unit
): TextView {
    if (expireTime ?: 0 < 0) return this
    val intervalValue = 1000L
    expireTime?.takeIf { expireTime > 0 }?.let {
        text = "${preString ?: ""} ${TimeUtils.timeFormatToMinute1(it)}${endString ?: ""}"
        val localRealtime = SystemClock.elapsedRealtime()
        // 添加倒计时组件中
        TimeCountDown.get().addCountView(this, object : TimeCountDown.OnIntervalListener {
            override fun onNext() {
                if ((expireTime - SystemClock.elapsedRealtime() + localRealtime) < intervalValue) {
                    text = "${preString ?: ""} ${TimeUtils.timeFormatToMinute1(0L)}${endString ?: ""}"
                    TimeCountDown.get().removeCountView(this@addCountDown1)
                    // 接受倒计时通知，更新内容
                    endLister.invoke()
                } else {
                    text = "${preString ?: ""} ${TimeUtils.timeFormatToMinute1(expireTime - SystemClock.elapsedRealtime() + localRealtime)}${endString ?: ""}"
                }
            }
        })
    }
    // 在recyclerview中的复用问题未解决
    addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
        override fun onViewAttachedToWindow(v: View) {
            // LogUtils.e("xyd attached")
        }

        override fun onViewDetachedFromWindow(v: View) {
            // LogUtils.e("xyd detached")
            TimeCountDown.get().removeCountView(this@addCountDown1)
        }

    })
    return this
}

fun TextView.addCountDownWithUnit(
    expireTime: Long? = 0L,
    preString: String? = "",
    endString: String? = "",
    endLister: () -> Unit
): TextView {
    if (expireTime ?: 0 < 0) return this
    val intervalValue = 1000L
    expireTime?.takeIf { expireTime > 0 }?.let {
        text = "${preString ?: ""} ${TimeUtils.timeFormatToMinuteWithUnit(it)}${endString ?: ""}"
        val localRealtime = SystemClock.elapsedRealtime()
        // 添加倒计时组件中
        TimeCountDown.get().addCountView(this, object : TimeCountDown.OnIntervalListener {
            override fun onNext() {
                if ((expireTime - SystemClock.elapsedRealtime() + localRealtime) < intervalValue) {
                    text = "${preString ?: ""} ${TimeUtils.timeFormatToMinuteWithUnit(0L)}${endString ?: ""}"
                    TimeCountDown.get().removeCountView(this@addCountDownWithUnit)
                    // 接受倒计时通知，更新内容
                    endLister.invoke()
                } else {
                    text = "${preString ?: ""} ${TimeUtils.timeFormatToMinuteWithUnit(expireTime - SystemClock.elapsedRealtime() + localRealtime)}${endString ?: ""}"
                }
            }
        })
    }
    // 在recyclerview中的复用问题未解决
    addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
        override fun onViewAttachedToWindow(v: View) {
            // LogUtils.e("xyd attached")
        }

        override fun onViewDetachedFromWindow(v: View) {
            // LogUtils.e("xyd detached")
            TimeCountDown.get().removeCountView(this@addCountDownWithUnit)
        }

    })
    return this
}

fun TextView.addSecondCountDown(
    expireTime: Long? = 0L,
    intervalLister: (expireTime: Long, end: Boolean) -> Unit
): TextView {
    if (expireTime ?: 0 < 0) return this
    val intervalValue = 1000L
    expireTime?.takeIf { expireTime > 0 }?.let {
        val localRealtime = SystemClock.elapsedRealtime()
        // 添加倒计时组件中
        TimeCountDown.get().addCountView(this, object : TimeCountDown.OnIntervalListener {
            override fun onNext() {
                if ((expireTime - SystemClock.elapsedRealtime() + localRealtime) < intervalValue) {
                    TimeCountDown.get().removeCountView(this@addSecondCountDown)
                    // 接受倒计时通知，更新内容
                    intervalLister.invoke(0, true)
                } else {
                    intervalLister.invoke(expireTime - SystemClock.elapsedRealtime() + localRealtime, false)
                }
            }
        })
    }
    // 在recyclerview中的复用问题未解决
    addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
        override fun onViewAttachedToWindow(v: View) {
        }

        override fun onViewDetachedFromWindow(v: View) {
            TimeCountDown.get().removeCountView(this@addSecondCountDown)
        }
    })
    return this
}


inline fun TextView.addUnderline() {
    paint.flags = Paint.UNDERLINE_TEXT_FLAG
}

inline fun TextView.addMiddleline() {
    paint.flags = Paint.STRIKE_THRU_TEXT_FLAG
}


class TimeCountDown() {

    private val MSG = 100
    private var intervalLong: Long = 1000L
    private val handler: Handler = @SuppressLint("HandlerLeak")
    object : Handler() {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            if (msg.what == MSG) {
                try {
                    val currentTimeMillis = System.currentTimeMillis()
                    val nextPostInterval = if (currentTimeMillis % intervalLong == 0L) intervalLong else (intervalLong - currentTimeMillis % intervalLong)
                    sendEmptyMessageDelayed(MSG, nextPostInterval)
                    cachedView.forEach { key, value -> value?.onNext() }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    private val cachedView: SparseArray<OnIntervalListener> = SparseArray()

    fun startTimer() {
        if (cachedView.size() == 1) {
            val currentTimeMillis = System.currentTimeMillis()
            val firstPostInterval = currentTimeMillis % intervalLong
            handler.sendEmptyMessageDelayed(MSG, firstPostInterval)
        }
    }

    fun cancelTimer() {
        handler.removeCallbacksAndMessages(null)
        handler.removeMessages(MSG)
    }

    fun addCountView(countdownView: View, next: OnIntervalListener) {
        cachedView.put(countdownView.hashCode(), next)
        startTimer()
    }

    fun removeCountView(countdownView: View) {
        cachedView.remove(countdownView.hashCode())
        if (cachedView.size() == 0) {
            cancelTimer()
        }
    }

    interface OnIntervalListener {
        fun onNext()
    }

    companion object {
        private lateinit var sInstance: TimeCountDown


        @MainThread
        fun get(): TimeCountDown {
            sInstance = if (::sInstance.isInitialized) sInstance else TimeCountDown()
            return sInstance
        }
    }

}
