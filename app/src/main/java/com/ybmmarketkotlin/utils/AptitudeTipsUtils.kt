package com.ybmmarketkotlin.utils

import android.app.Activity
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.text.style.ImageSpan
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.ybmmarket20.R
import com.ybmmarket20.common.ordertopmanager.OrderTopManager
import com.ybmmarket20.common.ordertopmanager.OrderTopParam
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.XyyIoUtil
import org.json.JSONObject

/**
 * <AUTHOR> Brin
 * @date : 2021/1/21 - 10:32
 * @Description :
 * @version
 */
class AptitudeTipsUtils {
    companion object {
        /**
         *  更新资质过期状态
         */
        @JvmOverloads
        fun initAptitudeOverdueTip(activity: Activity, layoutAptitudeTip: ConstraintLayout, tvAptitudeTip: TextView, trackIoPageName: String = "",orderTopParam: OrderTopParam? = null) {
            val tip = SpUtil.getAptitudeTip()
            if (!SpUtil.getAptitudeTip().isNullOrEmpty()) {
                val nameSSB: SpannableStringBuilder = if (tip == null) {
                    return
                } else {
                    SpannableStringBuilder("$tip ")
                }
//                layoutAptitudeTip.visibility = View.VISIBLE

                nameSSB.append("点击去更新资质 ")
                val endSpan = ForegroundColorSpan(activity.getResources().getColor(R.color.color_ff2121))
                nameSSB.setSpan(endSpan, nameSSB.length - 8, nameSSB.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)

                val imageSpan = ImageSpan(activity, R.drawable.icon_aptitude_tips_right,ImageSpan.ALIGN_BASELINE)
                nameSSB.append("-")
                nameSSB.setSpan(imageSpan, nameSSB.length - 1, nameSSB.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)

                tvAptitudeTip.text = nameSSB

                layoutAptitudeTip.setOnClickListener {
                    // licenseStatus = 1： 首营资质    其他：资质修改K
                    RoutersUtils.open("ybmpage://aptitude")
                    XyyIoUtil.track("QualificationUpdate_click", JSONObject().apply { put("pageName", trackIoPageName) })
                }
                orderTopParam?.let {
                    OrderTopManager.show(orderTopParam)
                }
            } else {
//                layoutAptitudeTip.visibility = View.GONE
                orderTopParam?.let {
                    OrderTopManager.dismiss(orderTopParam)
                }
            }
        }

    }
}