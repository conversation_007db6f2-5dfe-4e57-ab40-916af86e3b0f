package com.ybmmarketkotlin.utils

import android.os.Build
import com.ybm.app.utils.BugUtil
import okhttp3.internal.and
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.util.*
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

object ChCrypto {

    /**
     * 京东支付设置支付密码和验证支付密码加密
     */
    fun jdPayAesEncrypt(v: String) = AES256.encrypt(v, "FmpHxGc9no95cvd4")

    /**
     * 注册指纹到后端加密
     */
    @JvmStatic
    fun jdPayAesFingerprintEncryptWithKey(v: String) = AES256.encrypt(v, "Jzlt5e6cF2FznaGn")

    @JvmStatic
    fun aesEncrypt(v: String) = AES256.encrypt(v, "RVlXGN+kX6FLpIEt")

    @JvmStatic
    fun aesDecrypt(v: String) = AES256.decrypt(v, "RVlXGN+kX6FLpIEt")

    fun base64Encode(originString: String?): String {
        if (originString == null) return ""
        val encodeByteArray = originString.toByteArray()
        val encodedStr = try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val encorder = Base64.getEncoder()
                String(encorder.encode(encodeByteArray))
            } else {
                String(android.util.Base64.encode(encodeByteArray, android.util.Base64.NO_WRAP))
            }
        } catch (e: Exception) {
            BugUtil.sendBug(e)
            ""
        }
        return encodedStr
    }

    fun base64Decode(decodeString: String): String {
        val decodeByteArray = try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                Base64.getDecoder().decode(decodeString.toByteArray(Charsets.UTF_8))
            } else {
                android.util.Base64.decode(decodeString.toByteArray(Charsets.UTF_8), android.util.Base64.NO_WRAP)
            }
        } catch (e: Exception) {
            BugUtil.sendBug(e)
            byteArrayOf()
        }
        return String(decodeByteArray)
    }

    /**
     * sha256
     */
    @JvmStatic
    fun shaEncrypt256(strSrc: String): String?{
        return shaEncrypt(strSrc, "SHA-256")
    }

    @JvmStatic
    fun shaEncrypt(strSrc: String, algorithm: String): String? {
        var md: MessageDigest? = null
        var strDes: String? = null
        val bt = strSrc.toByteArray()
        try {
            md = MessageDigest.getInstance(algorithm) // 将此换成SHA-1、SHA-512、SHA-384等参数
            md.update(bt)
            strDes = bytes2Hex(md.digest()) // to HexString
        } catch (e: NoSuchAlgorithmException) {
            return null
        }
        return strDes
    }

    fun bytes2Hex(bts: ByteArray): String? {
        var des: String? = ""
        var tmp: String? = null
        for (i in bts.indices) {
            tmp = Integer.toHexString(bts[i] and 0xFF)
            if (tmp.length == 1) {
                des += "0"
            }
            des += tmp
        }
        return des
    }

}

private object AES256 {
    private fun cipher(opmode: Int, secretKey: String): Cipher {
        val c = Cipher.getInstance("AES/CBC/PKCS5Padding")
        val sk = SecretKeySpec(secretKey.toByteArray(Charsets.UTF_8), "AES")
        val iv = IvParameterSpec(secretKey.substring(0, 16).toByteArray(Charsets.UTF_8))
        c.init(opmode, sk, iv)
        return c
    }

    fun encrypt(str: String, secretKey: String): String {
        val encodeByteArray = cipher(Cipher.ENCRYPT_MODE, secretKey).doFinal(str.toByteArray(Charsets.UTF_8))
        val encodeStr =
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    val encorder = Base64.getEncoder()
                    String(encorder.encode(encodeByteArray))
                } else {
                    String(android.util.Base64.encode(encodeByteArray, android.util.Base64.NO_WRAP))
                }
            } catch (e: Exception) {
                BugUtil.sendBug(e)
                ""
            }
        return encodeStr
    }

    fun decrypt(str: String, secretKey: String): String {
        val decodeByteArray = try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                Base64.getDecoder().decode(str.toByteArray(Charsets.UTF_8))
            } else {
                android.util.Base64.decode(str.toByteArray(Charsets.UTF_8), android.util.Base64.NO_WRAP)
            }
        } catch (e: Exception) {
            BugUtil.sendBug(e)
            byteArrayOf()
        }
        return String(cipher(Cipher.DECRYPT_MODE, secretKey).doFinal(decodeByteArray))
    }
}
