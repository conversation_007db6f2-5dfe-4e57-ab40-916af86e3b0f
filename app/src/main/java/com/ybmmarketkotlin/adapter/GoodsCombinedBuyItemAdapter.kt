package com.ybmmarketkotlin.adapter

import android.text.Editable
import android.text.InputType
import android.text.SpannableStringBuilder
import android.text.TextWatcher
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.xyy.canary.utils.DensityUtil
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybm.app.common.SmartExecutorManager
import com.ybm.app.utils.BugUtil
import com.ybmmarket20.R
import com.ybmmarket20.adapter.YBMBaseListAdapter
import com.ybmmarket20.bean.RowsBeanCombinedExt
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.DialogUtil
import com.ybmmarket20.utils.DialogUtil.DialogClickListener
import com.ybmmarket20.utils.SpanUtils
import com.ybmmarket20.utils.UiUtils
import com.ybmmarketkotlin.views.combinedbuy.CombinedBuyListener

/**
 * <AUTHOR>
 * @desc    大搜加价购、组合购单个商品item
 * @date    2025/5/6
 */
open class GoodsCombinedBuyItemAdapter(products: MutableList<RowsBeanCombinedExt>?) :
    GoodsCombinedBuyItemAnalysisAdapter(R.layout.adapter_combinedbuy_goods, products) {
    var mListener: CombinedBuyListener? = null
    var mGroupFlag = true   // 是否为组合购

    override fun bindItemView(baseViewHolder: YBMBaseHolder, product: RowsBeanCombinedExt) {
        super.bindItemView(baseViewHolder, product)
        val ivPic = baseViewHolder.itemView.findViewById<ImageView>(R.id.ivPic)
        val tvSaveMoney = baseViewHolder.itemView.findViewById<RoundTextView>(R.id.tvSaveMoney)
        val tvGoodsName = baseViewHolder.itemView.findViewById<TextView>(R.id.tvProName)
        val tvGoodsPrice = baseViewHolder.itemView.findViewById<TextView>(R.id.tvProPrice)
        val tvSubtract = baseViewHolder.itemView.findViewById<TextView>(R.id.tvSubtract)
        val tvNum = baseViewHolder.itemView.findViewById<TextView>(R.id.tvNum)
        val tvAdd = baseViewHolder.itemView.findViewById<TextView>(R.id.tvAdd)
        val ivContact = baseViewHolder.itemView.findViewById<ImageButton>(R.id.ivGoodsContact)
        if (product.discount > 0 && !product.isMainProduct) {
            tvSaveMoney.visibility = View.VISIBLE
            tvSaveMoney.text = "省${UiUtils.transform(product.discount)}元"
        } else {
            tvSaveMoney.visibility = View.GONE
        }
        ImageHelper.with(mContext).load(AppNetConfig.LORD_IMAGE + product.imageUrl)
            .placeholder(R.drawable.jiazaitu_min)
            .diskCacheStrategy(DiskCacheStrategy.SOURCE)
            .dontAnimate()
            .into(ivPic)
        tvGoodsName.text = product.showName

        tvGoodsPrice.text = showGoodsPrice(product)
        product.newQty = product.qty
        tvNum.setText(showQty(product))
        if (baseViewHolder.layoutPosition == data.lastIndex) {
            ivContact.visibility = View.GONE
        } else {
            ivContact.visibility = View.VISIBLE
        }
        ivPic.setOnClickListener {
            mListener?.jumpToGoodsDetail(product)
        }
        tvGoodsName.setOnClickListener {
            mListener?.jumpToGoodsDetail(product)
        }
        tvSaveMoney.setOnClickListener {
            mListener?.jumpToGoodsDetail(product)
        }
        tvGoodsPrice.setOnClickListener {
            mListener?.jumpToGoodsDetail(product)
        }
        tvNum.setOnClickListener {
            val preNum = try{
                tvNum.getText().toString().toInt()
            }catch (e:Exception){
                BugUtil.sendBug(e,"组合购数量转换异常",tvNum.text.toString())
                0
            }
            mListener?.changeNumClick(product, preNum = preNum)
            //编辑弹出对话框加减数量
            DialogUtil.addOrSubDialog(
                (mContext as BaseActivity), InputType.TYPE_CLASS_NUMBER, tvNum.getText().toString(), product.stepNum,
                product.isSplit == 1, true, object : DialogClickListener {
                    override fun confirm(content: String) {
                        //获取商品的数量
                        var num = try {
                            content.toInt()
                        } catch (e: Exception) {
                            0
                        }
                        if (num <= 0) {
                            num = 0
                        }
                        SmartExecutorManager.getInstance().handler.postDelayed({
                            checkNum(num, product,preNum)
                        }, 50)
                    }

                    override fun cancel() {
                    }

                    override fun showSoftInput(view: View) {
                    }

                    override fun onDismiss() {
                        super.onDismiss()
                        SmartExecutorManager.getInstance().handler.postDelayed({
                            (mContext as BaseActivity).hideSoftInput()
                        }, 50)
                    }
                })
        }
        tvNum.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                if (s != null) {
                    if (s.length <= 3) {
                        tvNum.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14f)
                    }
                    if (s.length >= 4) {
                        tvNum.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f)
                    }
                }
            }

            override fun afterTextChanged(s: Editable) {
            }
        })
        tvSubtract.setOnClickListener {
            mListener?.changeNumClick(product, addFlag = false)
            // 已经减到最低值
            val step = subStepNum(product)
            // 组合购首次初始化，未校验库存。当出现库存不足，选中状态被变更 qty != 显示数量
            val curNum = try{
                tvNum.text.toString().toInt()
            }catch (e:Exception){
                0
            }
            checkNum(curNum - step, product)
        }
        tvAdd.setOnClickListener {
            mListener?.changeNumClick(product, addFlag = true)
            // 未选中商品首次添加数量
            product.newQty = if (product.selectStatus == 0) {
                product.actPt?.skuStartNum?:1
            } else {
                product.qty + addStepNum(product)
            }
            product.selectStatus = 1
            mListener?.changeNum(product, addFlag = true)
        }
    }

    /**
     * 显示商品价格 到手价 - 订单原价 - 限购价 - 拼团价
     * costPrice -> price -> limitFullDiscountActInfo.limitFullDiscount -> actPt.assemblePrice
     */
    private fun showGoodsPrice(product: RowsBeanCombinedExt):SpannableStringBuilder{
        val showPrice = if(product.costPrice == 0.0){
            try{
                if((product.price?.toDouble()?:0.0) == 0.0){
                    if(product.limitFullDiscountActInfo != null){
                        product.limitFullDiscountActInfo.limitFullDiscount
                    }else if(product.actPt != null){
                        product.actPt.assemblePrice
                    }else{
                        0.0
                    }
                }else{
                    product.price!!.toDouble()
                }
            }catch (e:Exception){
                if(product.limitFullDiscountActInfo != null){
                    product.limitFullDiscountActInfo.limitFullDiscount
                }else if(product.actPt != null){
                    product.actPt.assemblePrice
                }else{
                    0.0
                }
            }
        }else{
            product.costPrice
        }
        val priceSpan = SpanUtils().append("￥").setFontSize(DensityUtil.dip2px(YBMAppLike.getAppContext(), 12f))
            .append(UiUtils.transform(showPrice).toString())
            .setFontSize(DensityUtil.dip2px(YBMAppLike.getAppContext(), 14f))
            .create()
        return priceSpan
    }

    /**
     * 本地校验数量
     */
    private fun checkNum(curNum: Int, product: RowsBeanCombinedExt, preNum: Int = -1) {
        val lowVal = product.actPt?.skuStartNum ?: 1
        if (curNum >= lowVal) {
            product.newQty = curNum
            product.selectStatus = 1
            mListener?.changeNum(product, addFlag = false, preNum = preNum)
        } else {
            // 加价购低于起购数从已选->未选
            if (!mGroupFlag && product.selectStatus == 1) {
                product.newQty = 0
                product.selectStatus = 0
                mListener?.changeNum(product, addFlag = false, preNum = preNum)
                return
            }
            AlertDialogEx(mContext)
                .setTitle("")
                .setMessage("${product.showName} 不满足起购数")
                .setMessageGravity(Gravity.CENTER)
                .setCanceledOnTouchOutside(false)
                .setConfirmButton(
                    "我知道了"
                ) { dialog: AlertDialogEx?, _: Int ->
                    dialog?.dismiss()
                }.show()
        }
    }

    /**
     * 显示商品数量
     */
    private fun showQty(product: RowsBeanCombinedExt): CharSequence {
        // 未选中，数量展示0；实际上传为起购数
        return if (product.selectStatus == 0) {
            product.qty = if (product.actPt == null || product.actPt.skuStartNum == 0) {
                1
            } else {
                product.actPt.skuStartNum
            }
            "0"
        } else {
            product.qty.toString()
        }
    }

    // - 步长
    private fun subStepNum(product: RowsBeanCombinedExt) =
        if (product.isSplit == 1) {
            1
        } else {
            if (product.stepNum == 0) 1 else product.stepNum
        }

    // + 步长
    private fun addStepNum(product: RowsBeanCombinedExt): Int {
        return if (product.stepNum == 0) 1 else product.stepNum
    }

    override fun getIsGroupGoods(): Boolean {
        return mGroupFlag
    }


}
