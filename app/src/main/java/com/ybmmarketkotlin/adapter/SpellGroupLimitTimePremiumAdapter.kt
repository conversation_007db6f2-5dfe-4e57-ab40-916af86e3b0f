package com.ybmmarketkotlin.adapter

import android.os.CountDownTimer
import android.util.SparseArray
import android.widget.TextView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean

class SpellGroupLimitTimePremiumAdapter(rows: MutableList<RowsBean>) : GoodListAdapterNew(R.layout.item_goods_new_limit_time_premium, rows) {

    //限时补价倒计时
    val limitTimePremiumTimerMap: SparseArray<CountDownTimer> = SparseArray()

    override fun bindItemView(baseViewHolder: YBMBaseHolder, rowsBean: RowsBean) {
        super.bindItemView(baseViewHolder, rowsBean)

        processCountDown(baseViewHolder, rowsBean)
    }


    private fun processCountDown(baseViewHolder: YBMBaseHolder, rowsBean: RowsBean) {
        val tvMs = baseViewHolder.itemView.findViewById<TextView>(R.id.tv_time_hundred_ms)
        val tvS = baseViewHolder.itemView.findViewById<TextView>(R.id.tv_time_s)
        val tvMin = baseViewHolder.itemView.findViewById<TextView>(R.id.tv_time_min)
        val tvH = baseViewHolder.itemView.findViewById<TextView>(R.id.tv_time_h)
        val localDiff: Long = System.currentTimeMillis() - (rowsBean.limitFullDiscountActInfo?.responseLocalTime ?: 0L)
        val leftTime = (rowsBean.limitFullDiscountActInfo?.endTime ?: 0) - localDiff
        // 如果遇到刷新数据，要先取消原来的倒计时
        limitTimePremiumTimerMap.get(baseViewHolder.hashCode())?.let {
            it.cancel()
            limitTimePremiumTimerMap.remove(baseViewHolder.hashCode())
        }

        if (leftTime >= 0) {
            // 补价倒计时
            val countDownTimer = object : CountDownTimer(leftTime, 100L) {
                override fun onTick(millisUntilFinished: Long) {
                    // 计算剩余的小时数
                    val hours: Long = millisUntilFinished / (1000 * 60 * 60)
                    // 计算剩余的分钟数（去掉小时部分后的毫秒数）
                    val minutes: Long = millisUntilFinished % (1000 * 60 * 60) / (1000 * 60)
                    // 计算剩余的秒数（去掉分钟部分后的毫秒数）
                    val seconds: Long = millisUntilFinished % (1000 * 60) / 1000
                    // 计算剩余的百毫秒数
                    val hundredMilliseconds: Long = millisUntilFinished % 1000 / 100
                    tvMs.text = hundredMilliseconds.toString()
                    tvS.text = if (seconds < 10 ) "0$seconds" else seconds.toString()
                    tvMin.text = if (minutes < 10 ) "0$minutes" else minutes.toString()
                    tvH.text = if (hours < 10 ) "0$hours" else hours.toString()
                }

                override fun onFinish() {
                    tvMs.text = "0"
                    tvS.text = "00"
                    tvMin.text = "00"
                    tvH.text = "00"
                }
            }
            limitTimePremiumTimerMap.put(baseViewHolder.hashCode(), countDownTimer)
            countDownTimer.start()
        }
    }

}