package com.ybmmarketkotlin.adapter

class GoodsCombinedBuySingleBannerController {

    private var mBannerScrollStateControl: ((Boolean) -> Unit)? = null

    fun setBannerScrollStateControl(bannerScrollStateControl: ((Boolean) -> Unit)?) {
        this.mBannerScrollStateControl = bannerScrollStateControl
    }

    /**
     * 控制banner是否滚动
     */
    fun setBannerScrollState(isScrollable: Boolean) {
        mBannerScrollStateControl?.invoke(isScrollable)
    }
}