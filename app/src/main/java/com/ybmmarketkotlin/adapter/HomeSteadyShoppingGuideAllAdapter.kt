package com.ybmmarketkotlin.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Typeface
import androidx.core.content.ContextCompat
import androidx.cardview.widget.CardView
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StrikethroughSpan
import android.text.style.StyleSpan
import android.util.TypedValue
import android.view.View
import android.widget.TextView
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybmmarket20.R
import com.ybmmarket20.bean.homesteady.*
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

/**
 * 首页导购适配器（新的）
 */

const val SECKILL_STATUS_WAIT = 1           //未开始
const val SECKILL_STATUS_HAVE_IN_HAND = 2   //进行中
const val SECKILL_STATUS_END = 3            //已结束
const val SECKILL_CONVERSE = 4            // 将秒杀数据结构转化成其他结构

class HomeSteadyShoppingGuideAllAdapter(
        var shoppingGuideContext: Context,
        var list: MutableList<ShoppingGuideAllItem>,
        var resId: Int
) : YBMBaseAdapter<ShoppingGuideAllItem>(resId, list) {

    private var licenseStatus = -1
    private var analysisCallback: ((String, Int, String, String) -> Unit)? = null

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: ShoppingGuideAllItem?) {
        t?.also { it ->
            //标题副标题
            baseViewHolder?.setText(R.id.tv_major_title, t.moduleTitle)
            baseViewHolder?.setGone(R.id.tv_major_title,!TextUtils.isEmpty(t.moduleTitle))
            baseViewHolder?.setText(R.id.tv_minor_title, t.describe)
            baseViewHolder?.setGone(R.id.tv_minor_title,!TextUtils.isEmpty(t.describe))
            if (t.itemInfo?.size ?: 0 <= 0){
                baseViewHolder?.getView<CardView>(R.id.cv_item_shopping_guide_bg1)?.visibility = View.INVISIBLE
                baseViewHolder?.getView<CardView>(R.id.cv_item_shopping_guide_bg2)?.visibility = View.INVISIBLE
            }
            if (t.itemInfo?.size ?: 0 >= 1) {
                baseViewHolder?.getView<CardView>(R.id.cv_item_shopping_guide_bg1)?.visibility = View.VISIBLE
                baseViewHolder?.getView<CardView>(R.id.cv_item_shopping_guide_bg2)?.visibility = View.INVISIBLE
                setImage(baseViewHolder, "${t.itemInfo?.get(0)?.imageUrl}", R.id.iv_product1)
            }
            if (t.itemInfo?.size ?: 0 >= 2) {
                setImage(baseViewHolder, "${t.itemInfo?.get(1)?.imageUrl}", R.id.iv_product2)
                baseViewHolder?.getView<CardView>(R.id.cv_item_shopping_guide_bg2)?.visibility = View.VISIBLE
            }
            when (baseViewHolder?.layoutPosition) {
                0 -> R.drawable.icon_home_steady_fast_entry_kill
                1 -> R.drawable.icon_home_steady_fast_entry_sort
                2 -> R.drawable.icon_home_steady_fast_entry_daily_discount
                3 -> R.drawable.icon_home_steady_fast_entry_gross
                4 -> R.drawable.icon_home_steady_fast_entry_brand_purchase
                5 -> R.drawable.icon_home_steady_fast_entry_new_recommend
                else -> 0
            }.also { resId ->
                baseViewHolder?.setImageResource(R.id.iv_fast_entry_item_bg, resId)
            }

            //秒杀倒计时
            baseViewHolder?.setText(R.id.tv_kill_title, getSeckillName(t.seckillInfo))
            baseViewHolder?.setGone(R.id.tv_kill_title, t.seckillInfo!=null)
            baseViewHolder?.setGone(R.id.v_kill, t.seckillInfo!=null)
            //重置价格显示状态默认不显示
            baseViewHolder?.setGone(R.id.tv_price1,false)
            baseViewHolder?.setGone(R.id.tv_price2,false)
            if (t.itemInfo?.size ?: 0 >= 1) {
                    setPrice(baseViewHolder?.getView(R.id.tv_price1), t.itemInfo?.get(0),licenseStatus)
            }
            if (t.itemInfo?.size ?: 0 >= 2) {
                    setPrice(baseViewHolder?.getView(R.id.tv_price2), t.itemInfo?.get(1),licenseStatus)
            }
            if (t.seckillInfo?.status == SECKILL_STATUS_WAIT) {
                baseViewHolder?.getView<TextView>(R.id.tv_kill_title)?.setBackgroundResource(R.drawable.shape_home_steady_kill_title_green)
                baseViewHolder?.getView<View>(R.id.v_kill)?.setBackgroundResource(R.drawable.shape_home_steady_kill_green)
                baseViewHolder?.getView<TextView>(R.id.tv_seckill_time)?.setTextColor(ContextCompat.getColor(shoppingGuideContext, R.color.color_00B377))
                baseViewHolder?.getView<TextView>(R.id.tv_seckill_time)?.text = "即将开始"
            } else if (t.seckillInfo?.status == SECKILL_STATUS_HAVE_IN_HAND) {
                baseViewHolder?.getView<TextView>(R.id.tv_kill_title)?.setBackgroundResource(R.drawable.shape_home_steady_kill_title_red)
                baseViewHolder?.getView<View>(R.id.v_kill)?.setBackgroundResource(R.drawable.shape_home_steady_kill_red)
                baseViewHolder?.getView<TextView>(R.id.tv_seckill_time)?.setTextColor(ContextCompat.getColor(shoppingGuideContext, R.color.color_ff2121))
                baseViewHolder?.getView<TextView>(R.id.tv_seckill_time)?.text = t.seckillInfo?.time
            } else if (t.seckillInfo?.status == SECKILL_STATUS_END) {
                baseViewHolder?.getView<TextView>(R.id.tv_kill_title)?.setBackgroundResource(R.drawable.shape_home_steady_kill_title_red)
                baseViewHolder?.getView<View>(R.id.v_kill)?.setBackgroundResource(R.drawable.shape_home_steady_kill_red)
                baseViewHolder?.getView<TextView>(R.id.tv_seckill_time)?.setTextColor(ContextCompat.getColor(shoppingGuideContext, R.color.color_ff2121))
                baseViewHolder?.getView<TextView>(R.id.tv_seckill_time)?.text = "已结束"
            }else{
                baseViewHolder?.getView<TextView>(R.id.tv_seckill_time)?.text = ""
            }
            baseViewHolder?.getConvertView()?.setOnClickListener { v ->
                val action = t.jumpUrl ?: "";
                RoutersUtils.open(t.jumpUrl)
                var skuId = ""
                t.itemInfo?.forEach {
                    skuId += "_${it.id ?: ""}"
                }
                if (skuId.isNotEmpty()) skuId = skuId.substring(1, skuId.length)
                clickEvent(action, baseViewHolder.adapterPosition, t.moduleTitle ?: "", skuId)
            }
        }
    }

    /**
     * 点击事件埋点
     */
    private fun clickEvent(action: String, offset: Int, text: String, skuId: String) {
        try {
            val obj = JSONObject()
//            obj.apply {
//                put("action", action)
//                put("offset", offset + 1)
//                put("text", text)
//                put("sku_id", skuId)
//            }
//            XyyIoUtil.track("action_Home_Card", obj)
            analysisCallback?.invoke(action, offset, text, skuId)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 设置价钱
     */
    private fun setPrice(tv: TextView?, productItem: ProductAllItem?,licenseStatus: Int) {
        try {
            val price = productItem?.skuPrice ?: ""
            val fob = productItem?.fob ?: ""
            if ((TextUtils.isEmpty(price)&&TextUtils.isEmpty(fob))
                    ||licenseStatus==1||licenseStatus==5 //【价格认证资质可见 licenseStatus 1资质未提交 5首营资质审核中 】
                    ||(productItem?.isControl == 1 && !productItem.isPurchase) // 【暂无购买权限 productItem?.isControl == 1 && !productItem.isPurchase】
                    ||(productItem?.isOEM == true&&productItem.signStatus != 1)//【价格签署协议可见productItem?.isOEM == true&&productItem.signStatus != 1是否签署协议】
                    ||(productItem?.showAgree == 0)){ //【价格签署协议可见//是否符合协议标准展示价格,1:符合0:不符合seckillProduct?.showAgree == 0】
                tv?.visibility=View.GONE
                return
            }
            tv?.visibility=View.VISIBLE
            tv?.setTextColor(ContextCompat.getColor(shoppingGuideContext, R.color.color_ff2121))
            tv?.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12f)
            val builder = SpannableStringBuilder("¥$price")
            if (price.isNotEmpty()) {
                val styleSpan = StyleSpan(Typeface.BOLD)
                builder.setSpan(AbsoluteSizeSpan(ConvertUtils.dp2px(9f)), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                if (price.contains(".")){
                    builder.setSpan(AbsoluteSizeSpan(ConvertUtils.dp2px(9f)),price.indexOf(".")+1,price.length+1,Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                    builder.setSpan(styleSpan, 1, price.indexOf(".")+1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                }else{
                    builder.setSpan(styleSpan, 0, price.length + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                }
            }
            if (fob.isNotEmpty()) {
                val fobBuilder = SpannableStringBuilder("¥$fob")
                val fobSpan = AbsoluteSizeSpan(ConvertUtils.dp2px(9f))
                val colorSpan = ForegroundColorSpan(ContextCompat.getColor(shoppingGuideContext, R.color.color_4d222222))
                val strikeSpan = StrikethroughSpan()
                fobBuilder.setSpan(fobSpan, 0, fob.length + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                fobBuilder.setSpan(strikeSpan, 0, fob.length + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                fobBuilder.setSpan(colorSpan, 0, fob.length + 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                builder.append(fobBuilder)
            }
            tv?.text = builder
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 设置 价格签署协议可见、暂无购买权限、价格认证资质可见
     */
    private fun setPriceStatus(tv: TextView?, productItem: ProductAllItem?, licenseStatus: Int): String {
        tv?.setTextColor(ContextCompat.getColor(shoppingGuideContext, R.color.color_222))
        tv?.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 10f)
        if (licenseStatus == 1 || licenseStatus == 5) {
            //通过
            tv?.text = "价格认证资质可见"
            return "价格认证资质可见"
        }
        var priceStatusText = ""
        if (productItem?.isControl == 1 && !productItem.isPurchase) {
            priceStatusText = "暂无购买权限"
        } else {
            if (productItem?.isOEM == true) {
                //是否签署协议
                if (productItem.signStatus != 1) {
                    priceStatusText = "价格签署协议可见"
                }
            }

            //是否符合协议标准展示价格,1:符合0:不符合
            if (productItem?.showAgree == 0) {
                priceStatusText = "价格签署协议可见"
            }
        }

        tv?.text = priceStatusText
        return priceStatusText
    }

    /**
     * 设置商品图片
     */
    private fun setImage(baseViewHolder: YBMBaseHolder?, img: String?, imageViewId: Int) {
        val imageUrl = img?.let {
            if (img.startsWith("http")) {
                img
            } else {
                "${AppNetConfig.LORD_IMAGE}${img}"
            }
        } ?: ""
        ImageHelper.with(shoppingGuideContext).load(imageUrl).diskCacheStrategy(DiskCacheStrategy.SOURCE)
                .dontAnimate().into(baseViewHolder?.getView(imageViewId))
    }

    /**
     * 获取xx点场次
     */
    @SuppressLint("SimpleDateFormat")
    fun getSeckillName(seckill: SeckillAllInfo?): String? = seckill?.let {
        val cal = Calendar.getInstance()
        cal.time = Date(it.startDate)
        val hours = SimpleDateFormat("HH").format(Date(it.startDate))
        return "${hours}点场"
    }

    /**
     * 设置一审状态
     */
    fun setLicenseStatus(licenseStatus: Int) {
        this.licenseStatus = licenseStatus
    }

    fun setAnalysisCallback(callback: (action: String, offset: Int, text: String, sku_id: String) -> Unit) {
        analysisCallback = callback
    }

}