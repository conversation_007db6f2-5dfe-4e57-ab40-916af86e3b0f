package com.ybmmarketkotlin.adapter.goodslist

import android.content.Context
import android.os.CountDownTimer
import android.util.SparseArray
import android.view.View
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.common.widget.RoundTextView

/**
 * 拼团
 */
open class SellOutSpellGroupGoodsListAdapterNewBindItem(
    mContext: Context,
    baseViewHolder: YBMBaseHolder,
    rowsBean: RowsBean,
    countDownTimerMap: SparseArray<CountDownTimer>,
    adapter: RecyclerView.Adapter<*>
): SpellGroupGoodsListAdapterNewBindItem(mContext, baseViewHolder, rowsBean, countDownTimerMap, adapter) {

    override fun onGoodsNameWithTags() {
        super.onGoodsNameWithTags()
        baseViewHolder.getView<TextView>(R.id.shop_name).maxLines = 2
    }

    override fun onSellOut() {
        super.onSellOut()
        baseViewHolder.getView<LinearLayout>(R.id.ll_subscribe).visibility = View.GONE
    }

    override fun onOffShelf() {
        super.onOffShelf()
        baseViewHolder.getView<LinearLayout>(R.id.ll_subscribe).visibility = View.GONE
    }

    override fun onSpellGroupOrSeckill() {
        super.onSpellGroupOrSeckill()
        val btn = baseViewHolder.getView<RoundTextView>(R.id.tv_join_groupbooking)
        btn.text = "已抢光"
        btn.setBackgroundColor(ContextCompat.getColor(mContext, R.color.color_AAACB9))
    }

    override fun onGoodsDiscountPrice() {
//        super.onGoodsDiscountPrice()
        // 折后价
        val tvOriginalPrice = baseViewHolder.getView<TextView>(R.id.tv_original_price_spell_group)

        if ((!rowsBean.isControlTitle || rowsBean.controlType == 5) && rowsBean.availableQty > 0 && !rowsBean.showPriceAfterDiscount.isNullOrEmpty()) {
            tvOriginalPrice.visibility = View.VISIBLE
            tvOriginalPrice.text = rowsBean.showPriceAfterDiscount
        } else {
            tvOriginalPrice.visibility = View.GONE
        }
    }
}