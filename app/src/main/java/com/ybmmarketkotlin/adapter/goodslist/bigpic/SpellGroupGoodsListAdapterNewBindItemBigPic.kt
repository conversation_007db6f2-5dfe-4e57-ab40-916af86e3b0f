package com.ybmmarketkotlin.adapter.goodslist.bigpic

import android.content.Context
import android.os.CountDownTimer
import android.text.TextUtils
import android.util.SparseArray
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.ControlGoodsDialogUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.view.ProductEditLayoutNew
import com.ybmmarket20.view.ShopNameWithTagView
import com.ybmmarket20.xyyreport.page.search.SearchProductReport
import com.ybmmarketkotlin.adapter.SpellGroupPopWindow

/**
 * 拼团
 */
open class SpellGroupGoodsListAdapterNewBindItemBigPic(
    mContext: Context,
    baseViewHolder: YBMBaseHolder,
    rowsBean: RowsBean,
    private val countDownTimerMap: SparseArray<CountDownTimer>,
    val adapter: RecyclerView.Adapter<*>
): AbstractGoodsListAdapterNewBindItemBigPic(mContext, baseViewHolder, rowsBean, countDownTimerMap) {


    override fun onGoodsNameWithTags() {
        super.onGoodsNameWithTags()
        baseViewHolder.getView<TextView>(R.id.shop_name).maxLines = 2
    }

    override fun onSpellGroupOrSeckill() {
        super.onSpellGroupOrSeckill()
        processCountDown(baseViewHolder, rowsBean)
    }
    /**
     * 营销标签
     */
    override fun onMarketingTags() {
        super.onMarketingTags()
        try {
            val marketTag: ShopNameWithTagView = baseViewHolder.getView(R.id.snwtg_spell_group_market_tag)
            val isTagEmpty = rowsBean.tags?.productTags?.size ?: 0
            if (rowsBean.tags?.productTags != null && isTagEmpty != 0) {
                marketTag.visibility = View.VISIBLE
            } else {
                marketTag.visibility = View.GONE
            }
            val allTags = getAllTags()
            allTags.let {
                marketTag.bindData(it, maxTagCount = 100)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * @param baseViewHolder
     * @param rowsBean
     *
     *  1. 拼团倒计时
     *  2. 隐藏规格、厂家
     *  3. 替换拼团价、无折后价，增加划线价
     *  4. 增加拼购比例文字显示、进度显示、参团按钮
     *  5. 隐藏 数据标签、优惠标签、零售价、加购按钮、中包装
     *  处理 拼团相关 的逻辑
     */
    private fun processCountDown(baseViewHolder: YBMBaseHolder, rowsBean: RowsBean) {

        // 倒计时组件
        val tvCountdown = baseViewHolder.getView<TextView?>(R.id.tv_countdown)
        val clGroupbooking = baseViewHolder.getView<ConstraintLayout?>(R.id.cl_groupbooking)
        val tvGroupbooking = baseViewHolder.getView<TextView?>(R.id.tv_groupbooking)
        val progress = baseViewHolder.getView<ProgressBar?>(R.id.progress)
        val tvGroupbookingProgress = baseViewHolder.getView<TextView?>(R.id.tv_groupbooking_progress)
        val tvJoinGroupbooking = baseViewHolder.getView<TextView?>(R.id.tv_join_groupbooking)


        val localDiff: Long = System.currentTimeMillis() - (rowsBean.actPt?.responseLocalTime ?: 0L)
        val leftTime = (rowsBean.actPt?.surplusTime ?: 0L) * 1000 - localDiff

        // 如果遇到刷新数据，要先取消原来的倒计时
        countDownTimerMap.get(baseViewHolder.hashCode())?.let {
            it.cancel()
            countDownTimerMap.remove(baseViewHolder.hashCode())
        }
        if (leftTime > 0) {
            tvCountdown?.visibility = View.GONE
//            tvCountdown?.text = "距离结束仅剩 ${TimeUtils.timeFormat(leftTime)}"
//
//            val countDownTimer = object : CountDownTimer(leftTime, 1000L) {
//                override fun onTick(millisUntilFinished: Long) {
//                    tvCountdown?.text = "距离结束仅剩 ${TimeUtils.timeFormat(millisUntilFinished)}"
//                }
//
//                override fun onFinish() {
//                    rowsBean.actPt = null
//                    adapter.notifyItemChanged(baseViewHolder.bindingAdapterPosition)
//                }
//            }
//            countDownTimerMap.put(baseViewHolder.hashCode(), countDownTimer)
//            countDownTimer.start()

            clGroupbooking?.visibility = View.VISIBLE
            tvGroupbooking?.text =
                "已拼${rowsBean.actPt?.orderNum}${rowsBean.productUnit}/${rowsBean.actPt?.skuStartNum}${rowsBean.productUnit}起拼"
            tvGroupbooking?.setTextColor(ContextCompat.getColor(mContext, R.color.color_FF5F59))
            progress?.progress = rowsBean.actPt?.percentage?.toInt() ?: 0
            tvGroupbookingProgress?.text = "已拼${rowsBean.actPt?.percentage?.toInt() ?: 0}%"
            tvJoinGroupbooking?.setOnClickListener {
                SearchProductReport.trackSearchItemBtnClickSpellGroup(mContext, baseViewHolder.bindingAdapterPosition, rowsBean)
                if (rowsBean.isControlGoods) {
                    //控销品
                    ControlGoodsDialogUtil.showControlGoodsDialog(mContext, rowsBean)
                    return@setOnClickListener
                }
                if (rowsBean.actPt != null && rowsBean.actPt.isApplyListShowType) {
                    val mPopWindowSpellGroup = SpellGroupPopWindow(
                        baseViewHolder.itemView.context,
                        rowsBean,
                        rowsBean.actPt,
                        false,
                        mIsList = true,
                        <EMAIL>
                    )

//                mPopWindowSpellGroup.setData(rowsBean, rowsBean.actPt)
                    mPopWindowSpellGroup.show(baseViewHolder.itemView)
                } else {
                    var mUrl = "ybmpage://productdetail/" + rowsBean.id
                    RoutersUtils.open(mUrl)
//                    openUrl(
//                        "ybmpage://productdetail/" + rowsBean.id, mFlowData
//                    )
                }

                //点击立即参团也认为是点击商品的埋点
                productClickTrackListener?.invoke(rowsBean,baseViewHolder.bindingAdapterPosition,null)
            }

            // 1. 拼团倒计时
            // 2. 隐藏规格、厂家
            baseViewHolder.getView<TextView?>(R.id.tv_goods_spec)?.visibility = View.GONE
            baseViewHolder.getView<ImageView?>(R.id.iv_divider_of_spec_name)?.visibility = View.GONE
            baseViewHolder.getView<TextView?>(R.id.tv_chang_name)?.visibility = View.GONE
            // 3. 替换拼团价、无折后价，增加划线价
            // 4. 增加拼购比例文字显示、进度显示、参团按钮
            // 5. 隐藏 数据标签、优惠标签、零售价、加购按钮、中包装、有效期（后加）、售罄图标（如果在显示的话）
//            baseViewHolder.getView<ShopNameWithTagView?>(R.id.data_tag_list_view)?.visibility = View.GONE
            baseViewHolder.getView<ShopNameWithTagView?>(R.id.rl_icon_type)?.visibility = View.GONE
            baseViewHolder.getView<ImageView?>(R.id.iv_promotion_more)?.visibility = View.GONE
            baseViewHolder.getView<TextView?>(R.id.tv_retail_price)?.visibility = View.GONE
            baseViewHolder.getView<ProductEditLayoutNew?>(R.id.el_edit)?.visibility = View.GONE
            baseViewHolder.getView<TextView?>(R.id.shop_price_tv)?.visibility = View.GONE
//            baseViewHolder.getView<TextView?>(R.id.tv_validity_period)?.visibility = View.GONE
            baseViewHolder.getView<LinearLayout>(R.id.ll_subscribe)?.visibility = View.GONE
            val clSpellGroup = baseViewHolder.getView<ConstraintLayout?>(R.id.cl_groupbooking)
            clSpellGroup?.visibility = View.VISIBLE
            clSpellGroup?.setBackgroundResource(R.drawable.icon_spell_group_btn_active_bg)
            //时间设置图标
            val drawable = ContextCompat.getDrawable(mContext, R.drawable.icon_countdown)
            drawable?.setBounds(0, 0, drawable.minimumWidth, drawable.minimumHeight)
            tvCountdown?.setCompoundDrawables(drawable, null, null, null)
            //时间设置颜色
            tvCountdown?.setTextColor(ContextCompat.getColor(mContext, R.color.color_FE5427))
            //进度条
            val sgProgress = baseViewHolder.getView<ProgressBar>(R.id.progress)
            sgProgress.progressDrawable = ContextCompat.getDrawable(mContext, R.drawable.bg_progressbar_groupbooking)
            //进度
            tvGroupbookingProgress?.setTextColor(ContextCompat.getColor(mContext, R.color.color_FE5427))
            //立即参团
            val btn = baseViewHolder.getView<TextView>(R.id.tv_join_groupbooking)
            btn.text = "立即参团"
            btn.setTextColor(ContextCompat.getColor(mContext, R.color.white))
            btn.setBackgroundResource(R.drawable.icon_spell_group_btn_active)
        } else {
            tvCountdown?.visibility = View.GONE
        }
    }

    override fun onFinal() {
        super.onFinal()
        setControl()
    }

    //设置控销
    fun setControl() {
        if (!TextUtils.isEmpty(rowsBean.controlTitle)) {
            //控销商品隐藏拼团加购按钮
            val spellGroupBtn = baseViewHolder.getView<ConstraintLayout>(R.id.cl_groupbooking)
            spellGroupBtn.visibility = View.GONE
            //控销商品隐藏拼团倒计时
            val spellGroupTime = baseViewHolder.getView<TextView>(R.id.tv_countdown)
            spellGroupTime.visibility = View.GONE
        }
        // <PRD-8>”未签署控销协议商品“显示价格 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=776143984
        // 控销显示商品价格样式
        if (rowsBean.controlType == 5) {
            //控销商品隐藏拼团加购按钮
            val spellGroupBtn = baseViewHolder.getView<ConstraintLayout>(R.id.cl_groupbooking)
            spellGroupBtn.visibility = View.VISIBLE
            //控销商品隐藏拼团倒计时
            val spellGroupTime = baseViewHolder.getView<TextView>(R.id.tv_countdown)
            spellGroupTime.visibility = View.GONE
        }
    }
}