package com.ybmmarketkotlin.adapter.goodslist.findsamegoods

import android.text.TextUtils
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import com.ybmmarket20.R
import com.ybmmarket20.bean.TagBean
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.view.ShopNameWithTagView
import com.ybmmarketkotlin.adapter.goodslist.AbstractGoodsListAdapterNewBindItem
import com.ybmmarketkotlin.adapter.goodslist.bigpic.AbstractGoodsListAdapterNewBindItemBigPic

/**
 * 设置进店
 */
fun AbstractGoodsListAdapterNewBindItem.setFindSameGoodsShop(isShowShopInfo: Boolean) {
    if (!rowsBean.shopUrl.isNullOrEmpty() && isShowShopInfo) {
        baseViewHolder.setVisible(R.id.group_shop, true)
        baseViewHolder.getView<ConstraintLayout>(R.id.cl_pop_company).setOnClickListener {
            RoutersUtils.open(rowsBean.shopUrl)
        }
    } else {
        baseViewHolder.setVisible(R.id.group_shop, false)
    }
}

fun AbstractGoodsListAdapterNewBindItemBigPic.setFindSameGoodsShop(isShowShopInfo: Boolean) {
    if (!rowsBean.shopUrl.isNullOrEmpty() && isShowShopInfo) {
        baseViewHolder.setVisible(R.id.group_shop, true)
        baseViewHolder.getView<ConstraintLayout>(R.id.cl_pop_company).setOnClickListener {
            RoutersUtils.open(rowsBean.shopUrl)
        }
    } else {
        baseViewHolder.setVisible(R.id.group_shop, false)
    }
}

/**
 * 设置标签
 */
fun AbstractGoodsListAdapterNewBindItem.setFindSameGoodsCoupon(isGone: Boolean = false) {
    //个别layout中不存在tv_coupon_title和rl_icon_type_top会导致空指针
    try {
        if (isGone) {
            //全部隐藏
            baseViewHolder.setVisible(R.id.group_coupon, false)
            baseViewHolder.setVisible(R.id.tv_coupon_title, false)
            baseViewHolder.setVisible(R.id.rl_icon_type_top, false)
            return
        }
        if (!TextUtils.isEmpty(rowsBean.tags?.couponTag?.text)) {
            //显示券
            baseViewHolder.setText(R.id.tv_coupon_title, rowsBean.tags?.couponTag?.text)
            baseViewHolder.setVisible(R.id.group_coupon, true)
            baseViewHolder.setVisible(R.id.tv_coupon_title, true)
            baseViewHolder.setVisible(R.id.rl_icon_type_top, false)
        } else if (!rowsBean.tags?.productTagsNoCoupon.isNullOrEmpty()) {
            //显示标签
            baseViewHolder.setVisible(R.id.group_coupon, true)
            baseViewHolder.setVisible(R.id.tv_coupon_title, false)
            baseViewHolder.setVisible(R.id.rl_icon_type_top, true)
            val tagView = baseViewHolder.getView<ShopNameWithTagView>(R.id.rl_icon_type_top)
            tagView.bindData(getTags(rowsBean.tags?.productTagsNoCoupon))
        } else {
            //全部隐藏
            baseViewHolder.setVisible(R.id.group_coupon, false)
            baseViewHolder.setVisible(R.id.tv_coupon_title, false)
            baseViewHolder.setVisible(R.id.rl_icon_type_top, false)
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }

}

fun AbstractGoodsListAdapterNewBindItemBigPic.setFindSameGoodsCoupon(isGone: Boolean = false) {
    //个别layout中不存在tv_coupon_title和rl_icon_type_top会导致空指针
    try {
        if (isGone) {
            //全部隐藏
            baseViewHolder.setVisible(R.id.group_coupon, false)
            baseViewHolder.setVisible(R.id.tv_coupon_title, false)
            baseViewHolder.setVisible(R.id.rl_icon_type_top, false)
            return
        }
        if (!TextUtils.isEmpty(rowsBean.tags?.couponTag?.text)) {
            //显示券
            baseViewHolder.setText(R.id.tv_coupon_title, rowsBean.tags?.couponTag?.text)
            baseViewHolder.setVisible(R.id.group_coupon, true)
            baseViewHolder.setVisible(R.id.tv_coupon_title, true)
            baseViewHolder.setVisible(R.id.rl_icon_type_top, false)
        } else if (!rowsBean.tags?.productTagsNoCoupon.isNullOrEmpty()) {
            //显示标签
            baseViewHolder.setVisible(R.id.group_coupon, true)
            baseViewHolder.setVisible(R.id.tv_coupon_title, false)
            baseViewHolder.setVisible(R.id.rl_icon_type_top, true)
            val tagView = baseViewHolder.getView<ShopNameWithTagView>(R.id.rl_icon_type_top)
            tagView.bindData(getTags(rowsBean.tags?.productTagsNoCoupon))
        } else {
            //全部隐藏
            baseViewHolder.setVisible(R.id.group_coupon, false)
            baseViewHolder.setVisible(R.id.tv_coupon_title, false)
            baseViewHolder.setVisible(R.id.rl_icon_type_top, false)
        }
        if (!rowsBean.tags?.orderFreeShippingTags.isNullOrEmpty()) {
            baseViewHolder.setVisible(R.id.tag_view, true)
            val tagView = baseViewHolder.getView<ShopNameWithTagView>(R.id.tag_view)
            tagView.bindData(getTags(rowsBean.tags?.orderFreeShippingTags))
        } else {
            // 复用隐藏
            baseViewHolder.setVisible(R.id.tag_view, false)
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }
}
 fun getTags(list:List<TagBean>?): MutableList<TagBean> {
    // <PRD-8>”未签署控销协议商品“显示价格 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=776143984
    // 签署协议后可买 优先级第一
    // 老逻辑 先取tas.dataTags 再取tas.productTags
    var tags = mutableListOf<TagBean>()
     list?.also { tags += it }
//        rowsBean.tags?.dataTags?.also { tags += it }
    if (tags.size > 2) {
        tags = tags.subList(0, 2)
    }
    return tags
}