package com.ybmmarketkotlin.adapter.goodslist

import android.content.Context
import android.os.CountDownTimer
import android.util.SparseArray
import android.view.View
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean

class PreSpellGroupGoodsListAdapterNewCategoryBindItem(
    mContext: Context,
    baseViewHolder: YBMBaseHolder,
    rowsBean: RowsBean,
    countDownTimerMap: SparseArray<CountDownTimer>
) : PreSpellGroupGoodsListAdapterNewBindItem(
    mContext,
    baseViewHolder,
    rowsBean,
    countDownTimerMap
) {

    override fun handlePreSpellGroup() {
        super.handlePreSpellGroup()
    }

}