package com.ybmmarketkotlin.adapter.goodslist

import androidx.recyclerview.widget.RecyclerView
import com.ybmmarket20.bean.RowsBean

/**
 * 商品列表adapter绑定View
 */
interface IGoodsListAdapterNewBindItem {

    /**
     * 初始化View显隐
     */
    fun onInitViewVisibility()

    /**
     * 加载商品图标
     */
    fun onLoadGoodsIcon()

    /**
     * 卖点标签
     */
    fun onHandleSellingPointTag()

    /**
     * 医保
     */
    fun onGoodsMedicalInsuranceWithTags()

    /**
     * 预热标签
     */
    fun onHandlePreHotTag()

    /**
     * 买过次数
     */
    fun onBuyCount()

    /**
     * 商品标题
     */
    fun onGoodsNameWithTags()

    /**
     * 商品规格
     */
    fun onGoodsSpec()

    /**
     * 厂标签
     */
    fun onManufacturerTag()

    /**
     * 效期标签
     */
    fun onEffectTags()

    /**
     * 数据标签 ： "60天最低价",8, "区域毛利榜" 10, "比上次购买时降xx元",11, "比加入时降xx元",12, "品类点击榜"
     */
    fun onDataTags()

    /**
     * 商品价格
     */
    fun onGoodsPrice(showUnderlinePrice: Boolean, showPgbyUnderLineProce: Boolean = true)

    /**
     * 折后价
     */
    fun onGoodsDiscountPrice()

    /**
     * 控销价或零售价
     */
    fun onRetailPrice()

    /**
     * 中包装
     */
    fun onMediumPackage()

    /**
     * 营销标签
     */
    fun onMarketingTags()

    /**
     * 优惠券
     */
    fun onCoupon()

    /**
     * 店铺
     */
    fun onShop(isShowShopInfo: Boolean)

    /**
     * 到货提醒
     */
    fun onArrivalOfGoodsNotify(adapter: RecyclerView.Adapter<*>)

    /**
     * 售罄
     */
    fun onSellOut()

    /**
     * 下架
     */
    fun onOffShelf()

    /**
     * 加购
     */
    fun onAddCart(pageFrom: Int, rowsBean: RowsBean, index: String)

    /**
     * 价格认证资质可见
     */
    fun onHandleAuditPassedVisible()

    /**
     * 拼团或秒杀
     */
    fun onSpellGroupOrSeckill()

    /**
     * 拼团预热
     */
    fun onSpellGroupPreHot()

    /**
     * 普通商品
     */
    fun onNormal()

}