package com.ybmmarketkotlin.adapter.goodslist.bigpic

import android.content.Context
import android.os.CountDownTimer
import android.text.TextUtils
import android.util.SparseArray
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.SeekBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.view.ProductEditLayoutNew
import com.ybmmarket20.view.ShopNameWithTagView
import com.ybmmarketkotlin.views.SeckillTimeView

/**
 * 秒杀
 */
open class SeckillListAdapterNewBindItemBigPic(
    mContext: Context,
    baseViewHolder: YBM<PERSON>aseHolder,
    rowsBean: RowsBean,
    private val countDownTimerMap: SparseArray<CountDownTimer>,
    val adapter: RecyclerView.Adapter<*>
) : AbstractGoodsListAdapterNewBindItemBigPic(mContext, baseViewHolder, rowsBean, countDownTimerMap) {


    override fun onGoodsNameWithTags() {
        super.onGoodsNameWithTags()
        baseViewHolder.getView<TextView>(R.id.shop_name).maxLines = 2
    }

    override fun onSpellGroupOrSeckill() {
        super.onSpellGroupOrSeckill()
        processCountDown(baseViewHolder, rowsBean)
    }

    /**
     * @param baseViewHolder
     * @param rowsBean
     *
     *  1. 秒杀
     *  //2. 隐藏规格、厂家
     *  3. 替换拼团价、无折后价，增加划线价
     *  4. 增加拼购比例文字显示、进度显示、参团按钮
     *  5. 隐藏 数据标签、优惠标签、零售价、加购按钮、中包装
     *
     */
    private fun processCountDown(baseViewHolder: YBMBaseHolder, rowsBean: RowsBean) {

        // 倒计时组件
        val stCountdown = baseViewHolder.getView<SeckillTimeView?>(R.id.st_countdown)

        val localDiff: Long = System.currentTimeMillis() - (rowsBean.actSk?.responseLocalTime ?: 0L)
        val leftTime = (rowsBean.actSk?.surplusTime ?: 0) - localDiff
        // 如果遇到刷新数据，要先取消原来的倒计时
        countDownTimerMap.get(baseViewHolder.hashCode())?.let {
            it.cancel()
            countDownTimerMap.remove(baseViewHolder.hashCode())
        }

        // 隐藏 数据标签、优惠标签、零售价、加购按钮、中包装、有效期（后加）、售罄图标（如果在显示的话）
//        baseViewHolder.getView<ShopNameWithTagView?>(R.id.data_tag_list_view)?.visibility = View.GONE
        val dataView = baseViewHolder.getView<ShopNameWithTagView?>(R.id.data_tag_list_view)
        rowsBean.tags?.productTags?.let { dataView?.visibility = View.VISIBLE }
        val allTags = getAllTags()
        dataView?.bindData(allTags)
        baseViewHolder.getView<ShopNameWithTagView?>(R.id.rl_icon_type)?.visibility = View.GONE
        baseViewHolder.getView<ImageView?>(R.id.iv_promotion_more)?.visibility = View.GONE
        baseViewHolder.getView<TextView?>(R.id.tv_retail_price)?.visibility = View.GONE
        baseViewHolder.getView<ProductEditLayoutNew?>(R.id.el_edit)?.apply {
            visibility = if (productNum <= 0) View.GONE else View.VISIBLE
        }
        baseViewHolder.getView<TextView?>(R.id.shop_price_tv)?.visibility = View.GONE
        baseViewHolder.getView<TextView?>(R.id.tv_validity_period)?.visibility = View.GONE
        baseViewHolder.getView<LinearLayout>(R.id.ll_subscribe)?.visibility = View.GONE

        baseViewHolder.getView<SeekBar>(R.id.seckill_progress)?.progress = rowsBean.actSk?.percentage ?: 0
        baseViewHolder.getView<SeekBar>(R.id.seckill_progress)?.isEnabled = false
        if (rowsBean.status == 2 || rowsBean.status == 4 || rowsBean.availableQty <= 0) {
            rowsBean.actSk?.status = 2
        }

        if (leftTime >= 0 && (rowsBean.actSk?.status == 0 || rowsBean.actSk?.status == 1)) {
            // 秒杀倒计时
            val countDownTimer = object : CountDownTimer(leftTime, 1000L) {
                override fun onTick(millisUntilFinished: Long) {
                    stCountdown?.setCountDownData(rowsBean)
                }

                override fun onFinish() {
                    if (rowsBean.actSk?.status == 0) {
                        rowsBean.actSk?.status = 1
                    } else if (rowsBean.actSk?.status == 1) {
                        rowsBean.actSk?.status = 2
                    }
                    baseViewHolder.itemView.post {
                        adapter.notifyItemChanged(baseViewHolder.bindingAdapterPosition)
                    }
                }
            }
            countDownTimerMap.put(baseViewHolder.hashCode(), countDownTimer)
            countDownTimer.start()
        }
        if (rowsBean.actSk?.status == 2) {
            stCountdown?.setCountDownData(rowsBean)
        }

    }

    override fun onFinal() {
        super.onFinal()
        setControl()
    }

    //设置控销
    fun setControl() {
        if (!TextUtils.isEmpty(rowsBean.controlTitle)) {
            //秒杀商品隐藏秒杀加购按钮(未展开)
            val skillBtn = baseViewHolder.getView<TextView>(R.id.tv_seckill_commit)
            skillBtn.visibility = View.GONE
            //秒杀商品隐藏秒杀加购按钮(已展开)
            val skillBtn2 = baseViewHolder.getView<ProductEditLayoutNew>(R.id.el_edit)
            skillBtn2.visibility = View.GONE
            //倒计时
            val time = baseViewHolder.getView<SeckillTimeView>(R.id.st_countdown)
            time.visibility = View.GONE
            //进度条
            val progressBar = baseViewHolder.getView<SeekBar>(R.id.seckill_progress)
            progressBar.visibility = View.GONE
        }
        // <PRD-8>”未签署控销协议商品“显示价格 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=776143984
        // 控销显示商品价格样式
        if (rowsBean.controlType == 5) {//秒杀商品隐藏秒杀加购按钮(未展开)
            val skillBtn = baseViewHolder.getView<TextView>(R.id.tv_seckill_commit)
            skillBtn.visibility = View.VISIBLE
            //秒杀商品隐藏秒杀加购按钮(已展开)
            val skillBtn2 = baseViewHolder.getView<ProductEditLayoutNew>(R.id.el_edit)
            skillBtn2.visibility = View.VISIBLE
            //倒计时
            val time = baseViewHolder.getView<SeckillTimeView>(R.id.st_countdown)
            time.visibility = View.VISIBLE
            //进度条
            val progressBar = baseViewHolder.getView<SeekBar>(R.id.seckill_progress)
            progressBar.visibility = View.VISIBLE
        }
    }

}