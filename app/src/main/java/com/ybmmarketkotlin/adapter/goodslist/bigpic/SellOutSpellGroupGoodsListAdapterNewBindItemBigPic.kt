package com.ybmmarketkotlin.adapter.goodslist.bigpic

import android.content.Context
import android.os.CountDownTimer
import android.util.SparseArray
import android.view.View
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean

/**
 * 拼团
 */
open class SellOutSpellGroupGoodsListAdapterNewBindItemBigPic(
    mContext: Context,
    baseViewHolder: YBMBaseHolder,
    rowsBean: RowsBean,
    countDownTimerMap: SparseArray<CountDownTimer>,
    adapter: RecyclerView.Adapter<*>
): SpellGroupGoodsListAdapterNewBindItemBigPic(mContext, baseViewHolder, rowsBean, countDownTimerMap, adapter) {

    override fun onGoodsNameWithTags() {
        super.onGoodsNameWithTags()
        baseViewHolder.getView<TextView>(R.id.shop_name).maxLines = 2
    }

    override fun onSellOut() {
        super.onSellOut()
        baseViewHolder.getView<LinearLayout>(R.id.ll_subscribe).visibility = View.GONE
    }

    override fun onOffShelf() {
        super.onOffShelf()
        baseViewHolder.getView<LinearLayout>(R.id.ll_subscribe).visibility = View.GONE
    }

    override fun onSpellGroupOrSeckill() {
        super.onSpellGroupOrSeckill()
        //立即参团
        val btn = baseViewHolder.getView<TextView>(R.id.tv_join_groupbooking)
        btn.text = "已抢光"
        btn.setTextColor(ContextCompat.getColor(mContext, R.color.white))
        btn.setBackgroundResource(R.drawable.icon_spell_group_btn_sellout)
        btn.setOnClickListener(null)
        val clSpellGroup = baseViewHolder.getView<ConstraintLayout?>(R.id.cl_groupbooking)
        clSpellGroup?.visibility = View.VISIBLE
        clSpellGroup?.setBackgroundResource(R.drawable.icon_spell_group_btn_sellout_bg)
        val tvGroupbooking = baseViewHolder.getView<TextView?>(R.id.tv_groupbooking)
        tvGroupbooking?.setTextColor(ContextCompat.getColor(mContext, R.color.color_676773))
        //进度条
        val progress = baseViewHolder.getView<ProgressBar>(R.id.progress)
        progress.progressDrawable = ContextCompat.getDrawable(mContext, R.drawable.bg_progressbar_groupbooking_sellout)
        //进度
        val tvGroupbookingProgress = baseViewHolder.getView<TextView?>(R.id.tv_groupbooking_progress)
        tvGroupbookingProgress?.setTextColor(ContextCompat.getColor(mContext, R.color.color_676773))
    }
}