package com.ybmmarketkotlin.adapter.goodslist

/**
 * 商品列表状态
 */
sealed class GoodListBindItemStatus {
    /**
     * 拼团-进行中
     */
    class SpellGroupStatusActive: GoodListBindItemStatus()

    /**
     * 拼团-售罄
     */
    class SpellGroupStatusSellOut: GoodListBindItemStatus()

    /**
     * 拼团-预热
     */
    class SpellGroupStatusPreHot: GoodListBindItemStatus()

    /**
     * 秒杀商品
     */
    class SeckillStatus: GoodListBindItemStatus()

    /**
     * 普通商品
     */
    class NomalStatus: GoodListBindItemStatus()

    /**
     * 批购包邮
     */
    class PGBYStatus: GoodListBindItemStatus()
}