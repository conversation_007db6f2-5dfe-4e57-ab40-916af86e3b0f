package com.ybmmarketkotlin.adapter.goodslist

import android.content.Context
import android.os.CountDownTimer
import android.util.SparseArray
import android.view.View
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean

/**
 * 拼团商品卡片-分类
 */
class SpellGroupGoodsListAdapterNewCategoryBindItem(
    mContext: Context,
    baseViewHolder: YBMBaseHolder,
    rowsBean: RowsBean,
    countDownTimerMap: SparseArray<CountDownTimer>,
    adapter: RecyclerView.Adapter<*>
) : SpellGroupGoodsListAdapterNewBindItem(
    mContext,
    baseViewHolder,
    rowsBean,
    countDownTimerMap,
    adapter
) {

    override fun handleSpellGroupStyle() {
        super.handleSpellGroupStyle()
    }

}