package com.ybmmarketkotlin.adapter

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.util.SparseArray
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.common.BaseYBMApp
import com.ybmmarket20.R
import com.ybmmarket20.activity.BaseProductActivity
import com.ybmmarket20.activity.ProductDetailActivity
import com.ybmmarket20.adapter.YBMBaseListAdapter
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.report.coupon.ICouponEntryType
import com.ybmmarket20.utils.analysis.NewTrackParams
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.utils.analysis.flowDataPageListPageExposureForFeedWithCode
import com.ybmmarket20.utils.analysis.flowDataPageListPageExposureWithCode
import com.ybmmarket20.utils.analysis.getOpenUrlNotJump
import com.ybmmarket20.view.ProductEditLayout
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.view.operationposition.content.goodsState.OPERATION_POSITION_GOODS_BTN_CLICK_TYPE_CARD
import com.ybmmarket20.view.operationposition.track.OPTrackGoodsItem
import com.ybmmarket20.view.operationposition.track.OPTrackManager
import com.ybmmarket20.xyyreport.page.search.SearchProductReport
import com.ybmmarketkotlin.adapter.goodslist.AbstractGoodsListAdapterNewBindItem
import com.ybmmarketkotlin.adapter.goodslist.GoodListBindItemStatus
import com.ybmmarketkotlin.adapter.goodslist.NormalGoodsListAdapterNewBindItem
import com.ybmmarketkotlin.adapter.goodslist.PGBYListAdapterNewBindItem
import com.ybmmarketkotlin.adapter.goodslist.PreSpellGroupGoodsListAdapterNewBindItem
import com.ybmmarketkotlin.adapter.goodslist.SeckillListAdapterNewBindItem
import com.ybmmarketkotlin.adapter.goodslist.SellOutSpellGroupGoodsListAdapterNewBindItem
import com.ybmmarketkotlin.adapter.goodslist.SpellGroupGoodsListAdapterNewBindItem
import com.ybmmarketkotlin.utils.TimeCountDown


/**
 * <AUTHOR> Brin
 * @date : 2020/11/30 - 15:38
 * @Description :
 * @version
 */
open class GoodListAdapterNew(layoutResId: Int, data: MutableList<RowsBean>?, private val isAddCartShowPopupWindow: Boolean = false) :
    YBMBaseListAdapter<RowsBean>(layoutResId, data) {

    constructor(iCouponEntryType: ICouponEntryType?, layoutResId: Int, data: MutableList<RowsBean>?, isAddCartShowPopupWindow: Boolean = false): this(layoutResId, data, isAddCartShowPopupWindow) {
        mCouponEntryType = iCouponEntryType
    }


    var mCouponEntryType: ICouponEntryType? = null
    var pageFrom = ProductEditLayout.FROMPAGE_ACTIVITS
    val countDownTimerMap: SparseArray<CountDownTimer> = SparseArray()
    val traceProductData = SparseArray<String>()
    var newTrackParams: NewTrackParams? = null
    // 商品默认会带有店铺信息入口，某些情况不需要展示，比如：自营店铺、pop店铺
    var isShowShopInfo = true

    //是否配置预热
    var isConfigPreHot = true

    //是否配置拼团售罄
    var isConfigSpellGroupSellOut = true

    //
    var showUnderlinePrice = true

    var productClickTrackListener: ((RowsBean,Int,isBtnClick:Boolean,btnContent:String,number:Int?)->Unit)? = null

    var mItemBgResId = R.drawable.shape_op_bg_radius_nothing

    var isFromSearch = false //是否是搜索页
    var isFromFrequently = false    // 是否来自常购常搜
    var isFromRestocking= false    // 是否来自一键补货

    //是否来自搜索页的sug
    var isFromSearchSug = false
    //是否隐藏列表商品item标签后的查看更多按钮
    var isHiddenPromotionMore = false

    //是否来自购物车去凑单
    var mIsFromShopCartGatherOrders = false

    companion object {
//        private const val TRACK_DURATION = 2 * 60 * 1000 //2分钟内不上报
//        private const val TRACK_DURATION = 0 //不限制时间
    }

    init {
        pageFrom = if (BaseYBMApp.getApp().currActivity != null && BaseYBMApp.getApp().currActivity is BaseProductActivity) {
            ProductEditLayout.FROMPAGE_ACTIVITS
        } else {
            ProductEditLayout.FROMPAGE_HOME
        }

        recyclerView?.addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(v: View) {
                TimeCountDown.get().addCountView(recyclerView, object : TimeCountDown.OnIntervalListener {
                    override fun onNext() {

                    }
                })
            }

            override fun onViewDetachedFromWindow(v: View) {
                TimeCountDown.get().removeCountView(recyclerView)
            }
        })
    }

    constructor(context: Context, layoutResId: Int, data: MutableList<RowsBean>?)
            : this(layoutResId, data) {
        if (mContext == null) mContext = context
    }

    fun bindItemViewWithBackground(baseViewHolder: YBMBaseHolder?, rowsBean: RowsBean?, resId: Int = R.drawable.shape_op_bg_radius_nothing) {
        mItemBgResId = resId
        whenAllNotNull(baseViewHolder, rowsBean) {holder, bean ->
            bindItemView(holder, bean)
        }
    }

    public override fun bindItemView(baseViewHolder: YBMBaseHolder, rowsBean: RowsBean) {
        val goodsListBindItem: AbstractGoodsListAdapterNewBindItem = getGoodsListAdapterNewBindItem(baseViewHolder, rowsBean, countDownTimerMap, this)
                ?: if (rowsBean.isGroupBookingRow is GoodListBindItemStatus.SpellGroupStatusActive) {
                    //拼团
                    getSpellGroupItem(baseViewHolder, rowsBean)
                } else if (rowsBean.isGroupBookingRow is GoodListBindItemStatus.SpellGroupStatusPreHot && isConfigPreHot) {
                    //预热
                    getPreSpellGroupItem(baseViewHolder, rowsBean)
                } else if (rowsBean.isGroupBookingRow is GoodListBindItemStatus.SpellGroupStatusSellOut && isConfigSpellGroupSellOut) {
                    //售罄
                    getSellOutSpellGroupItem(baseViewHolder, rowsBean)

                } else if (rowsBean.isGroupBookingRow is GoodListBindItemStatus.SeckillStatus) {
                    //秒杀
                    SeckillListAdapterNewBindItem(
                            mContext,
                            baseViewHolder,
                            rowsBean,
                            countDownTimerMap,
                            this
                    )

                } else if (rowsBean.isGroupBookingRow is GoodListBindItemStatus.PGBYStatus) {
                    //批购包邮
                    PGBYListAdapterNewBindItem(
                        mContext,
                        baseViewHolder,
                        rowsBean,
                        countDownTimerMap,
                        this
                    )
                } else NormalGoodsListAdapterNewBindItem(mContext, baseViewHolder, rowsBean, countDownTimerMap, isAddCartShowPopupWindow, isHiddenPromotionMore)

//        if (goodsListBindItem is NormalGoodsListAdapterNewBindItem) {
//            goodsListBindItem.setOnAddCartPopupWindowCallback {
//                ListItemAddCartPopWindow(mContext as BaseActivity)
//                    .setObserver()
//                    .setData(rowsBean, baseViewHolder.getView(R.id.shop_name))
//            }
//        }
        goodsListBindItem.apply {
            mCouponEntryType = <EMAIL>
            this.mFlowData = flowData
            this.newTrackParams = newTrackParams
            this.isFromShopCartGatherOrders = <EMAIL>
            this.isFromFrequetly = isFromFrequently
            this.isFromRestocking = <EMAIL>
            //搜索页细分了模块 所以这里手动再改一下子

            this.productClickTrackListener = <EMAIL>
            this.isFromSearchSug = <EMAIL>
            setItemBackground(mItemBgResId)
            //初始化View显隐
            onInitViewVisibility()
            onLoadGoodsIcon()
            //卖点标签
            onHandleSellingPointTag()
            //预热标签
            onHandlePreHotTag()
            //商品标题
            onBuyCount()
            //商品标题
            onGoodsNameWithTags()
            //显示医保
            onGoodsMedicalInsuranceWithTags()
            //商品规格
            onGoodsSpec()
            //厂标签
            onManufacturerTag()
            //效期标签
            onEffectTags()
            //数据标签 ： "60天最低价",8, "区域毛利榜" 10, "比上次购买时降xx元",11, "比加入时降xx元",12, "品类点击榜"
            onDataTags()
            //商品价格
            onGoodsPrice(showUnderlinePrice, true)
            //折后价
            onGoodsDiscountPrice()
            //控销价或零售价
            onRetailPrice()
            //中包装
            onMediumPackage()
            //营销标签
            onMarketingTags()
            //优惠券
            onCoupon()
            //店铺
            onShop(isShowShopInfo)
            //到货提醒
            onArrivalOfGoodsNotify(this@GoodListAdapterNew)
            //售罄
            onSellOut()
            //下架
            onOffShelf()
            //加购
            onAddCart(pageFrom, rowsBean, "${baseViewHolder.bindingAdapterPosition}")
            //价格认证资质可见
            onHandleAuditPassedVisible()
            //普通商品
            onNormal()
            //拼团预热
            onSpellGroupPreHot()
            //拼团或秒杀倒计时
            onSpellGroupOrSeckill()
            //最后调用，执行其他操作
            onFinal()
        }


        // 商品列表点击埋点
        baseViewHolder.setOnClickListener(R.id.ll_item_root) {
            SearchProductReport.trackSearchGoodsClick(mContext, baseViewHolder.bindingAdapterPosition, rowsBean)
            jumpToProductDetail(rowsBean, baseViewHolder)
            if (rowsBean.isOPSingleGoods){
                OPTrackManager.opGoodsBtnClickTrack(OPTrackGoodsItem(rowsBean, baseViewHolder.bindingAdapterPosition, flowData, OPERATION_POSITION_GOODS_BTN_CLICK_TYPE_CARD,0))
            }
            //极光埋点 -  根据判断当前页面是否是店铺内搜索页，如果是店铺搜索场景，移除 action_list_product_click 和 action_product_button_click 事件
            // 首页-猜你喜换跳转常购页面，商品点击
            productClickTrackListener?.invoke(rowsBean, baseViewHolder.bindingAdapterPosition,false,"",null)
        }

        // 商品列表曝光埋点
        traceGoodsExposure(rowsBean, baseViewHolder)
    }

    /**
     * 商品曝光埋点
     */
    open fun traceGoodsExposure(rowsBean: RowsBean, holder: YBMBaseHolder) {
        if (flowData != null && traceProductData[holder.bindingAdapterPosition] == null) {
            flowDataPageListPageExposureWithCode(flowData, rowsBean.productId, rowsBean.showName, rowsBean.sourceType, "${holder.bindingAdapterPosition}", rowsBean.searchSortStrategyCode)
            rowsBean.nsid?.let {
                flowDataPageListPageExposureForFeedWithCode(rowsBean.nsid, rowsBean.sdata
                    ?: "", rowsBean.productId, rowsBean.sourceType, "${holder.bindingAdapterPosition}", rowsBean.searchSortStrategyCode)
            }
            traceProductData.put(holder.bindingAdapterPosition, rowsBean.productId)
        }
    }

    open fun jumpToProductDetail(rowsBean: RowsBean, holder: YBMBaseHolder) {
        XyyIoUtil.track("Test_Product_Action", rowsBean)
        var url = "ybmpage://productdetail?${IntentCanst.PRODUCTID}=${rowsBean.id}&nsid=${rowsBean.nsid ?: ""}&sdata=${rowsBean.sdata ?: ""}&sourceType=${rowsBean.sourceType}&search_sort_strategy_id=${rowsBean.searchSortStrategyCode}&index=${holder.bindingAdapterPosition}"
        getOpenUrlNotJump(
                url,
                flowData)?.let {
            url = it
        }

        val mParams = Bundle().apply {
            putString(IntentCanst.PRODUCTID,rowsBean.id.toString())
            putString("nsid",rowsBean.nsid?:"")
            putString("sdata",rowsBean.sdata?:"")
            putString("sourceType",rowsBean.sourceType)
            putString("search_sort_strategy_id",rowsBean.searchSortStrategyCode)
            putInt("index",holder.adapterPosition)
            putBoolean(IntentCanst.SHOW_GROUPPURCHASE_FLAG, rowsBean.showGroupPurchase())
            putBoolean(IntentCanst.SHOW_ADDITIONALPURCHASE_FLAG, rowsBean.showAdditinalPurchase())
        }

        //这里带参太多了 只能Intent跳了 不能用路由
        val intent = Intent(mContext, ProductDetailActivity::class.java)
        intent.putExtras(mParams)
        mContext.startActivity(intent)
//        RouterJump.jump2ProductDetail(
//                url,
//                mParams)
    }

    override fun setNewData(data: MutableList<Any?>?) {
        super.setNewData(data)
        traceProductData.clear()
    }

    protected open fun getGoodsListAdapterNewBindItem(
            baseViewHolder: YBMBaseHolder,
            rowsBean: RowsBean,
            countDownTimerMap: SparseArray<CountDownTimer>,
            adapter: RecyclerView.Adapter<*>
    ): AbstractGoodsListAdapterNewBindItem? = null


    /**
     * 拼团item
     */
    open fun getSpellGroupItem(baseViewHolder: YBMBaseHolder, rowsBean: RowsBean): SpellGroupGoodsListAdapterNewBindItem {
        return SpellGroupGoodsListAdapterNewBindItem(
            mContext,
            baseViewHolder,
            rowsBean,
            countDownTimerMap,
            this
        ).apply {
            mFlowData = <EMAIL>
        }
    }

    /**
     * 拼团预热
     */
    open fun getPreSpellGroupItem(baseViewHolder: YBMBaseHolder, rowsBean: RowsBean): PreSpellGroupGoodsListAdapterNewBindItem {
        return PreSpellGroupGoodsListAdapterNewBindItem(
            mContext,
            baseViewHolder,
            rowsBean,
            countDownTimerMap
        ).apply {
            setFlowData(flowData)
        }
    }

    /**
     * 拼团售罄
     */
    open fun getSellOutSpellGroupItem(baseViewHolder: YBMBaseHolder, rowsBean: RowsBean): SellOutSpellGroupGoodsListAdapterNewBindItem {
        return SellOutSpellGroupGoodsListAdapterNewBindItem(
            mContext,
            baseViewHolder,
            rowsBean,
            countDownTimerMap,
            this
        )
    }
}