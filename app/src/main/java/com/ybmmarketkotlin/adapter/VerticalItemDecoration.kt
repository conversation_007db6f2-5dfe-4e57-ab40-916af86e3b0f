package com.ybmmarketkotlin.adapter

import android.graphics.Rect
import androidx.recyclerview.widget.RecyclerView
import android.view.View
import com.ybmmarket20.common.util.ConvertUtils

/**
 * <AUTHOR> Brin
 * @date : 2020/11/30 - 16:31
 * @Description :
 * @version
 */
class VerticalItemDecoration : RecyclerView.ItemDecoration() {
    private val space = ConvertUtils.dp2px(1f)

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        if (parent.getChildAdapterPosition(view) >= 0) {
            outRect.top = space
            outRect.bottom = 0
            outRect.left = 0
            outRect.right = 0
        }
    }
}