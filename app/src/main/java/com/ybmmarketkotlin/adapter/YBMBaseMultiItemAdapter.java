package com.ybmmarketkotlin.adapter;

import android.util.Log;
import android.util.SparseArray;
import android.view.ViewGroup;

import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.ybm.app.bean.AbstractMutiItemEntity;
import com.ybm.app.utils.BugUtil;
import com.ybmmarket20.adapter.YBMBaseListAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * 多类型的列表
 */
public abstract class YBMBaseMultiItemAdapter<T extends AbstractMutiItemEntity> extends YBMBaseListAdapter<T> {
    private SparseArray<Integer> layouts;

    public YBMBaseMultiItemAdapter(List<T> data) {
        super(data);
    }

    protected int getDefItemViewType(int position) {
        return ((MultiItemEntity) this.mData.get(position)).getItemType();
    }

    private int getLayoutId(int viewType) {

        int layoutId = 0;
        try {
            layoutId = ((Integer) this.layouts.get(viewType)).intValue();
        } catch (Exception e) {
            e.printStackTrace();
            Log.i("getLayoutId,layouts", layouts.toString());
            Log.i("getLayoutId,viewType", viewType + "");
            Log.i("getLayoutId,intValue", (Integer) this.layouts.get(viewType) + "");
        }
        return layoutId;
    }

    public void addItemType(int type, int layoutResId) {
        if (this.layouts == null) {
            this.layouts = new SparseArray();
        }
        this.layouts.put(type, Integer.valueOf(layoutResId));
    }

    protected BaseViewHolder onCreateDefViewHolder(ViewGroup parent, int viewType) {
        return this.createBaseViewHolder(parent, this.getLayoutId(viewType));
    }

    @Override
    public void setNewData(List data) {
        if (data == null) {
            data = new ArrayList();
        }
        try {
            super.setNewData(data);
        } catch (Throwable e) {
            BugUtil.sendBug(e);
        }
    }
}
