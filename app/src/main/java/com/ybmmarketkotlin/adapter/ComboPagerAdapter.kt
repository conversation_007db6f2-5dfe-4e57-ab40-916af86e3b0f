package com.ybmmarketkotlin.adapter

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter

class ComboPagerAdapter(fm: FragmentManager, //fragment列表
                                private val list_fragment: List<Fragment>, //tab名的列表
                                private val list_Title: List<String>) : FragmentPagerAdapter(fm) {

    override fun getItem(position: Int): Fragment {
        return list_fragment[position]
    }

    override fun getCount(): Int {
        return list_fragment.size
    }

    override fun getPageTitle(position: Int): CharSequence? {
        return list_Title[position]
    }

}