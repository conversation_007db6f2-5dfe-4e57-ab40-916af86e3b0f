package com.ybmmarketkotlin.adapter

import android.os.Handler
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import android.text.TextUtils
import android.view.View
import android.widget.CheckBox
import android.widget.TextView
import com.google.gson.Gson
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.VoucherListBean
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.fragments.VoucherAvailableFragment
import com.ybmmarket20.utils.*
import java.util.*

/**
 * <AUTHOR> Brin
 * @date : 2020/8/5 - 11:19
 * @Description :
 * @version
 */
class VoucherAvailableAdapter2(layoutResId: Int, listData: MutableList<VoucherListBean>, _mTab: Int, handler: Handler) :
    YBMBaseAdapter<VoucherListBean>(layoutResId, listData) {

    val VOUCHER_TEXT = 10
    val VOUCHER_IDS = 20

    var mVoucherText = "请先取消同类型叠加券" //toast提示文案
    private val mHandler: Handler = handler
    private val mTab = _mTab
    var orderTotalAmount = 0.0      // 订单金额
    var totalDiscount = 0.0         // 订单已优惠金额

    fun init() {
        val map: MutableMap<String, String> = HashMap()
        (data as MutableList<VoucherListBean>).forEach {
            if (it.isSelected) {
                map[it.id.toString()] = it.shopCode + ":" + it.shopPatternCode
            }
        }
        val params = Gson().toJson(map)
        mHandler.sendMessage(mHandler.obtainMessage(VOUCHER_IDS, params))
    }

    override fun bindItemView(ybmBaseHolder: YBMBaseHolder, voucherListBean: VoucherListBean) {
        val tvPriceUnit: TextView = ybmBaseHolder.getView(R.id.tv_PriceUnit)
        val tvDiscountUnit: TextView = ybmBaseHolder.getView(R.id.tv_discount_unit)
        val tvCouponAmount: TextView = ybmBaseHolder.getView(R.id.tv_coupon_amount)
        val tvCouponFullReduce: TextView = ybmBaseHolder.getView(R.id.tv_coupon_full_reduce)
        val clGoodsCouponBgLeft: ConstraintLayout = ybmBaseHolder.getView(R.id.cl_goods_coupon_bg_left)
        val tvCouponTitle: TextView = ybmBaseHolder.getView(R.id.tv_coupon_title)
        val tvCouponSubTitle: TextView = ybmBaseHolder.getView(R.id.tv_coupon_subtitle)
        val tvCouponLimt: TextView = ybmBaseHolder.getView(R.id.tv_coupon_limit)
        val tvCouponFullReduceMax: TextView = ybmBaseHolder.getView(R.id.tv_coupon_full_reduce_max)
        val coupon_divider: View = ybmBaseHolder.getView(R.id.coupon_divider)

        tvCouponSubTitle.text = voucherListBean.voucherTitle
        tvCouponLimt.text = voucherListBean.voucherScope
        //checkbox是否显示
        when (mTab) {
            VoucherAvailableFragment.VOUCHER_AVAILABLE -> {
                //优惠券样式-可用还是不可用显示不同UI
                val showSameTime = voucherListBean.canSelected
                if (showSameTime) {
                    tvCouponTitle.setTextColor(ContextCompat.getColor(mContext, R.color.text_292933))
                    tvCouponSubTitle.setTextColor(ContextCompat.getColor(mContext, R.color.text_292933))
                    tvCouponLimt.setTextColor(ContextCompat.getColor(mContext, R.color.text_676773))
                    tvCouponAmount.setTextColor(mContext.resources.getColor(R.color.color_ff4244))
                    tvPriceUnit.setTextColor(mContext.resources.getColor(R.color.color_ff4244))
                    tvDiscountUnit.setTextColor(mContext.resources.getColor(R.color.color_ff4244))
                    tvCouponFullReduce.setTextColor(mContext.resources.getColor(R.color.color_ff4244))
                    tvCouponFullReduceMax.setTextColor(mContext.resources.getColor(R.color.color_ff4244))
                    setTitleAndTag(
                        mContext,
                        voucherListBean.getVoucherType(),
                        voucherListBean.getShopName(),
                        voucherListBean.getVoucherTypeDesc(),
                        tvCouponTitle
                    )
                    coupon_divider.background = mContext.resources.getDrawable(R.drawable.shape_coupon_imaginary_line)
                    clGoodsCouponBgLeft.background = mContext.resources.getDrawable(R.drawable.shape_item_goods_coupon_left)
                } else {
                    tvCouponTitle.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
                    tvCouponSubTitle.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
                    tvCouponLimt.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
                    tvCouponAmount.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
                    tvPriceUnit.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
                    tvDiscountUnit.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
                    tvCouponFullReduce.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
                    tvCouponFullReduceMax.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
                    setTitleTag(
                        mContext,
                        tvCouponTitle,
                        R.drawable.shape_coupon_type_tag_gray,
                        R.color.white,
                        voucherListBean.getShopName(),
                        voucherListBean.getVoucherTypeDesc()
                    )
                    coupon_divider.background = mContext.resources.getDrawable(R.drawable.shape_coupon_imaginary_line_gray)
                    clGoodsCouponBgLeft.background = mContext.resources.getDrawable(R.drawable.shape_item_goods_coupon_left_gray)
                }
                ybmBaseHolder.getView<CheckBox>(R.id.voucher_check).visibility = View.VISIBLE
            }
            VoucherAvailableFragment.WISH_DISABLED -> {
                tvCouponTitle.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
                tvCouponSubTitle.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
                tvCouponLimt.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
                tvCouponAmount.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
                tvPriceUnit.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
                tvDiscountUnit.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
                tvCouponFullReduce.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
                tvCouponFullReduceMax.setTextColor(ContextCompat.getColor(mContext, R.color.color_9494A6))
                ybmBaseHolder.getView<CheckBox>(R.id.voucher_check).visibility = View.GONE
                setTitleTag(
                    mContext,
                    tvCouponTitle,
                    R.drawable.shape_coupon_type_tag_gray,
                    R.color.white,
                    voucherListBean.getShopName(),
                    voucherListBean.getVoucherTypeDesc()
                )
                coupon_divider.background = mContext.resources.getDrawable(R.drawable.shape_coupon_imaginary_line_gray)
                clGoodsCouponBgLeft.background = mContext.resources.getDrawable(R.drawable.shape_item_goods_coupon_left_gray)
            }
        }

        //只有不可用优惠券会显示不可用优惠券提示
        val tvUnusedTip = ybmBaseHolder.getView<TextView>(R.id.tv_unUsed_tip)
        ybmBaseHolder.setVisible(R.id.tv_unUsed_tip, !TextUtils.isEmpty(voucherListBean.canNotUseReason))
        tvUnusedTip.text = voucherListBean.canNotUseReason?: ""

        if (voucherListBean.voucherState == 1) {
            // 折扣
            tvPriceUnit.visibility = View.GONE
            tvDiscountUnit.visibility = View.VISIBLE
            tvCouponAmount.text = voucherListBean?.discount ?: ""
        } else {
            tvPriceUnit.visibility = View.VISIBLE
            tvDiscountUnit.visibility = View.GONE
            tvCouponAmount.text = UiUtils.transformInt(voucherListBean.moneyInVoucher)
        }

        //根据是否是叠加券显示按钮
        val isVoucherDemo = !TextUtils.isEmpty(voucherListBean.voucherDemo)

        // 最高减显示文案
        if (!voucherListBean.maxMoneyInVoucherDesc.isNullOrEmpty()) {
            tvCouponFullReduceMax.visibility = View.VISIBLE
            tvCouponFullReduceMax.text = voucherListBean.maxMoneyInVoucherDesc
        } else {
            tvCouponFullReduceMax.visibility = View.GONE
        }

        //有效时间-叠加券文案
        if (isVoucherDemo) {
            ybmBaseHolder.setText(R.id.tv_coupon_date, voucherListBean.voucherDemo)
        } else {
            ybmBaseHolder.setText(
                R.id.tv_coupon_date,
                "${DateTimeUtil.getCouponDateTime(voucherListBean.validDate)}-${DateTimeUtil.getCouponDateTime(voucherListBean.expireDate)}"
            )
        }
        //优惠券 适用金额限制条件
        ybmBaseHolder.setText(R.id.tv_coupon_full_reduce, voucherListBean.minMoneyToEnableDesc)
        setCheckBox(ybmBaseHolder, voucherListBean)

    }

    /**
     * 设置选中状态
     *
     * @param ybmBaseHolder
     * @param itemBean
     */
    private fun setCheckBox(ybmBaseHolder: YBMBaseHolder, itemBean: VoucherListBean) {
        val clRoot = ybmBaseHolder.getView<ConstraintLayout>(R.id.cl_root)
        val checkBox = ybmBaseHolder.getView<CheckBox>(R.id.voucher_check)
        //是否可以点击
        checkBox.isEnabled = itemBean.canSelected
        //选中状态
        checkBox.isChecked = itemBean.isSelected
        //
        clRoot.setOnClickListener(View.OnClickListener {
            when (mTab) {
                // 可用优惠券
                VoucherAvailableFragment.VOUCHER_AVAILABLE -> {
                    //当前item不可点击
                    if (!itemBean.canSelected) {
                        ToastUtils.showShort(mVoucherText)
                        return@OnClickListener
                    }
                    // 如果是叠加券，可选择状态，要计算 (当前优惠金额+本次勾选优惠券金额) 是否大于订单金额，如果优惠金额大于订单金额，给一个提示
                    if (itemBean.voucherType == 6 && !checkBox.isChecked) {
                        itemBean.isSelected = true
                        if (MathUtils.add(totalDiscount, itemBean.moneyInVoucher) > orderTotalAmount) {
                            AlertDialogEx(mContext).apply {
                                setCancelButton("放弃使用") { dialog, _ ->
                                    checkBox.isChecked = false
                                    itemBean.isSelected = false
                                    dialog?.dismiss()
                                }
                                setConfirmButton("确定使用") { dialog, button ->
                                    itemBean.isSelected = true
                                    init()
                                    dialog?.dismiss()
                                }
                                setMessage("选中的叠加券优惠金额已超过应付总额，是否确认使用")
                                setCanceledOnTouchOutside(false)
                                setTitle(null)
                                show()
                                return@OnClickListener
                            }
                        }
                    }

                    itemBean.isSelected = !checkBox.isChecked
                    init()
                }
                // 不可用优惠券
                VoucherAvailableFragment.WISH_DISABLED -> {
                }
            }
        })
    }


}