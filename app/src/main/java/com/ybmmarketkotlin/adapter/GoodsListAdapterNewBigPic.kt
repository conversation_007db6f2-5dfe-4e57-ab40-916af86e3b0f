package com.ybmmarketkotlin.adapter

import android.content.Context
import android.os.CountDownTimer
import android.util.SparseArray
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.common.BaseYBMApp
import com.ybmmarket20.R
import com.ybmmarket20.activity.BaseProductActivity
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.utils.analysis.flowDataPageListPageExposure
import com.ybmmarket20.utils.analysis.flowDataPageListPageExposureForFeed
import com.ybmmarket20.view.ProductEditLayout
import com.ybmmarket20.xyyreport.page.search.SearchProductReport
import com.ybmmarketkotlin.adapter.goodslist.GoodListBindItemStatus
import com.ybmmarketkotlin.adapter.goodslist.bigpic.AbstractGoodsListAdapterNewBindItemBigPic
import com.ybmmarketkotlin.adapter.goodslist.bigpic.NormalGoodsListAdapterNewBindItemBigPic
import com.ybmmarketkotlin.adapter.goodslist.bigpic.PGBYListAdapterNewBindItemBigPic
import com.ybmmarketkotlin.adapter.goodslist.bigpic.PreSpellGroupGoodsListAdapterNewBindItemBigPic
import com.ybmmarketkotlin.adapter.goodslist.bigpic.SeckillListAdapterNewBindItemBigPic
import com.ybmmarketkotlin.adapter.goodslist.bigpic.SellOutSpellGroupGoodsListAdapterNewBindItemBigPic
import com.ybmmarketkotlin.adapter.goodslist.bigpic.SpellGroupGoodsListAdapterNewBindItemBigPic
import com.ybmmarketkotlin.utils.RouterJump
import com.ybmmarketkotlin.utils.TimeCountDown

open class GoodsListAdapterNewBigPic(layoutResId: Int, data: MutableList<RowsBean>?, private val isAddCartShowPopupWindow: Boolean = false) :
    YBMBaseAdapter<RowsBean>(layoutResId, data) {

    var pageFrom = ProductEditLayout.FROMPAGE_ACTIVITS
    private val countDownTimerMap: SparseArray<CountDownTimer> = SparseArray()
    var flowData: BaseFlowData? = null
    private val traceProductData = SparseArray<String>()

    // 商品默认会带有店铺信息入口，某些情况不需要展示，比如：自营店铺、pop店铺
    var isShowShopInfo = true

    //是否配置预热
    var isConfigPreHot = true

    //是否配置拼团售罄
    var isConfigSpellGroupSellOut = true

    //
    var showUnderlinePrice = true
    var productClickTrackListener: ((RowsBean,Int,Int?)->Unit)? = null
    var trackContext: Context? = null


    init {
        pageFrom = if (BaseYBMApp.getApp().currActivity != null && BaseYBMApp.getApp().currActivity is BaseProductActivity) {
            ProductEditLayout.FROMPAGE_ACTIVITS
        } else {
            ProductEditLayout.FROMPAGE_HOME
        }

        recyclerView?.addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(v: View) {
                TimeCountDown.get().addCountView(recyclerView, object : TimeCountDown.OnIntervalListener {
                    override fun onNext() {

                    }
                })
            }

            override fun onViewDetachedFromWindow(v: View) {
                TimeCountDown.get().removeCountView(recyclerView)
            }
        })
    }

    constructor(context: Context, layoutResId: Int, data: MutableList<RowsBean>?)
            : this(layoutResId, data) {
        if (mContext == null) mContext = context
    }

    public override fun bindItemView(baseViewHolder: YBMBaseHolder, rowsBean: RowsBean) {
        val goodsListBindItem: AbstractGoodsListAdapterNewBindItemBigPic = getGoodsListAdapterNewBindItem(baseViewHolder, rowsBean, countDownTimerMap, this)
            ?: if (rowsBean.isGroupBookingRow is GoodListBindItemStatus.SpellGroupStatusActive) {
                //拼团
                SpellGroupGoodsListAdapterNewBindItemBigPic(
                    mContext,
                    baseViewHolder,
                    rowsBean,
                    countDownTimerMap,
                    this
                ).apply {
                    mFlowData = <EMAIL>
                }
            } else if (rowsBean.isGroupBookingRow is GoodListBindItemStatus.SpellGroupStatusPreHot && isConfigPreHot) {
                //预热
                PreSpellGroupGoodsListAdapterNewBindItemBigPic(
                    mContext,
                    baseViewHolder,
                    rowsBean,
                    countDownTimerMap
                ).apply {
                    setFlowData(flowData)
                }
            } else if (rowsBean.isGroupBookingRow is GoodListBindItemStatus.SpellGroupStatusSellOut && isConfigSpellGroupSellOut) {
                //售罄
                SellOutSpellGroupGoodsListAdapterNewBindItemBigPic(
                    mContext,
                    baseViewHolder,
                    rowsBean,
                    countDownTimerMap,
                    this
                )

            } else if (rowsBean.isGroupBookingRow is GoodListBindItemStatus.SeckillStatus) {
                //秒杀
                SeckillListAdapterNewBindItemBigPic(
                    mContext,
                    baseViewHolder,
                    rowsBean,
                    countDownTimerMap,
                    this
                )

            } else if (rowsBean.isGroupBookingRow is GoodListBindItemStatus.PGBYStatus) {
                //批购包邮
                PGBYListAdapterNewBindItemBigPic(
                    mContext,
                    baseViewHolder,
                    rowsBean,
                    countDownTimerMap,
                    this
                )
            } else NormalGoodsListAdapterNewBindItemBigPic(mContext, baseViewHolder, rowsBean, countDownTimerMap, isAddCartShowPopupWindow)

//        if (goodsListBindItem is NormalGoodsListAdapterNewBindItem) {
//            goodsListBindItem.setOnAddCartPopupWindowCallback {
//                ListItemAddCartPopWindow(mContext as BaseActivity)
//                    .setObserver()
//                    .setData(rowsBean, baseViewHolder.getView(R.id.shop_name))
//            }
//        }
        goodsListBindItem.apply {

            productClickTrackListener = <EMAIL>
            this.mFlowData = flowData
            //初始化View显隐
            onInitViewVisibility()
            onLoadGoodsIcon()
            //卖点标签
            onHandleSellingPointTag()
            //预热标签
            onHandlePreHotTag()
            //商品标题
            onBuyCount()
            //商品标题
            onGoodsNameWithTags()
            //商品规格
            onGoodsSpec()
            //厂标签
            onManufacturerTag()
            //效期标签
            onEffectTags()
            //数据标签 ： "60天最低价",8, "区域毛利榜" 10, "比上次购买时降xx元",11, "比加入时降xx元",12, "品类点击榜"
            onDataTags()
            //商品价格
            onGoodsPrice(showUnderlinePrice, true)
            //折后价
            onGoodsDiscountPrice()
            //控销价或零售价
            onRetailPrice()
            //中包装
            onMediumPackage()
            //营销标签
            onMarketingTags()
            //优惠券
            onCoupon()
            //店铺
            onShop(isShowShopInfo)
            //到货提醒
            onArrivalOfGoodsNotify(this@GoodsListAdapterNewBigPic)
            //售罄
            onSellOut()
            //下架
            onOffShelf()
            //加购
            onAddCart(pageFrom, rowsBean, "${baseViewHolder.bindingAdapterPosition}")
            //价格认证资质可见
            onHandleAuditPassedVisible()
            //普通商品
            onNormal()
            //拼团预热
            onSpellGroupPreHot()
            //拼团或秒杀倒计时
            onSpellGroupOrSeckill()
            //最后调用，执行其他操作
            onFinal()
        }


        // 商品列表点击埋点
        baseViewHolder.setOnClickListener(R.id.ll_item_root) {
            trackContext?.let { it1 -> SearchProductReport.trackSearchGoodsClick(it1, baseViewHolder.bindingAdapterPosition, rowsBean) }
            XyyIoUtil.track("Test_Product_Action", rowsBean)
            var url = "ybmpage://productdetail?${IntentCanst.PRODUCTID}=${rowsBean.id}&nsid=${rowsBean.nsid ?: ""}&sdata=${rowsBean.sdata ?: ""}&sourceType=${rowsBean.sourceType}"
            RouterJump.jump2ProductDetail(url)

            productClickTrackListener?.invoke(rowsBean,baseViewHolder.bindingAdapterPosition,null)
        }

        // 商品列表曝光埋点
        if (flowData != null && traceProductData[baseViewHolder.bindingAdapterPosition] == null) {
            flowDataPageListPageExposure(flowData, rowsBean.productId, rowsBean.showName, rowsBean.sourceType, "${baseViewHolder.bindingAdapterPosition}")
            rowsBean.nsid?.let {
                flowDataPageListPageExposureForFeed(rowsBean.nsid, rowsBean.sdata
                    ?: "", rowsBean.productId, rowsBean.sourceType, "${baseViewHolder.bindingAdapterPosition}")
            }
            traceProductData.put(baseViewHolder.bindingAdapterPosition, rowsBean.productId)
        }
    }

    override fun setNewData(data: MutableList<Any?>?) {
        super.setNewData(data)
        traceProductData.clear()
    }

    protected open fun getGoodsListAdapterNewBindItem(
        baseViewHolder: YBMBaseHolder,
        rowsBean: RowsBean,
        countDownTimerMap: SparseArray<CountDownTimer>,
        adapter: RecyclerView.Adapter<*>
    ): AbstractGoodsListAdapterNewBindItemBigPic? = null
}