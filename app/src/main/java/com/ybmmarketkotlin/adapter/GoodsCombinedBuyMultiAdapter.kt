package com.ybmmarketkotlin.adapter

import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.adapter.YBMBaseListAdapter
import com.ybmmarket20.bean.GroupPurchaseInfo
import com.ybmmarketkotlin.views.combinedbuy.CombinedBuyListener
import com.ybmmarketkotlin.views.combinedbuy.CombinedBuyMultiLayout

/**
 * <AUTHOR>
 * @desc    大搜加价购
 * @date    2025/5/6
 */
class GoodsCombinedBuyMultiAdapter(purchaseInfos: MutableList<GroupPurchaseInfo>?) :
    YBMBaseListAdapter<GroupPurchaseInfo>(R.layout.adapter_combinedbuy_multi, purchaseInfos) {
    var mListener: CombinedBuyListener? = null

    public override fun bindItemView(baseViewHolder: YBMBaseHolder, purchaseInfo: GroupPurchaseInfo) {
        val layoutMulti = baseViewHolder.itemView.findViewById<CombinedBuyMultiLayout>(R.id.layoutMulti)
        layoutMulti.setNewData(purchaseInfo)
        layoutMulti.mListener = mListener
    }

    public override fun bindItemView(baseViewHolder: YBMBaseHolder, t: GroupPurchaseInfo, payloads: List<Any?>) {
        super.bindItemView(baseViewHolder, t, payloads)
        payloads.forEach {
            it?:return@forEach
            val layoutMulti = baseViewHolder.itemView.findViewById<CombinedBuyMultiLayout>(R.id.layoutMulti)
            layoutMulti.setNewData(it as GroupPurchaseInfo)
        }
    }
}