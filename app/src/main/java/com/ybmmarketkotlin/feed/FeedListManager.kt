package com.ybmmarketkotlin.feed

import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.bean.NetError
import com.ybmmarket20.bean.*
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.AdapterUtils
import java.lang.reflect.Type

/**
 * <AUTHOR> Brin
 * @date : 2020/12/8 - 16:19
 * @Description :
 * @version
 */
class FeedListManager<E : BaseFeedBean<T>, T>
@JvmOverloads constructor(
        var rv: RecyclerView,
        var adapter: YBMBaseAdapter<T>,
        url: String,
        params: RequestParams?,
        beanType: Type,
        var response: BaseResponse<T>? = null) {

    private var loadMoreParams: RequestParams? = null
    private var initParams: RequestParams? = null
    private var url: String? = null
    private var beanType: Type? = null

    init {
        this.url = url
        this.beanType = beanType
        initParams = params
        adapter.setOnLoadMoreListener(OnloadMoreListener(), rv)
    }

    fun start() {
        getInitData()
    }

    private fun getInitData() {
        adapter?.data?.let { it.clear() }
        HttpManager.getInstance().postListData(url, initParams, response
                ?: BaseFeedResponse<E, T>())
    }

    private fun getMoreData() {
        HttpManager.getInstance().postListData(url, loadMoreParams, response
                ?: BaseFeedResponse<E, T>())
    }


    inner class OnloadMoreListener : BaseQuickAdapter.RequestLoadMoreListener {
        override fun onLoadMoreRequested() {
            getMoreData()
        }

    }

    inner class BaseFeedResponse<T : BaseFeedBean<V>, V> : BaseResponse<T>() {
        override fun onSuccess(content: String?, obj: BaseBean<T>?, t: T?) {
            t?.let {
                loadMoreParams = it.requestParams
                t?.getDataList()?.let { datalist -> adapter?.data?.addAll(datalist) }
                if (it.isEnd) {
                    //adapter.setEnableLoadMore(false)
                    adapter.loadMoreEnd()
                } else {
                    adapter.loadMoreComplete()
                }
                t?.getDataList()?.let { it ->
                    AdapterUtils.getAfterDiscountPrice(it as MutableList<RowsBean>, adapter)
                }
            }


        }

        override fun onFailure(error: NetError) {
            super.onFailure(error)
        }

        override fun json(content: String, type: Type): BaseBean<*>? {
            return super.json(content, beanType)
        }
    }


}