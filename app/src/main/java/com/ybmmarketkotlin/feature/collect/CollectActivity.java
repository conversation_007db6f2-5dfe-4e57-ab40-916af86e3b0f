package com.ybmmarketkotlin.feature.collect;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.viewpager.widget.ViewPager;

import com.flyco.tablayout.SlidingTabLayout;
import com.github.mzule.activityrouter.annotation.Router;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.BaseProductActivity;
import com.ybmmarket20.constant.IntentCanst;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 我的收藏
 */
@Router({"collectactivity", "collectactivity/:currentid"})
public class CollectActivity extends BaseProductActivity  {

    @Bind(R.id.ps_tab_new)
    SlidingTabLayout ps_tab_new;
    @Bind(R.id.vp_client)
    ViewPager mVpClient;
    @Bind(R.id.tv_edit)
    TextView tv_edit;

    private ArrayList<String> mList_title;
    private List<Fragment> list_fragment;
    private CollectAdapter mAdapter;
    private Handler mHandler;
    private int position = 0;
    private boolean isEdit = true;
    private String mTabId;

    @Override
    protected void initData() {
        super.initData();
        setTitle("我的收藏");
        setTabFilterColor();
        mTabId = getIntent().getStringExtra("currentid");
        initFragmentTitle();

        mAdapter = new CollectAdapter(getSupportFragmentManager(), mList_title, list_fragment);
        mVpClient.setAdapter(mAdapter);
        mVpClient.setOffscreenPageLimit(mList_title.size() + 1);
        ps_tab_new.setViewPager(mVpClient);

        if (!TextUtils.isEmpty(mTabId)) {
            int i = Integer.parseInt(mTabId);
            position = i;
            mVpClient.setCurrentItem(i);
        }
        mVpClient.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {

            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {

                if (CollectActivity.this.position != position) {
                    isEdit = false;
                    CollectActivity.this.position = position;
                }
                setPositionListener(position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });

        initReceiver();
    }

    private void initReceiver() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(IntentCanst.ACTION_DEL_FAVORITE);
        LocalBroadcastManager.getInstance(this).registerReceiver(mBroadcastReceiver, intentFilter);
    }

    /*
     * 广播
     * */
    private BroadcastReceiver mBroadcastReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (IntentCanst.ACTION_DEL_FAVORITE.equals(action)){
                tv_edit.callOnClick();
            }
        }
    };

    @Override
    protected void onDestroy() {
        super.onDestroy();
        LocalBroadcastManager.getInstance(this).unregisterReceiver(mBroadcastReceiver);
    }

    private void setTabFilterColor() {
//        YBMAppLike.changeThemeBg(R.drawable.base_header_dynamic_bg, mPsTab);
    }

    private void initFragmentTitle() {
        mList_title = new ArrayList<>();
        list_fragment = new ArrayList<>();
        mList_title.add("全部");
        mList_title.add("已降价");
        mList_title.add("已有货");
        mList_title.add("已过期");
        for (int a = 0; a < mList_title.size(); a++) {
//            mPsTab.addTab(mPsTab.newTab().setText(mList_title.get(a)));
            String tabName = mList_title.get(a);
            CollectFragment mFragment = CollectFragment.getInstance(a,tabName);
            list_fragment.add(a, mFragment);
        }

    }

    @Override
    protected String getRawAction() {
        return "ybmpage://collectactivity/";
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_collect;
    }

    private class CollectAdapter extends FragmentPagerAdapter {

        private List<Fragment> list_fragment;     //fragment列表
        private List<String> list_Title;                              //tab名的列表

        public CollectAdapter(FragmentManager fm, List<String> list_Title, List<Fragment> mFragments) {
            super(fm);
            this.list_Title = list_Title;
            this.list_fragment = mFragments;
        }

        @Override
        public Fragment getItem(int position) {
            return list_fragment.get(position);
        }

        @Override
        public int getCount() {
            return list_Title.size();
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return list_Title.get(position);
        }
    }

    @OnClick({R.id.tv_edit})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tv_edit:
                setPositionListener(position);
                break;
        }
    }

    /**
     * 点击编辑根据tab的position对Fragment传递参数
     *
     * @param position tab位置
     */
    public void setPositionListener(int position) {
        switch (position) {
            case 0:
                if (fragment0ClickedListener != null) {
                    fragment0ClickedListener.onClicked(isEdit);
                    isEdit = !isEdit;
                }
                break;
            case 1:
                if (fragment1ClickedListener != null) {
                    fragment1ClickedListener.onClicked(isEdit);
                    isEdit = !isEdit;
                }
                break;
            case 2:
                if (fragment2ClickedListener != null) {
                    fragment2ClickedListener.onClicked(isEdit);
                    isEdit = !isEdit;
                }
                break;
            case 3:
                if (fragment3ClickedListener != null) {
                    fragment3ClickedListener.onClicked(isEdit);
                    isEdit = !isEdit;
                }
                break;
        }
        if (isEdit) {
            tv_edit.setText("编辑");
        } else {
            tv_edit.setText("完成");
        }
    }

    public void setHandler(Handler handler) {
        this.mHandler = handler;
    }

    private OnFragment0ClickedListener fragment0ClickedListener;

    public interface OnFragment0ClickedListener {
        void onClicked(boolean isEdit);
    }

    public void setFragment0ClickedListener(OnFragment0ClickedListener fragment0ClickedListener) {
        this.fragment0ClickedListener = fragment0ClickedListener;
    }

    private OnFragment1ClickedListener fragment1ClickedListener;

    public interface OnFragment1ClickedListener {
        void onClicked(boolean isEdit);
    }

    public void setFragment1ClickedListener(OnFragment1ClickedListener fragment1ClickedListener) {
        this.fragment1ClickedListener = fragment1ClickedListener;
    }

    private OnFragment2ClickedListener fragment2ClickedListener;

    public interface OnFragment2ClickedListener {
        void onClicked(boolean isEdit);
    }

    public void setFragment2ClickedListener(OnFragment2ClickedListener fragment2ClickedListener) {
        this.fragment2ClickedListener = fragment2ClickedListener;
    }

    private OnFragment3ClickedListener fragment3ClickedListener;

    public interface OnFragment3ClickedListener {
        void onClicked(boolean isEdit);
    }

    public void setFragment3ClickedListener(OnFragment3ClickedListener fragment3ClickedListener) {
        this.fragment3ClickedListener = fragment3ClickedListener;
    }

    @Override
    protected void cartClick() {
        super.cartClick();
    }
}

