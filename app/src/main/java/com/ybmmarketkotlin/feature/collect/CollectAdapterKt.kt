package com.ybmmarketkotlin.feature.collect

import android.view.View
import android.widget.CheckBox
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarketkotlin.adapter.GoodListAdapterNew

class CollectAdapterKt(layoutResId: Int, rows: MutableList<RowsBean>) : GoodListAdapterNew(layoutResId, rows) {

    val selectPosition: MutableList<Int> by lazy(::mutableListOf)
    var showChecked = false

    override fun bindItemView(baseViewHolder: YBMBaseHolder, rowsBean: RowsBean) {
        super.bindItemView(baseViewHolder, rowsBean)
        val checkBox = baseViewHolder?.getView<CheckBox>(R.id.checkbox)
        checkBox?.visibility = if (showChecked) View.VISIBLE else View.GONE
        checkBox?.isChecked = selectPosition.contains(baseViewHolder.absoluteAdapterPosition)
        checkBox?.setOnCheckedChangeListener { buttonView, isChecked ->

            baseViewHolder.absoluteAdapterPosition?.let {
                if (isChecked && !selectPosition.contains(it)) {
                    selectPosition.add(it)
                }
                if (!isChecked && selectPosition.contains(it)) {
                    selectPosition.remove(it)
                }
            }

        }
    }

}