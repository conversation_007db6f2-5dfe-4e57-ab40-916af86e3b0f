package com.ybmmarketkotlin.bean

import com.google.gson.annotations.SerializedName
import com.ybmmarket20.bean.ShopTab
import com.ybmmarket20.bean.TagBean

data class ShopBaseInfo(
        var shopHomeUrl: String?,
        var shopName: String?,
        var shopCode: String?,
        var shopLogoUrl: String?,
        var freightTips: String?,
        var freightJumpUrl: String?,    //
        var iMPackUrl: String?,         // 客服
        var shopNoticeUrl: String?,     // 采购须知
        var shareLink: String?,     // 分享链接
        var shareDesc: String?,      //  分享文案

        var salesVolumeDesc: String?,     //  发货xxx件
        var salesVolume: String?,     //  发货xxx件
        var shelvesDesc: String?,      //  上架xxx种
        var shelves: String?,     //  分享文案
        var shopPropTags: List<TagBean>?,
        var shopServiceQualityTags: List<TagBean>?,
        var shopPatternCode: String? = null // 店铺种类

        /*INDUSTRY("industry", "工业"),

        POP("pop", "第三方"),

        YBM("ybm", "药帮忙"),

        YKQ("ykq", "宜块钱"),

        PERSONAL("personal", "自然人"),

        VIRTUAL("virtual", "虚拟供应商"),*/

) {
        @SerializedName("relateOrgs")
        val shopTabList: List<ShopTab>? = null
}