package com.ybmmarketkotlin.bean

import com.google.gson.annotations.SerializedName
import com.ybmmarket20.bean.Rows2Bean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.homesteady.SpellGroupGoods
import com.ybmmarket20.utils.analysis.FlowData

class CommodityComboBean {
    var description //套餐描述
            : String? = ""
    var descriptionPCUrl //套餐pc调转路径
            : String? = ""
    var descriptionUrl //套餐app调转路径
            : String? = ""
    var discountPrice //优惠金额
            : String? = ""
    var packageId //套餐id
            : String? = ""
    var packagePrice //套餐价格
            : String? = ""
    var sourceType //套餐来源 1:平台2.店铺
            = 0
    var subtotalPrice //套餐小计
            : String? = ""
    var skuList //套餐内商品
            : List<ComboRowsBean>? = null
    var packageTitle //套餐title
            : String? = ""
    var skuNum //套餐内商品数量
            : String? = ""
}


class CommodityGroupRecommondBean(
    var licenseStatus: Int?,     // 用户的资质状态
    var jumpName: String?,      // 跳转文案名称
    var jumpUrl: String?,       // 跳转链接
    var mainTitle: String?,     // 主标题
    var subTitleUrl: String?,   // 副标题图片URL，绝对路径
    var rows: MutableList<SpellGroupGoods>?,  // 相似品列表
    val cmsPageParam: CmsPageParam?
)

data class CmsPageParam(
    var sptype: String?,
    var spid: String?,
    var sid: String?
)

data class SameSpecificationsListResponse(
        val rows:List<RowsBean>? = arrayListOf(),
        @SerializedName("scmE")
        val scmId: String?
)