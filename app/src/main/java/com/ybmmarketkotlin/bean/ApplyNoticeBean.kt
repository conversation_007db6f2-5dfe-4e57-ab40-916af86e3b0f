package com.ybmmarketkotlin.bean

data class ApplyNoticeBean(
    var status: Int,                // 1 线上待支付、2 线下待支付已上传凭证、3 线下待支付未上传凭证, 500 非法状态
    var statusDesc: String?,        //支付状态描述
    var payType: Int,               // 支付类型(1:在线支付 2:货到付款 3:线下转账 4:银行授信支付 5自有账期）
    var evidenceVerifyStatus: Int,   // 凭证状态0-待上传电汇凭证、1-待审核电汇凭证、2-审核通过
    var iconUrl: String?,
    var orderId: String?,           // 订单ID
    var orderNo: String?,           // 订单号
    var countDownNewTime: Long,     //剩余支付时间，单位：秒
    var refundOrderId: String, //退款单id
    var refundOrderNo: String //退款单编号
)
