package com.ybmmarketkotlin.bean

import com.ybmmarket20.bean.BaseFeedBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.TagBean


data class ShopListBean(var shopListInfo: MutableList<ShopItemBean>) : BaseFeedBean<ShopItemBean>() {

    override fun getDataList(): MutableList<ShopItemBean> {
        return shopListInfo
    }
}

data class ShopItemBean(
    var qualityTag: TagBean?,
    var shopPropTags: List<TagBean>?,
    var shopCode: String?,
    var orgId: String?,
    var showName: String?,
    var appLogo: String?,
    var shelves: Int?,
    var shelvesDesc: String?,
    var salesVolume: String?,
    var salesVolumeDesc: String?,
    var newAppLink: String?,
    var freightTips: String?,
    var activityInfo: MutableList<ActivityInfo>?,
    var productInfo: MutableList<RowsBean>?,
    var shopPatternCode: String? //店铺类型
)


// 营销信息
data class ActivityInfo(var activityType: Int,      // 1券  4 促
                        var activityTypeDesc: String,
                        var activityContent: String)