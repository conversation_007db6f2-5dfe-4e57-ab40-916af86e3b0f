package com.ybmmarketkotlin.bean

import android.os.Parcel
import android.os.Parcelable
import com.ybmmarket20.utils.AuditStatusSyncUtil

class ComboRowsBean : Parcelable {
    /**
     * barcode : 1049002000089
     * commonName : 消渴降糖胶囊
     * fob : 4.8
     * imageUrl : 1049002000089-1.jpg
     * isControl : 0
     * isOEM : false
     * isPurchase : true
     * packageId : 5780
     * personalLimit : 0
     * productNumber : 10
     * purchase : true
     * setMealMoney : 48.0
     * showAgree : 1
     * signStatus : 0
     * skuId : 609474
     * spec : 0.3g*10s*4板
     * totalLimit : 0
     */
    var barcode: String? = null
    var commonName: String? = null
    var fob: String? = "0"
    var imageUrl: String? = null
    var isControl = 0
    var isOEM: Boolean? = null
    var isPurchase = false
    var isControlAgreement = 0
    var packageId: String? = null
    var personalLimit = 0
    var productNumber: String? = null
    var setMealMoney: String? = null
    var showAgree = 0
    var signStatus = 0
    var skuId: String? = null
    var spec: String? = null
    var totalLimit = 0


    //region 商品标签列表重构
    /**
     * 0 : 正常展示价格
     * 1 ： 展示 "暂无购买权限"
     * 2 :  展示  "价格签署协议可见"
     * 3 :  展示  "价格认证资质可见"
     *
     * @return
     */
    fun showPriceType(): Int {
        var showType = 0
        if (isControl == 1 && !isPurchase) {
            showType = 1
        }
        if (isOEM == false && isControlAgreement == 1 && showAgree == 0 ||
            isOEM == true && signStatus == 0 && isControlAgreement == 1 && showAgree == 0 ||
            isOEM == true && signStatus == 0 && isControlAgreement == 0
        ) {
            showType = 2
        }
        if (!AuditStatusSyncUtil.getInstance().isAuditFirstPassed) {
            showType = 3
        }
        return showType
    }


    override fun describeContents(): Int {
        return 0
    }

    override fun writeToParcel(dest: Parcel, flags: Int) {
        dest.writeString(barcode)
        dest.writeString(commonName)
        dest.writeString(fob)
        dest.writeString(imageUrl)
        dest.writeInt(isControl)
        dest.writeByte(if (isPurchase) 1.toByte() else 0.toByte())
        dest.writeString(packageId)
        dest.writeInt(personalLimit)
        dest.writeString(productNumber)
        dest.writeString(setMealMoney)
        dest.writeInt(showAgree)
        dest.writeInt(signStatus)
        dest.writeString(skuId)
        dest.writeString(spec)
        dest.writeInt(totalLimit)
    }

    constructor() {}
    protected constructor(`in`: Parcel) {
        barcode = `in`.readString()
        commonName = `in`.readString()
        fob = `in`.readString()
        imageUrl = `in`.readString()
        isControl = `in`.readInt()
        isPurchase = `in`.readByte().toInt() != 0
        packageId = `in`.readString()
        personalLimit = `in`.readInt()
        productNumber = `in`.readString()
        setMealMoney = `in`.readString()
        showAgree = `in`.readInt()
        signStatus = `in`.readInt()
        skuId = `in`.readString()
        spec = `in`.readString()
        totalLimit = `in`.readInt()
    }

    companion object {
        @JvmField
        val CREATOR: Parcelable.Creator<ComboRowsBean> = object : Parcelable.Creator<ComboRowsBean> {
            override fun createFromParcel(source: Parcel): ComboRowsBean? {
                return ComboRowsBean(source)
            }

            override fun newArray(size: Int): Array<ComboRowsBean?> {
                return arrayOfNulls(size)
            }
        }
    }
}