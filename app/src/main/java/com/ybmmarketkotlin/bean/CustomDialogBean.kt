package com.ybmmarketkotlin.bean

import com.google.android.exoplayer.extractor.mp4.Track
import com.google.gson.annotations.SerializedName
import com.ybmmarket20.bean.CouponRowBean
import com.ybmmarket20.bean.CouponScopeGoods
import com.ybmmarket20.xyyreport.spm.TrackData


data class CustomDialogNewBean(
    val detail: CouponDialogDataNewBean?,
    val sceneType: String?,
    val style: Int
)

data class CouponDialogDataNewBean(
    val imageDtos: List<ImageDialogBean>?,
    val couponDtos: List<CouponRowBean>?,
    val receiveCouponCenterUrl: String?,
    val couponColorDto: CouponColorBean?,
    val pageType: String?,
    val pageId: String?,
    val pageName: String?,
    val trackData: TrackData?,
    @SerializedName("imageInfo")
    val customImageTrackDataInfo: CustomTrackDataInfo?,
    @SerializedName("couponInfo")
    val customCouponTrackData: CustomTrackDataInfo?
)

data class CustomTrackDataInfo(
    val trackData: TrackData?
)

data class CustomDialogBean(
    var detail: CustomDialogDetailBean
)

data class CustomDialogDetailBean(
    val imageDtos: List<ImageDialogBean>?,
    val couponDtos: List<CouponRowBean>?,
    val receiveCouponCenterUrl:String?,
    val couponColorDto:CouponColorBean?
)

data class ImageDialogBean(
        val action: String = "",
        val imgUrl: String = "",
        val imageColor: String = "",//背景色
        val isHyaline: String = "",//是否是透明，true透明，false不透明，为false时，imageColor必有值
        val componentPosition: String?,
        val componentName: String?,
        val componentTitle: String?,
        val trackData: TrackData?
)
data class CouponColorBean(
    val couponColor: String = "",//背景色
    val isHyaline: String = ""//是否是透明，true透明，false不透明，为false时，couponColor必有值
)

//dialog中返回，sceneType=3时返回
data class CouponDialogData(
    val imageDtos: List<ImageDialogBean>?,
    val list: List<CouponRowBeanWrap>?,
    val receiveCouponCenterUrl:String?,
    val couponColorDto:CouponColorBean?
)

class CouponRowBeanWrap(
    appUrl: String?,
    discount: Double,
    discountStr: String?,
    expireDate: Long,
    fanliDesc: String?,
    id: Int,
    isUse: Int,
    minMoneyToEnable: String?,
    minMoneyToEnableDesc: String?,
    moneyInVoucher: Double,
    pcUrl: String?,
    state: Int,
    skuRelationType: Int,
    validDate: Long,
    voucherId: String,
    voucherInstructions: String?,
    voucherState: Int,
    voucherTemplateId: String?,
    templateId: String?,
    voucherTitle: String?,
    voucherType: Int,
    voucherTypeDesc: String?,
    voucherUsageWay: String?,
    maxMoneyInVoucherDesc: String?,
    voucherSkuImages: MutableList<CouponScopeGoods>?,
    isUnFold: Boolean,
    shopName: String?,
    var activityState: Int,
    validDayStr: String?,
    rightsType: Int, //权益类型:1券 2 红包
    redPacketOriginMoney: String?, //红包可用金额
    templateName: String?, //名称（红包
    validDateToString: String?, //红包生效时间
    expireDateToString: String?, //失效时间
    marketingId: String?,
    componentPosition: String?,
    componentName: String?,
    componentTitle: String?//活动id
    , trackData: TrackData?
): CouponRowBean(
    appUrl,
    discount,
    discountStr,
    expireDate,
    fanliDesc,
    id,
    isUse,
    minMoneyToEnable,
    minMoneyToEnableDesc,
    moneyInVoucher,
    pcUrl,
    state,
    skuRelationType,
    validDate,
    voucherId,
    voucherInstructions,
    voucherState,
    voucherTemplateId,
    templateId,
    voucherTitle,
    voucherType,
    voucherTypeDesc,
    voucherUsageWay,
    maxMoneyInVoucherDesc,
    voucherSkuImages,
    isUnFold,
        shopName,
        validDayStr,
        "",
        0,
        "",
        rightsType,
        redPacketOriginMoney,
        templateName,
        validDateToString,
        expireDateToString,
        marketingId,
        componentPosition,
        componentName,
        componentTitle,
        trackData
) {

    fun initWrapData():CouponRowBeanWrap {
        if (rightsType == 0) {
            //券
            state = if (activityState == 2) 1 else if (activityState == 3) 2 else activityState
            discountStr = if (moneyInVoucher > 0) "$discount" else ""
        }
        return this
    }
}

fun CouponDialogData.convertToCustomDialogBean(): CustomDialogBean {
    return CustomDialogBean(
        CustomDialogDetailBean(
            imageDtos,
            list?.map { it.initWrapData() },
            receiveCouponCenterUrl,
            couponColorDto
        )
    )
}