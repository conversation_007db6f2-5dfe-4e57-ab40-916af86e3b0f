package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.*
import com.ybmmarket20.bean.AccountBean
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.VerifyCodeBean
import com.ybmmarket20.db.AccountTable
import com.ybmmarket20.network.request.JDPayRequest
import com.ybmmarket20.utils.SpUtil
import com.ybmmarketkotlin.utils.ChCrypto
import kotlinx.coroutines.launch

class SetPayPWViewModel(private val app: Application): AndroidViewModel(app) {

    private val _userLiveData = MutableLiveData<AccountBean>()
    val userLiveData: LiveData<AccountBean> = _userLiveData

    private val _verifyCodeLiveData = MutableLiveData<BaseBean<VerifyCodeBean>>()
    val verifyCodeLiveData: LiveData<BaseBean<VerifyCodeBean>> = _verifyCodeLiveData

    private val _jdPWLiveData = MutableLiveData<BaseBean<Any>>()
    val jdPWLiveData: LiveData<BaseBean<Any>> = _jdPWLiveData

    /**
     * 通过merchantid获取用户信息
     */
    fun getUserInfoByMerchantId() {
        viewModelScope.launch {
            val accountInfo = AccountTable.getAccount(app, SpUtil.getMerchantid())?: AccountBean()
            val mobile = SpUtil.getLoginPhone()
            accountInfo.phone = mobile
            _userLiveData.postValue(accountInfo)
        }
    }

    /**
     * 发送验证码
     */
    fun sendVerifyCode() {
        viewModelScope.launch {
            val sendVerifyCode =
                JDPayRequest().sendVerifyCode(_userLiveData.value?.phone ?: "", "1")
            _verifyCodeLiveData.postValue(sendVerifyCode)
        }
    }

    /**
     * 设置支付密码
     */
    fun setJDPayPassword(pwd: String, verifyCode: String) {
        viewModelScope.launch {
            val jdPayPassword =
                JDPayRequest().setJDPayPassword(ChCrypto.jdPayAesEncrypt(pwd), _userLiveData.value?.phone ?: "", verifyCode)
            _jdPWLiveData.postValue(jdPayPassword)
        }
    }
}