package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CompanyLicenseBean
import com.ybmmarket20.bean.aftersales.AfterSalesInfo
import com.ybmmarket20.bean.aftersales.AfterSalesLicense
import com.ybmmarket20.bean.aftersales.AfterSalesLicenseGoods
import com.ybmmarket20.bean.aftersales.AfterSalesNoBean
import com.ybmmarket20.bean.aftersales.AfterSalesTips
import com.ybmmarket20.bean.aftersales.AfterSalesUploadImage
import com.ybmmarket20.network.request.LicenseRequirementRequest
import kotlinx.coroutines.launch

class AfterSalesLicenseViewModel(appLike: Application) : BaseViewModel(appLike) {

    private val _licenseRequirementLiveData = MutableLiveData<AfterSalesLicense>()
    val licenseRequirementLiveData: LiveData<AfterSalesLicense> = _licenseRequirementLiveData

    private val _licenseSubmitResultLiveData = MutableLiveData<BaseBean<AfterSalesNoBean>>()
    val licenseSubmitResultLiveData: LiveData<BaseBean<AfterSalesNoBean>> = _licenseSubmitResultLiveData

    private val mGoodsList: MutableList<AfterSalesLicenseGoods> = mutableListOf()

    private var mKeyWord = ""

    fun getLicense(orderNo: String, orgName: String) {
        viewModelScope.launch {
            val companyLicense = LicenseRequirementRequest().getCompanyLicenseList()
            val licenseGoodsList = LicenseRequirementRequest().getLicenseGoodsList(orderNo)
            if (licenseGoodsList.isSuccess && !licenseGoodsList.data.isNullOrEmpty()) {
                mGoodsList.addAll(licenseGoodsList.data.map { it.mappingData() })
            }
            val afterSalesLicense = AfterSalesLicense(
                licenseGoods = mGoodsList,
                licenseCompany = if (companyLicense.isSuccess) {
                    companyLicense.data.apply {
                        labelTitle = orgName
                    }
                } else {
                    CompanyLicenseBean(orgName, mutableListOf())
                },
                afterSalesTips = AfterSalesTips(),
                afterSalesUploadImage = AfterSalesUploadImage(),
                ""
            )
            _licenseRequirementLiveData.postValue(afterSalesLicense)
        }
    }

    fun searchGoods(keyWord: String) {
        val afterSalesLicense = _licenseRequirementLiveData.value ?: return
        if (afterSalesLicense.licenseGoods.isEmpty() && keyWord.startsWith(mKeyWord)) return
        mKeyWord = keyWord
        afterSalesLicense.licenseGoods = mGoodsList.filter {
            it.goodsName?.contains(keyWord) == true
        }.toMutableList()
        _licenseRequirementLiveData.postValue(afterSalesLicense)
    }

    fun submitLicenseInfo(orderNo: String) {
        val afterSalesLicense = _licenseRequirementLiveData.value ?: return
        //补充说明
        val remark = afterSalesLicense.afterSalesTips.tips ?: ""
        //图片地址
        val uploadPathList = afterSalesLicense.afterSalesUploadImage.imagePathList?.let {
            Gson().toJson(it).toString()
        }
        //企业相关资质
        val corpCredential =
            afterSalesLicense.licenseCompany.items?.filter { it.isSelected }?.joinToString { it.itemType ?: "" }

        //商品资质
        val productCredential = afterSalesLicense.licenseGoods
            .filter { it.checkReportState || it.licenseState }
            .map {
                val str = mutableListOf<String>().apply {
                    if (it.checkReportState) add("1")
                    if (it.licenseState) add("2")
                }.joinToString(",")
                return@map mapOf(
                    "skuId" to it.skuId,
                    "credentialType" to str
                )
            }.filter { !it["credentialType"].isNullOrEmpty() }
            .let {
                Gson().toJson(it, object: TypeToken<List<Map<String, String>>>(){}.type).toString()
            }

        val paramsMap = mapOf(
            "remarks" to remark,
            "evidences" to uploadPathList,
            "corpCredential" to corpCredential,
            "productCredential" to productCredential,
            "orderNo" to orderNo
        ).filter { entry -> !entry.value.isNullOrEmpty() }
        viewModelScope.launch {
            val submitLicenseAfterSalesResult =
                LicenseRequirementRequest().submitLicenseAfterSales(paramsMap as Map<String, String>)
            _licenseSubmitResultLiveData.postValue(submitLicenseAfterSalesResult)
        }
    }
}