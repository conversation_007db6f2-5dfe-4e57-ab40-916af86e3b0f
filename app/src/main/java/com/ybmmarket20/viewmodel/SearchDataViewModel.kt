package com.ybmmarket20.viewmodel

import android.text.TextUtils
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.EmptyBean
import com.ybmmarket20.bean.RebateVoucherStartActInfoBean
import com.ybmmarket20.bean.RefreshWrapperPagerBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SearchDynamicLabelConfig
import com.ybmmarket20.bean.SearchFindBean
import com.ybmmarket20.bean.searchfilter.FullSearchFilterBean
import com.ybmmarket20.bean.searchfilter.SearchFilterContentBean
import com.ybmmarket20.bean.searchfilter.SearchFilterDrugType
import com.ybmmarket20.bean.searchfilter.SearchFilterTitleBean
import com.ybmmarket20.network.request.SearchDataRequest
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.view.ManufacturersPopupWindow
import com.ybmmarket20.view.searchFilter.adapter.SEARCH_FILTER_DATA_TYPE_DRUG_TYPE
import com.ybmmarket20.view.searchFilter.adapter.SEARCH_FILTER_DATA_TYPE_PRICE_RANGE
import com.ybmmarket20.view.searchFilter.adapter.SEARCH_FILTER_DATA_TYPE_SERVICE
import com.ybmmarket20.view.searchFilter.adapter.SEARCH_FILTER_DATA_TYPE_TITLE
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 搜索
 */
class SearchDataViewModel : ViewModel() {

    private val _recommendLiveData = MutableLiveData<BaseBean<RefreshWrapperPagerBean<RowsBean>>>()
    val recommendLiveData: LiveData<BaseBean<RefreshWrapperPagerBean<RowsBean>>> =
        _recommendLiveData

    //搜索发现
    private val _searchFindLiveData = MutableLiveData<BaseBean<SearchFindBean>>()
    val searchFindLiveData: LiveData<BaseBean<SearchFindBean>> = _searchFindLiveData

    val searchStartLiveData = MutableLiveData<Unit>()

    //动态标签
    private val _dynamicLabelConfigLiveData = MutableLiveData<List<SearchDynamicLabelConfig>>()
    val dynamicLabelConfigLiveData: LiveData<List<SearchDynamicLabelConfig>> = _dynamicLabelConfigLiveData

    //动态标签选中
    private val _dynamicLabelSelectedLiveData = MutableLiveData<Map<String, String>>()
    val dynamicLabelSelectedLiveData: LiveData<Map<String, String>> = _dynamicLabelSelectedLiveData

    //领券接口
    private val _voucherLiveData = MutableLiveData<BaseBean<EmptyBean>>()
    val voucherLiveData: LiveData<BaseBean<EmptyBean>> = _voucherLiveData
    //查询消费返活动标签
    private val _rebateLiveData = MutableLiveData<BaseBean<RebateVoucherStartActInfoBean>>()
    val rebateLiveData: LiveData<BaseBean<RebateVoucherStartActInfoBean>> = _rebateLiveData


    fun getRecommendSearchData(params: Map<String, String>?) {
        val lParams = params ?: mutableMapOf()
        viewModelScope.launch(Dispatchers.IO) {
            val searchData = SearchDataRequest().getRecommendSearchDataRequest(lParams)
            if (searchData.data != null) {
                _recommendLiveData.postValue(searchData)
            }
        }
    }

    private fun generateDrugsClassStr(searchFilterData: ManufacturersPopupWindow.SearchFilterData): String {
        val drugsClassList = mutableListOf<String>()
        if (searchFilterData.isClassA) {
            drugsClassList.add("1")
        }
        if (searchFilterData.isClassB) {
            drugsClassList.add("2")
        }
        if (searchFilterData.isClassRx) {
            drugsClassList.add("3")
        }
        if (searchFilterData.isClassElse) {
            drugsClassList.add("4")
        }
        if (drugsClassList.isNotEmpty()) {
            return drugsClassList.joinToString(",")
        }
        return ""
    }

    /**
     * 获取搜索服务数据
     */
    private fun getSearchFilterServiceData(): Pair<FullSearchFilterBean, FullSearchFilterBean> {
        val serviceTitle = SearchFilterTitleBean()
        serviceTitle.itemType = SEARCH_FILTER_DATA_TYPE_TITLE
        serviceTitle.title = "服务"
        val serviceContent = SearchFilterContentBean(Any())
        serviceContent.itemType = SEARCH_FILTER_DATA_TYPE_SERVICE
        return Pair(serviceTitle, serviceContent)
    }

    /**
     * 获取搜索价格区间数据
     */
    private fun getSearchFilterPriceRangeData(): Pair<FullSearchFilterBean, FullSearchFilterBean> {
        val priceRangeTitle = SearchFilterTitleBean()
        priceRangeTitle.itemType = SEARCH_FILTER_DATA_TYPE_TITLE
        priceRangeTitle.title = "价格区间"
        val priceRangeContent = SearchFilterContentBean(Any())
        priceRangeContent.itemType = SEARCH_FILTER_DATA_TYPE_PRICE_RANGE
        return Pair(priceRangeTitle, priceRangeContent)
    }

    /**
     * 生成药品类型数据
     */
    private fun generateDrugTypeData(): List<FullSearchFilterBean> {
        return mutableListOf("甲类OTC", "乙类OTC", "处方药RX", "其他").mapIndexed {index, bean ->
            SearchFilterContentBean(SearchFilterDrugType(bean, "${index + 1}")).also {
                it.itemType = SEARCH_FILTER_DATA_TYPE_DRUG_TYPE
            }
        }
    }

    /**
     * 获取搜索发现数据
     */
    fun getSearchFindData() {
        viewModelScope.launch {
            val searchFindBean = SearchDataRequest().getSearchFind()
            _searchFindLiveData.postValue(searchFindBean)
        }
    }

    /**
     * 领取优惠券跳转倒凑单页
     */
    fun getVoucher(voucherTemplateId: String?) {
        viewModelScope.launch {
            if (!TextUtils.isEmpty(voucherTemplateId)) {
                val paramsMap = mutableMapOf<String, String>()
                paramsMap["merchantId"] = SpUtil.getMerchantid()
                paramsMap["voucherTemplateId"] = voucherTemplateId!!
                val voucher = SearchDataRequest().getVoucher(paramsMap)
                _voucherLiveData.postValue(voucher)
            }
        }
    }
    /**
     * 查询消费返活动标签
     */
    fun getStartActTags() {
        viewModelScope.launch {
            val value = SearchDataRequest().getStartActTags()
            _rebateLiveData.postValue(value)
        }
    }

    /**
     * 更新动态标签
     */
    fun updateSearchDynamicLabelConfig(labelConfig: List<SearchDynamicLabelConfig>) {
        _dynamicLabelConfigLiveData.postValue(labelConfig)
    }

    /**
     * 更新动态标签选中
     */
    fun updateSearchDynamicLabelSelected(selectedMap: Map<String, String>) {
        _dynamicLabelSelectedLiveData.postValue(selectedMap)
    }

    /**
     * 更新当前已选中动态标签
     */
    fun updateSearchCurrentDynamicLabelSelected() {
        mutableMapOf<String, String>().apply {
            putAll(_dynamicLabelSelectedLiveData.value?: mapOf())
            _dynamicLabelSelectedLiveData.postValue(this)
        }
    }

}