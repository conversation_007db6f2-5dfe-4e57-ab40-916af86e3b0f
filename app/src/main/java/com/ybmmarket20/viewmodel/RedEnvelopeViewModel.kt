package com.ybmmarket20.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.RedEnvelopeData
import com.ybmmarket20.bean.RedEnvelopeRecordData
import com.ybmmarket20.bean.RedEnvelopeResult
import com.ybmmarket20.network.request.RedEnvelopeRequest
import kotlinx.coroutines.launch

/**
 * 我的红包
 */
class RedEnvelopeViewModel: ViewModel() {

    private var redEnvelopeLiveData = MutableLiveData<RedEnvelopeData?>()
    val _redEnvelopeLiveData = redEnvelopeLiveData

    private var redEnvelopeRecordLiveData = MutableLiveData<RedEnvelopeRecordData?>()
    val _redEnvelopeRecordLiveData = redEnvelopeRecordLiveData

    /**
     * 获取我的红包数据
     */
    fun getRedEnvelopeList(queryStatus: String, pageNum: String, pageSize: String = "") {
        viewModelScope.launch {
            val redEnvelopeResult = RedEnvelopeRequest().getRedEnvelopeList(queryStatus, pageNum, pageSize)
            if (redEnvelopeResult.isSuccess) {
                redEnvelopeLiveData.postValue(redEnvelopeResult.data)
            } else {
                redEnvelopeLiveData.postValue(null)
            }
        }
    }

    /**
     * 获取红包收支记录
     */
    fun getRedEnvelopeRecordList(tradeType: String, pageNum: String, pageSize: String = "") {
        viewModelScope.launch {
            val redEnvelopeRecordResult = RedEnvelopeRequest().getRedEnvelopeRecordList(tradeType, pageNum, pageSize)
            if (redEnvelopeRecordResult.isSuccess) {
                redEnvelopeRecordLiveData.postValue(redEnvelopeRecordResult.data)
            } else {
                redEnvelopeRecordLiveData.postValue(null)
            }
        }
    }
}