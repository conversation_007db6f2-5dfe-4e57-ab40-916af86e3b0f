package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.utils.SpUtil
import kotlinx.coroutines.launch

class SendInvoiceByEmailViewModel: ViewModel() {

    private val _sendEmailLiveData = MutableLiveData<BaseBean<Any>>()
    val sendEmailLiveData: LiveData<BaseBean<Any>> = _sendEmailLiveData

    /**
     * 发送订单的发票电子邮件
     */
    fun sendInvoiceEmailForOrder(orderId: String, email: String) {
        viewModelScope.launch {
            val sendMailResult = NetworkService.instance.sendMail(SpUtil.getMerchantid(), orderId, email)
            _sendEmailLiveData.postValue(sendMailResult)
        }
    }
}