package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybm.app.common.SmartExecutorManager
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.PurchaseAmountBean
import com.ybmmarket20.bean.PurchaseBean
import com.ybmmarket20.network.request.PurchaseRequest
import com.ybmmarket20.utils.DateTimeUtil
import com.ybmmarketkotlin.utils.TimeUtils
import kotlinx.coroutines.launch
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

data class PurchaseDate(
    var year: String,
    var month: String,
    var timeStamp: Long,
    var yearList: List<String>,
    var monthList: List<String>,
    var currentMonthList: List<String>
)

class PurchaseViewModel: ViewModel() {
    //时间
    private val _purchaseDataLiveDate = MutableLiveData<PurchaseDate>()
    val purchaseDataLiveDate: LiveData<PurchaseDate> = _purchaseDataLiveDate
    //采购对账单金额
    private val _purchaseAmountLiveData = MutableLiveData<BaseBean<PurchaseAmountBean>>()
    val purchaseAmountLiveData: LiveData<BaseBean<PurchaseAmountBean>> = _purchaseAmountLiveData
    //采购对账单列表
    private val _purchaseListLiveData = MutableLiveData<BaseBean<PurchaseBean>>()
    val purchaseListLiveData: LiveData<BaseBean<PurchaseBean>> = _purchaseListLiveData

    /**
     * 通过远程获取当前时间
     */
    private suspend fun getRemoteDate() = suspendCoroutine<Long> {
        SmartExecutorManager.getInstance().execute {
            val time = DateTimeUtil.getBaiduTime()
            it.resume(time)
        }
    }

    /**
     * 获取对账单时间
     */
    fun getPurchaseDate() {
        viewModelScope.launch {
            var timeStamp: Long = getRemoteDate()
            if (timeStamp == 0L) {
                //获取远程时间失败
                timeStamp = System.currentTimeMillis()
            }
            val endYear = TimeUtils.getYear(timeStamp).toInt()
            val endMonth = TimeUtils.getMonth(timeStamp).toInt()
            val yearList = (2020..endYear).map { "$it" }
            val monthList = (1..12).map{"$it"}
            val currentMonthList = (1..endMonth).map { "$it" }
            val purchaseDate = PurchaseDate("$endYear", "$endMonth", timeStamp, yearList, monthList, currentMonthList)
            _purchaseDataLiveDate.postValue(purchaseDate)
        }
    }

    /**
     * 获取对账单金额
     */
    fun getPurchaseAmount(year: String, month: String) {
        viewModelScope.launch {
            val purchaseAllAmount = PurchaseRequest().getPurchaseAllAmount(year, month)
            _purchaseAmountLiveData.postValue(purchaseAllAmount)
        }
    }

    /**
     * 获取采购对账单列表
     */
    fun getPurchaseList(year: String, month: String, offset: String, limit: String) {
        viewModelScope.launch {
            val purchaseBean = PurchaseRequest().getPurchaseList(year, month, offset, limit)
            _purchaseListLiveData.postValue(purchaseBean)
        }
    }
}