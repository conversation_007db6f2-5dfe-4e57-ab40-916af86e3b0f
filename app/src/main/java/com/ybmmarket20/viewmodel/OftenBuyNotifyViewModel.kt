package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel

/**
 * <AUTHOR>
 * @date 2022/12/2
 * @description 常购清单Fragment通知
 */
class OftenBuyNotifyViewModel: ViewModel() {
    private val _oftenBuyLiveData = MutableLiveData<Any>()
    val oftenBuyLiveData: LiveData<Any> = _oftenBuyLiveData

    fun notifyOftenData() {
        _oftenBuyLiveData.postValue(Any())
    }
}