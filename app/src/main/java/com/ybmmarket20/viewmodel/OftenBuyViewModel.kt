package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybm.app.bean.AbstractMutiItemEntity
import com.ybmmarket20.bean.*
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.network.request.SearchDataRequest
import com.ybmmarket20.network.request.SearchRequest
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @description 常购清单
 */
class OftenBuyViewModel : ViewModel() {

    var oftenBuyListParams: HashMap<String, String>? = null
    var oftenBuyRecommendListParams: HashMap<String, String>? = null

    var mList = mutableListOf<AbstractMutiItemEntity>()

    // 常购清单列表是否加载完成
    var isOftenBuyListEnd = false
    // 常购清单推荐列表是否加载完成
    var isOftenBuyRecommendListEnd = false

    var oftenBuyListJob: Job? = null

    private val _oftenBuyListLiveData = MutableLiveData<BaseBean<OftenBuyItemData>>()
    val oftenBuyItemLiveData: LiveData<BaseBean<OftenBuyItemData>> = _oftenBuyListLiveData

    private val _oftenBuyRecommendLiveData = MutableLiveData<BaseBean<RefreshWrapperPagerBean<OftenBuyRecommendRowsBean>>>()
    val oftenBuyRecommendListLiveData: MutableLiveData<BaseBean<RefreshWrapperPagerBean<OftenBuyRecommendRowsBean>>> = _oftenBuyRecommendLiveData

    /**
     * 获取常购清单数据和推荐数据
     */
    fun getOftenBuyListData(
        thirtyDays: String,
        order: String,
    ) {
        oftenBuyListJob = viewModelScope.launch {
            if (!isOftenBuyListEnd) {
                getOftenBuyList(thirtyDays, order)
            } else {
                getOftenBuyRecommendList()
            }
        }
    }

    /**
     * 获取常购清单数据
     */
    private suspend fun getOftenBuyList(thirtyDays: String, order: String) {
        oftenBuyListParams = oftenBuyListParams?: hashMapOf(
            "thirtyDays" to thirtyDays,
            "order" to order
        )
        val searchOftenBuyListData = SearchRequest().searchOftenBuyList(oftenBuyListParams!!.toMap())
        if (searchOftenBuyListData.isSuccess) {
            isOftenBuyListEnd = searchOftenBuyListData.data.isEnd
            oftenBuyListParams = searchOftenBuyListData.data.requestParam?: hashMapOf()
            searchOftenBuyListData.data.rows?.let { mList.addAll(it) }
        }
        if (isOftenBuyListEnd) {
            //添加空白页
            if (mList.isEmpty()) {
                mList.add(OftenBuyEmptyBean())
            }
            //添加猜你喜欢标题
            mList.add(OftenBuyRecommendTitle(""))
        }
        _oftenBuyListLiveData.postValue(searchOftenBuyListData)
    }

    /**
     * 获取常购清单推荐列表
     */
    private suspend fun getOftenBuyRecommendList() {
        oftenBuyRecommendListParams = oftenBuyRecommendListParams?: hashMapOf("sptype" to "3", "pageType" to "7", "spFrom" to "33")
        val oftenBuyRecommendListData = SearchDataRequest().getRecommendOftenBuyService(oftenBuyRecommendListParams!!.toMap())
        if (oftenBuyRecommendListData.isSuccess) {
            isOftenBuyRecommendListEnd = oftenBuyRecommendListData.data.isEnd
            oftenBuyRecommendListParams = oftenBuyRecommendListData.data.requestParam?.let {
                HashMap(it)
            }
            oftenBuyRecommendListData.data.rows?.let { mList.addAll(it) }
        }
        _oftenBuyRecommendLiveData.postValue(oftenBuyRecommendListData)
    }

    //购物车数量
    fun getCartNumber(): String {
        val num = YBMAppLike.cartNum
        return if (num > 0) {
            if (num > 99) "99+"
            else "$num"
        } else ""
    }
}