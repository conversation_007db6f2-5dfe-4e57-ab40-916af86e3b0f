package com.ybmmarket20.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.FindSameGoodsResultBean
import com.ybmmarket20.bean.SearchResultBean
import com.ybmmarket20.network.request.GoodsListRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 商品列表
 */
class GoodsListViewModel: ViewModel() {

    val findSameGoodsLiveData = MutableLiveData<FindSameGoodsResultBean?>()

    fun getGoodsListFindSameGoods(paramsMap: MutableMap<String, String>) {
        viewModelScope.launch(Dispatchers.IO) {
            val goodsListFindSameGoods = GoodsListRequest().getGoodsListFindSameGoods(paramsMap)
            if (goodsListFindSameGoods?.data != null) {
                findSameGoodsLiveData.postValue(goodsListFindSameGoods.data)
            }
        }
    }
}