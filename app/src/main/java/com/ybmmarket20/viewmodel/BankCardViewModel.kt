package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BankCardItem
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.network.request.JDPayRequest
import kotlinx.coroutines.launch

class BankCardViewModel: ViewModel() {

    private val _bankCardListLiveData: MutableLiveData<BaseBean<MutableList<BankCardItem>>> = MutableLiveData()
    val bankCardListLiveData: LiveData<BaseBean<MutableList<BankCardItem>>> = _bankCardListLiveData

    fun getBankCardList() {
        viewModelScope.launch {
            val bankCardList = JDPayRequest().getBankCardList()
            _bankCardListLiveData.postValue(bankCardList)
        }
    }
}