package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.EmptyBean
import com.ybmmarket20.network.request.AccountInfoRequest
import kotlinx.coroutines.launch

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2025/6/26 10:04
 *    desc   :
 */
class BindWXChatViewModel(app: Application): BaseViewModel(app) {
    private val _bindWechatLiveData = MutableLiveData<BaseBean<EmptyBean>>()
    val bindWxChatLiveData: LiveData<BaseBean<EmptyBean>> = _bindWechatLiveData
    /**
     * 绑定微信
     */
    fun bindWechatCustomer(code: String) {
        viewModelScope.launch {
            val bindValue = AccountInfoRequest().bindWechatCustomer(code)
            _bindWechatLiveData.postValue(bindValue)
        }
    }
}