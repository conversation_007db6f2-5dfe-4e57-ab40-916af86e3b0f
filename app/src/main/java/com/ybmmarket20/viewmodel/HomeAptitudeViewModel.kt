package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.HomeAptitudeStatus
import com.ybmmarket20.home.newpage.bean.HomeFeedStreamResponse
import com.ybmmarket20.home.newpage.bean.HomeModulesResponse
import com.ybmmarket20.home.newpage.bean.HomeSearchContentResponse
import com.ybmmarket20.home.newpage.bean.TabBeanResponse
import com.ybmmarket20.network.request.HomeAptitudeRequest
import com.ybmmarket20.network.request.NewHomeRequest
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine

class HomeAptitudeViewModel(app: Application): BaseViewModel(app) {

    //是否加载过底部导航栏
    var isLoadHomeBottomTab = false

    private val _homeAptitudeStatusLiveData = MutableLiveData<BaseBean<HomeAptitudeStatus>>()
    val homeAptitudeStatusLiveData: LiveData<BaseBean<HomeAptitudeStatus>> = _homeAptitudeStatusLiveData

    //搜索热词
    private val _searchHotListLiveData = MutableLiveData<BaseBean<MutableList<HomeSearchContentResponse>>>()
    val searchHotListLiveData: LiveData<BaseBean<MutableList<HomeSearchContentResponse>>> = _searchHotListLiveData

    //tab列表，Boolean 是否来自缓存数据
    private val _tabListLiveData = MutableLiveData<BaseBean<TabBeanResponse>>()
    //Boolean 是否来自缓存数据
    val tabListLiveData: LiveData<BaseBean<TabBeanResponse>> = _tabListLiveData

    //tab下的模块数据
    private val _tabModelLiveData = MutableLiveData<BaseBean<HomeModulesResponse>>()
    val tabModelLiveData: LiveData<BaseBean<HomeModulesResponse>> = _tabModelLiveData

    //tab下的模块数据
    private val _tabFeedStreamLiveData = MutableLiveData<Pair<Boolean,BaseBean<HomeFeedStreamResponse>>>()
    val tabFeedStreamLiveData: LiveData<Pair<Boolean,BaseBean<HomeFeedStreamResponse>>> = _tabFeedStreamLiveData

    var mFeedStreamPagePosition = DEFAULT_PAGE_POSITION
    val mFeedStreamPageSize = DEFAULT_PAGE_SIZE
    private val _refreshLoadLiveData = MutableLiveData<Boolean>()
    val refreshLoadLiveData: LiveData<Boolean> = _refreshLoadLiveData
    private val _canLoadLiveData = MutableLiveData(false)
    val canLoadLiveData: LiveData<Boolean> = _canLoadLiveData

    val showBigWheelImgLiveData = MutableLiveData(false)

    private var isGuaranteed = false //是否兜底 后端用的

    companion object{
        private const val DEFAULT_PAGE_POSITION = 1
        private const val DEFAULT_PAGE_SIZE = 18
    }
    /**
     * 获取资质状态
     */
    fun getHomeAptitudeStatus() {
        viewModelScope.launch {
            val homeAptitudeStatus = HomeAptitudeRequest().getHomeAptitudeStatus()
            _homeAptitudeStatusLiveData.postValue(homeAptitudeStatus)
        }
    }

    fun getSearchHotList(){
        viewModelScope.launch {
            val result = NewHomeRequest().requestSearchHotList()
            _searchHotListLiveData.postValue(result)
        }
    }

    fun getTabList(){
        viewModelScope.launch {
            val result = NewHomeRequest().requestTabList()
            _tabListLiveData.postValue(result)
        }
    }

    suspend fun getTabModel(tabId:String,tabType:String){
        showLoading()
        val result = NewHomeRequest().requestTabModel(tabId, tabType)
        dismissLoading()
        _tabModelLiveData.postValue(result)
    }


    var requestParam: Map<String, String>? = null

    suspend fun getFeedStream(tabId: String, tabType: String, isRefresh: Boolean, sptype: String?, sid: String?) {
        if (isRefresh) {
            mFeedStreamPagePosition = DEFAULT_PAGE_POSITION
            _canLoadLiveData.value = false
            isGuaranteed = false
        }
        val result = if (isRefresh || requestParam == null) {
            NewHomeRequest().requestHomeFeedStream(tabId, tabType, mFeedStreamPagePosition.toString(), mFeedStreamPageSize.toString(), isGuaranteed, sptype, sid)
        } else {
            NewHomeRequest().requestHomeFeedStream(requestParam?: mapOf())
        }
        requestParam = result.data?.requestParam
        _tabFeedStreamLiveData.postValue(Pair(isRefresh, result))
        _refreshLoadLiveData.postValue(isRefresh)
        if (result.isSuccess) {
            result.data?.let {
                    it.responseLocalTime = System.currentTimeMillis()
                isGuaranteed = it.isGuaranteed == true
            }
            if (result.data?.isEnd == false) {
                mFeedStreamPagePosition++
                _canLoadLiveData.value = true
            } else {
                _canLoadLiveData.value = false
            }
        }
    }

    fun setCacheTabData(bean: BaseBean<TabBeanResponse>) {
        _tabListLiveData.value = bean
    }

    fun setCacheCommonModuleData(bean: BaseBean<HomeModulesResponse>) {
        _tabModelLiveData.value = bean
    }

    fun setCacheFeedStreamData(bean: BaseBean<HomeFeedStreamResponse>) {
        _tabFeedStreamLiveData.value = Pair(true,bean)
    }
}