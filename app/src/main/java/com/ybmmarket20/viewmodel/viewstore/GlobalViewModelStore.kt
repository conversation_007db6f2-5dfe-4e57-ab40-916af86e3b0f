package com.ybmmarket20.viewmodel.viewstore

import androidx.lifecycle.ViewModelStore

/**
 * 全局的ViewStore
 */
class GlobalViewModelStore {

    private var mViewModelStore: ViewModelStore? = null

    companion object {
        private var instance: GlobalViewModelStore? = null
            get() {
                if (field == null) field = GlobalViewModelStore()
                return field
            }

        fun get() = instance!!
    }

    /**
     * 初始化设置ViewModelStore并且只能设置一次
     */
    fun registerViewModelStore(viewModelStore: ViewModelStore) {
        if (mViewModelStore == null) mViewModelStore = viewModelStore
    }

    fun getGlobalViewModelStore(): ViewModelStore {
        return mViewModelStore ?: EmptyViewModelStore()
    }

    fun clearGlobalViewModelStore() {
        mViewModelStore = EmptyViewModelStore()
    }

    /**
     * 空实现的ViewModelStore
     */
    inner class EmptyViewModelStore : ViewModelStore()
}