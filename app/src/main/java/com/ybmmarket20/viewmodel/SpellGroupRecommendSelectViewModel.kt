package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.SettleBean
import com.ybmmarket20.network.request.SettleRequest
import com.ybmmarket20.utils.SpUtil
import kotlinx.coroutines.launch

/**
 * 随心拼专区
 */
class SpellGroupRecommendSelectViewModel: ViewModel() {

    private val _spellGroupTranNoLiveData = MutableLiveData<BaseBean<SettleBean>>()
    val spellGroupTranNoLiveData: LiveData<BaseBean<SettleBean>> = _spellGroupTranNoLiveData

    fun getSpellGroupTranNo(skuId: String, productNum: String) {
        viewModelScope.launch {
            if (skuId.isEmpty() || productNum.isEmpty()) {
                _spellGroupTranNoLiveData.postValue(BaseBean.newFailureBaseBean(SettleBean()))
                return@launch
            }
            val preSettleSpellGroupTranNo = SettleRequest().preSettleSpellGroupTranNo(SpUtil.getMerchantid(), skuId, productNum)
            _spellGroupTranNoLiveData.postValue(preSettleSpellGroupTranNo)
        }
    }
}