package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CheckPayPasswordBean
import com.ybmmarket20.bean.JDPWBean
import com.ybmmarket20.network.request.JDPayRequest
import com.ybmmarketkotlin.utils.ChCrypto
import kotlinx.coroutines.launch

/**
 * 验证支付密码
 */
class CheckPayPasswordViewModel(app: Application): BaseViewModel(app) {

    private val _checkPayPasswordLiveData = MutableLiveData<BaseBean<CheckPayPasswordBean>>()
    val checkPayPasswordLiveData: LiveData<BaseBean<CheckPayPasswordBean>> = _checkPayPasswordLiveData

    private val _checkPayPasswordForPayLiveData = MutableLiveData<BaseBean<CheckPayPasswordBean>>()
    val checkPayPasswordForPayLiveData: LiveData<BaseBean<CheckPayPasswordBean>> = _checkPayPasswordForPayLiveData

    //是否设置支付密码
    private val _jDPWSettingLiveData = MutableLiveData<BaseBean<JDPWBean>>()
    val jDPWSettingLiveData: LiveData<BaseBean<JDPWBean>> = _jDPWSettingLiveData

    /**
     * 是否设置支付密码
     */
    fun queryPWSettingStatus() {
        viewModelScope.launch {
            val queryPWSettingStatus = JDPayRequest().queryPWSettingStatus()
            _jDPWSettingLiveData.postValue(queryPWSettingStatus)
        }
    }

    /**
     * 验证密码
     */
    fun checkPayPassword(pwd: String, scence: String, orderId: String, orderNo: String) {
        viewModelScope.launch {
            val checkPayPassword = JDPayRequest().checkPayPassword(ChCrypto.jdPayAesEncrypt(pwd), scence, orderId, orderNo, "")
            _checkPayPasswordLiveData.postValue(checkPayPassword)
        }
    }

    /**
     * 支付验证密码
     */
    fun checkPayPasswordForPay(pwd: String, scence: String, orderId: String, orderNo: String, tranNo: String) {
        viewModelScope.launch {
            val checkPayPassword = JDPayRequest().checkPayPassword(ChCrypto.jdPayAesEncrypt(pwd), scence, orderId, orderNo, tranNo)
            _checkPayPasswordForPayLiveData.postValue(checkPayPassword)
        }
    }
}