package com.ybmmarket20.viewmodel

import android.text.TextUtils
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.network.request.EmailRequest
import kotlinx.coroutines.launch

/**
 * 电子邮件
 */
class EmailViewModel: ViewModel() {

    private val _sendEmailLiveData = MutableLiveData<BaseBean<Any>>()
    val sendEmailLiveData: LiveData<BaseBean<Any>> = _sendEmailLiveData


    private val _sendEmailForContractLiveData = MutableLiveData<BaseBean<Any>>()
    val sendEmailForContractLiveData: LiveData<BaseBean<Any>> = _sendEmailForContractLiveData

    fun sendEmail(orderNo: String?, email: String?) {
        viewModelScope.launch {
            val result = if (TextUtils.isEmpty(orderNo) || TextUtils.isEmpty(email)) {
                BaseBean.newFailureBaseBean(Any())
            } else {
                EmailRequest().sendEmail(orderNo!!, email!!)
            }
            _sendEmailLiveData.postValue(result)
        }
    }

    fun sendPurchaseEmail(email: String, year: String, month: String) {
        viewModelScope.launch {
            val result = EmailRequest().sendPurchaseEmail(email, year, month)
            _sendEmailLiveData.postValue(result)
        }
    }

    /**
     * 发送购销合同到邮箱
     */
    fun sendEmailForContract(email: String, orderNo: String, fileType: String) {
        viewModelScope.launch {
            val sendEmailForContract = EmailRequest().sendEmailForContract(email, orderNo, fileType)
            _sendEmailForContractLiveData.postValue(sendEmailForContract)
        }
    }
}