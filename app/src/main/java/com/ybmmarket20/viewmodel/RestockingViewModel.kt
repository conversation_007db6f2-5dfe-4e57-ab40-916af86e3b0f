package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.EmptyBean
import com.ybmmarket20.bean.RestockingListResult
import com.ybmmarket20.bean.RestockingVoucherData
import com.ybmmarket20.bean.SearchResultOPBean
import com.ybmmarket20.network.request.RestockingRequest
import kotlinx.coroutines.launch

class RestockingViewModel : ViewModel() {

    private val _restockingListLiveData: MutableLiveData<RestockingListResult> =
        MutableLiveData()
    val restockingListLiveData: LiveData<RestockingListResult> = _restockingListLiveData
    private val _restockingVoucherListLiveData: MutableLiveData<BaseBean<RestockingVoucherData>> =
        MutableLiveData()
    val restockingVoucherListLiveData: LiveData<BaseBean<RestockingVoucherData>> = _restockingVoucherListLiveData
    private val _restockingVoucherReceiveLiveData: MutableLiveData<BaseBean<RestockingVoucherData>> =
        MutableLiveData()
    val restockingVoucherReceiveLiveData: LiveData<BaseBean<RestockingVoucherData>> = _restockingVoucherReceiveLiveData

    fun getRestockingListData(params: Map<String, String>, isRefresh: Boolean = true) {
        if (params.isEmpty()){
            _restockingListLiveData.postValue(RestockingListResult(null, isRefresh))
            return
        }
        viewModelScope.launch {
            val restockingData = RestockingRequest().getRestockingListData(params)
            _restockingListLiveData.postValue(RestockingListResult(restockingData, isRefresh))
        }
    }
    fun getOneClickRestockCoupon() {
        viewModelScope.launch {
            val restockingCouponData = RestockingRequest().getOneClickRestockCoupon()
            _restockingVoucherListLiveData.postValue(restockingCouponData)
        }
    }
    fun getOneClickRestockCouponReceive(couponTemplateIds:String) {
        viewModelScope.launch {
            val restockingCouponReceive = RestockingRequest().getOneClickRestockCouponReceive(couponTemplateIds)
            _restockingVoucherReceiveLiveData.postValue(restockingCouponReceive)
        }
    }
}