package com.ybmmarket20.viewmodel

import android.R.attr.path
import android.app.Application
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.media.MediaMetadataRetriever
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.animation.GlideAnimation
import com.bumptech.glide.request.target.SimpleTarget
import com.luck.picture.lib.tools.ScreenUtils
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybm.app.common.SmartExecutorManager
import com.ybmmarket20.R
import com.ybmmarket20.bean.ProductDetailBeanWrapper
import com.ybmmarket20.bean.getSingleStepPriceStr
import com.ybmmarket20.bean.isStep
import com.ybmmarket20.common.util.Abase.getResources
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.ZxingUtils
import com.ybmmarket20.utils.generateUnit
import com.ybmmarket20.view.GoodsDetailShareDialog
import kotlinx.coroutines.*
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine


/**
 * 商品详情分享数据
 */
class GoodsDetailShareViewModel(application: Application) : AndroidViewModel(application) {

    private val _shareDataLiveData = MutableLiveData<GoodsDetailShareDialog.GoodsDetailShareDialogResultBean>()
    val shareDataLiveData: LiveData<GoodsDetailShareDialog.GoodsDetailShareDialogResultBean> = _shareDataLiveData

    fun getGoodDetailShareData(detailBean: GoodsDetailShareDialog.GoodsDetailShareDialogRequestBean, qrCodeWidthAndHeight: Int = 100, isWholeSale: Boolean = false) {
        viewModelScope.launch(Dispatchers.IO) {
            val shopName = detailBean.shopName
            val imageBitmapDeferred = async(Dispatchers.Main) {
                var showImageUrl = ""
                val showImageList = detailBean.productDetail.rows.getImagesVideosList()
                var isVideo = true
                if (showImageList.isNotEmpty()) {
                    val imagesVideosListBean = showImageList[0]
                    showImageUrl = if (imagesVideosListBean.type == 1) {
                        isVideo = false
                        AppNetConfig.LORD_BIDIMAGE + imagesVideosListBean.imageUrl
                    } else {
                        isVideo = true
                        AppNetConfig.LORD_IMAGESVIDEOS + imagesVideosListBean.videoUrl
                    }
                }
                val bitmap = getBitmapByUrl(showImageUrl, isVideo = isVideo)
                bitmap
            }
            val tagBitmapDeferred = async(Dispatchers.Main) {
                getBitmapByUrl(AppNetConfig.LORD_TAG + detailBean.tagUrl, false)
            }
            val qrCodeBitmapDeferred = async {
                val qRCodeBitmap = ZxingUtils.createQRCodeBitmap(generateShareUrl(detailBean.shareUrl
                        ?: "", "2"), ScreenUtils.dip2px(getApplication(), 70f))
                qRCodeBitmap
            }
            val price = getPrice(detailBean.productDetail)

            val afterDiscountPriceDeferred = async {
                val list = NetworkService.instance.getPriceAfterDiscount(detailBean.id).data
                try {
                    val inHandPrice = list[0].inHandPrice
                    if (list.isNullOrEmpty()) null
                    else {
                        if(detailBean.rangeStepPrice.isStep()) {
                            if (inHandPrice < (detailBean.rangeStepPrice?.minSkuPrice?:"0.00").toDouble()){
                                list[0].price
                            }else{
                                null
                            }
                        }else{
                            if (inHandPrice < price.toDouble()){
                                list[0].price
                            }else{
                                null
                            }
                        }
                    }
                }catch (e:Exception){
                    return@async null
                }
            }
            val showPriceAfterDiscount = afterDiscountPriceDeferred.await()
            val spannable = if(detailBean.rangeStepPrice.isStep()) {
                priceFormat(detailBean.rangeStepPrice?.minSkuPrice?: "0.00", detailBean.productDetail.rows.productUnit, true)
            } else {
                priceFormat(price, detailBean.productDetail.rows.productUnit, detailBean.isStepPrice)
            }
            val dataBean = GoodsDetailShareDialog.GoodsDetailShareDialogResultBean(
                    shopName,
                    imageBitmapDeferred.await(),
                    qrCodeBitmapDeferred.await(),
                    detailBean.shareTitle ?: "",
                    spannable,
                    showPriceAfterDiscount,
                    detailBean.productDetail.getIsAssemble(),
                    tagBitmapDeferred.await(),
                    detailBean.isShowPrice,
                    detailBean.iscontrolType
            )
            _shareDataLiveData.postValue(dataBean)
        }
    }

    /**
     * 获取价格
     */
    private fun getPrice(detailBean: ProductDetailBeanWrapper): String {
        val price = if (detailBean.getIsAssemble()) {
            //拼团
            if (detailBean.actPt.isStepPrice()) {
                detailBean.actPt.minSkuPrice ?: ""
            } else {
                try {
                    detailBean.rows.actPtBean.assemblePrice
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        } else if (detailBean.rows.productType == 2) {
            //秒杀
            detailBean.rows.fob
        } else if (detailBean.actPgby != null) {
            detailBean.actPgby.assemblePrice
        } else {
            //平销品
            detailBean.rows.fob
        }
        return price.toString()
    }

    private fun priceFormat(price: String, unit: String, isStepPrice: Boolean = false): SpannableStringBuilder {
        var tempPrice = price
        tempPrice = UiUtils.transform(tempPrice)
        val symbol = createSpannable("¥", 11)
        val price1 = createSpannable("${tempPrice.split(".")[0]}.", 23)
        val price2 = createSpannable(tempPrice.split(".")[1], 14)
        val goodsUnit = createSpannable(generateUnit(unit, isStepPrice), 13, "#ffffff")
        return symbol.append(price1).append(price2).append(goodsUnit)
    }

    private fun priceFormatNoTransform(price: String, unit: String, isStepPrice: Boolean = false): SpannableStringBuilder {
        var tempPrice = price
        val symbol = createSpannable("¥", 11)
        val price1 = createSpannable("${tempPrice.split(".")[0]}.", 23)
        val price2 = createSpannable(tempPrice.split(".")[1], 14)
        val goodsUnit = createSpannable(generateUnit(unit, isStepPrice), 13, "#ffffff")
        return symbol.append(price1).append(price2).append(goodsUnit)
    }

    private fun createSpannable(content: String, size: Int, color: String = "#F6FC50"): SpannableStringBuilder {
        val spannable = SpannableStringBuilder(content)
        spannable.setSpan(
                ForegroundColorSpan(Color.parseColor(color)),
                0,
                content.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spannable.setSpan(AbsoluteSizeSpan(size, true), 0, content.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        return spannable
    }

    private suspend fun getBitmapByUrl(url: String, isPlace: Boolean = true, isVideo: Boolean = false) = suspendCancellableCoroutine<Bitmap?> { con ->
        if (isVideo) {
            SmartExecutorManager.getInstance().execute(Runnable {
                con.resume(UiUtils.getVideoThumbnail(url))
            })
        } else {
            ImageHelper.with(getApplication())
                    .load(url)
                    .asBitmap()
                    .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                    .into(object : SimpleTarget<Bitmap>() {
                        override fun onLoadFailed(e: Exception?, errorDrawable: Drawable?) {
                            if (isPlace) {
                                BitmapFactory.decodeResource(getResources(), R.drawable.logo)
                            } else {
                                null
                            }.let(con::resume)
                        }

                        override fun onResourceReady(
                                resource: Bitmap?,
                                glideAnimation: GlideAnimation<in Bitmap>?
                        ) {
                            if (resource == null) {
                                val bitmap = BitmapFactory.decodeResource(getResources(), R.drawable.logo)
                                con.resume(bitmap)
                            } else {
                                try {
                                    con.resume(resource)
                                } catch (e: Exception) {
                                    e.printStackTrace()
                                }
                            }
                        }
                    })
        }
    }
}

/**
 * 为url添加参数
 * @param type
 */
fun generateShareUrl(url: String, type: String): String {
    if (TextUtils.isEmpty(url)) return ""
    val builder = StringBuilder(url)
    if (url.contains("?")) {
        builder.append("&merchantId=")
                .append(SpUtil.getMerchantid())
                .append("&type=")
                .append(type)
    } else {
        builder.append("?merchantId=")
                .append(SpUtil.getMerchantid())
                .append("&type=")
                .append(type)
    }
    return builder.toString()
}