package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SearchResultBean
import com.ybmmarket20.network.request.SearchRequest
import kotlinx.coroutines.launch

/**
 * 搜索随心拼商品
 */
class SearchSpellGroupRecommendGoodsViewModel: ViewModel() {

    private val _searchSpellGroupRecommendGoodsLiveData = MutableLiveData<BaseBean<SearchResultBean>>()
    val searchSpellGroupRecommendGoodsLiveData: LiveData<BaseBean<SearchResultBean>> = _searchSpellGroupRecommendGoodsLiveData

    /**
     * 随心拼商品搜索
     */
    fun searchSpellGroupRecommendGoods(params: Map<String, String>) {
        viewModelScope.launch {
            val searchSpellGroupRecommendGoods = SearchRequest().searchSpellGroupRecommendGoods(params)
            _searchSpellGroupRecommendGoodsLiveData.postValue(searchSpellGroupRecommendGoods)
        }
    }
}