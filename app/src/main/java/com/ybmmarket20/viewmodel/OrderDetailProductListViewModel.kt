package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.OrderDetailProductListData
import com.ybmmarket20.network.request.OrderDetailProductRequest
import kotlinx.coroutines.launch

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2024/10/28 14:39
 *    desc   :
 */
class OrderDetailProductListViewModel(val app: Application) : AndroidViewModel(app) {
    private val _onOrderProductLiveData = MutableLiveData<BaseBean<OrderDetailProductListData>>()
    val onOrderProductLiveData: LiveData<BaseBean<OrderDetailProductListData>> =
        _onOrderProductLiveData

    /**
     * 根据订单号查询订单商品明细
     */
    fun getOrderProductList(orderNo: String) {
        viewModelScope.launch {
            val orderProduct = OrderDetailProductRequest().getOrderProductList(orderNo)
            _onOrderProductLiveData.postValue(orderProduct)
        }
    }
}