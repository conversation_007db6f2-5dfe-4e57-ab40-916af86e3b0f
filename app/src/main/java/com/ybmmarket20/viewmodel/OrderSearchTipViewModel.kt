package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.adapter.SEARCH_ORDER_TIP_HEADER
import com.ybmmarket20.adapter.SEARCH_ORDER_TIP_ITEM
import com.ybmmarket20.adapter.SEARCH_ORDER_TIP_TITLE
import com.ybmmarket20.bean.*
import com.ybmmarket20.network.request.OrderSearchTipRequest
import kotlinx.coroutines.launch

/**
 * 订单搜索提示
 */
class OrderSearchTipViewModel: ViewModel() {

    private val _orderSearchTipLiveData = MutableLiveData<BaseBean<MutableList<OrderSearchTipEntry>>>()
    val orderSearchTipLiveData: LiveData<BaseBean<MutableList<OrderSearchTipEntry>>> = _orderSearchTipLiveData

    fun getOrderSearchTip() {
        viewModelScope.launch {
            val requestResult = OrderSearchTipRequest().getSearchTipData()
            if (requestResult.isSuccess) {
                val result = setOrderSearchTipData(requestResult.data)
                BaseBean.newSuccessBaseBean(result)
            } else {
                BaseBean.newFailureBaseBean(mutableListOf())
            }.let(_orderSearchTipLiveData::postValue)
        }
    }

    /**
     * 设置订单搜索提示数据
     * @param orderSearchTipData
     */
    private fun setOrderSearchTipData(orderSearchTipData: OrderSearchTipBean): MutableList<OrderSearchTipEntry> {
        //添加头部数据
        val entries = mutableListOf<OrderSearchTipEntry>()
        val headerListOrg = orderSearchTipData.itemList
        val headerList = headerListOrg?.map {
            OrderSearchTipItem(it.itemName, it.field, it.param, "")
        }
        if (headerList != null) {
            val headerEntry = OrderSearchTipEntry(SEARCH_ORDER_TIP_HEADER, headerList, null)
            entries.add(headerEntry)
        }
        //添加最近采购商家店铺
        val itemListOrg = orderSearchTipData.aggregationItemList
        itemListOrg?.forEach {
            entries.add(OrderSearchTipEntry(SEARCH_ORDER_TIP_TITLE, null, OrderSearchTipItem(it.title, "", "", it.tips)))
            it.items?.map { orderSearchTipData ->
                OrderSearchTipEntry(SEARCH_ORDER_TIP_ITEM, null, OrderSearchTipItem(orderSearchTipData.itemName, orderSearchTipData.field, orderSearchTipData.param, ""))
            }?.let(entries::addAll)
        }
        return entries
    }
}