package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData

open class BaseViewModel(appLike: Application): AndroidViewModel(appLike) {

    private val _loadingLiveData = MutableLiveData<Boolean>()
    val loadingLiveData: LiveData<Boolean> = _loadingLiveData

    fun showLoading() {
        _loadingLiveData.postValue(true)
    }

    fun dismissLoading() {
        _loadingLiveData.postValue(false)
    }
}