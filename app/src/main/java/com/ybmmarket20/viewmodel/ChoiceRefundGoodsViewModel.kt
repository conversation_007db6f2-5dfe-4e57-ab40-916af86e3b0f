package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.QueryGiftRefundNum
import com.ybmmarket20.bean.QueryGiftRefundNumResult
import com.ybmmarket20.bean.RefundProductListBean
import com.ybmmarket20.network.request.ChoiceRefundGoodsRequest
import kotlinx.coroutines.launch

/**
 * 选择退款商品
 */
class ChoiceRefundGoodsViewModel: ViewModel() {

    private val _queryGiftRefundNumLiveData: MutableLiveData<BaseBean<QueryGiftRefundNumResult>> = MutableLiveData()
    val queryGiftRefundNumLiveData: LiveData<BaseBean<QueryGiftRefundNumResult>> = _queryGiftRefundNumLiveData

    fun queryGiftRefundNum(giftRefundDetailDtos: String, orderNo: String) {
        viewModelScope.launch {
            val result = ChoiceRefundGoodsRequest().queryGiftRefundNum(giftRefundDetailDtos, orderNo)
            val refundGiftResult = if (result.isSuccess) {
                BaseBean.newSuccessBaseBean(QueryGiftRefundNumResult(
                    result.data
                ))
            } else {
                BaseBean.newFailureBaseBean(QueryGiftRefundNumResult(emptyList()))
            }
            _queryGiftRefundNumLiveData.postValue(refundGiftResult)
        }
    }

}