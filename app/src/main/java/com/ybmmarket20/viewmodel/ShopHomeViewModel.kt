package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.ImPackUrlBean
import com.ybmmarket20.bean.ShopBasicInfoBean
import com.ybmmarket20.bean.ShopTab
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.utils.SpUtil
import com.ybmmarketkotlin.bean.ShopBaseInfo
import kotlinx.coroutines.launch

class ShopHomeViewModel : ViewModel() {

    val shopInfoPopLiveData = MutableLiveData<ShopBasicInfoBean>()
    val shopInfoSelfLiveData = MutableLiveData<ShopBaseInfo>()
    val onLineMessageLiveData = MutableLiveData<ImPackUrlBean>()

    private val _shopTabsLiveData = MutableLiveData<BaseBean<List<ShopTab>>>()
    val shopTabsLiveData: LiveData<BaseBean<List<ShopTab>>> = _shopTabsLiveData

    fun getSelfShopInfo(merchantId: String, shopCode: String): Unit {
        viewModelScope.launch {
            val baseBean = NetworkService.instance.getSelfShopInfo(merchantId, shopCode)
            sendOnLineService("1")
            baseBean?.data?.let {
                shopInfoSelfLiveData.postValue(baseBean.data)
            }

        }
    }

    fun getShopInfoPop(
        merchantId: String,
        orgId: String,
        selectDeliveryAddress: Boolean = false
    ): Unit {
        viewModelScope.launch {
            val popBasicBean = NetworkService.instance.getPopCompanyDetail(
                merchantId,
                orgId,
                selectDeliveryAddress
            )
            sendOnLineService("1")
            popBasicBean?.data?.let {
                shopInfoPopLiveData.postValue(popBasicBean.data)
            }

        }
    }

    fun sendOnLineService(isThirdCompany: String) {
        viewModelScope.launch {
            val imUrlBean = NetworkService.instance.sendOnLineService(isThirdCompany)
            imUrlBean?.data?.let {
                onLineMessageLiveData.postValue(imUrlBean.data)
            }
        }
    }

    fun getShopTabs() {
        viewModelScope.launch {
            val shopTabs = NetworkService.instance.getShopTabs(SpUtil.getMerchantid())
            _shopTabsLiveData.postValue(shopTabs)
        }
    }

}