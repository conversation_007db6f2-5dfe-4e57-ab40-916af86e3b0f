package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.home.newpage.bean.TabBeanResponse
import kotlinx.coroutines.launch

/**
 * @class   GiftRefundSelectBottomDialogVM
 * <AUTHOR>
 * @date  2024/9/11
 * @description
 */
class GiftRefundSelectBottomDialogVM:ViewModel(){

    //tab列表
    // FIXME: 李江   TabBeanResponse待改
    private val _tabListLiveData = MutableLiveData<BaseBean<TabBeanResponse>>()
    val tabListLiveData: LiveData<BaseBean<TabBeanResponse>> = _tabListLiveData

    fun requestGiftData(){
        viewModelScope.launch {


        }
    }
}