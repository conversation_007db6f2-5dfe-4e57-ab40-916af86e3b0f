package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CartVoucher
import com.ybmmarket20.bean.CouponInfoBean
import com.ybmmarket20.bean.CouponRelatedGoodsPriceBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SearchFilterBean
import com.ybmmarket20.bean.SearchResultBean
import com.ybmmarket20.bean.SearchResultOPBean
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.network.request.CouponRelatedGoodsRequest
import kotlinx.coroutines.launch

/**
 * 凑单页
 */
class CouponRelatedGoodsViewModel(app: Application): BaseViewModel(app) {

    //当前获取商品列表的请求参数
    private var currGoodsListRequestParams = mutableMapOf<String, String>()

    //凑单页价格筛选列表
    private val _priceFilterListLiveData = MutableLiveData<BaseBean<List<CouponRelatedGoodsPriceBean>>>()
    val priceFilterListLiveData: LiveData<BaseBean<List<CouponRelatedGoodsPriceBean>>> = _priceFilterListLiveData

    //优惠券信息
    private val _couponInfoLiveData = MutableLiveData<BaseBean<CouponInfoBean>>()
    val couponInfoLiveData: LiveData<BaseBean<CouponInfoBean>> = _couponInfoLiveData

    //店铺列表
    private val _couponShopListLiveData = MutableLiveData<BaseBean<List<SearchFilterBean>>>()
    val couponShopListLiveData: LiveData<BaseBean<List<SearchFilterBean>>> = _couponShopListLiveData

    //商品列表-追加参数
    private val _couponRelatedGoodsListLiveData = MutableLiveData<BaseBean<SearchResultOPBean>>()
    val couponRelatedGoodsListLiveData: LiveData<BaseBean<SearchResultOPBean>> = _couponRelatedGoodsListLiveData

    //商品列表-加载更多
    private val _loadMoreGoodsListLiveData = MutableLiveData<BaseBean<SearchResultOPBean>>()
    val loadMoreGoodsListLiveData: LiveData<BaseBean<SearchResultOPBean>> = _loadMoreGoodsListLiveData

    //凑单信息
    private val _cartVoucherBeanLiveData = MutableLiveData<BaseBean<CartVoucher>>()
    val cartVoucherBeanLiveData: LiveData<BaseBean<CartVoucher>> = _cartVoucherBeanLiveData


    /**
     * 获取价格筛选项列表
     */
    fun getPriceFilterList(params: Map<String, String>) {
        viewModelScope.launch {
            val priceFilter = CouponRelatedGoodsRequest().getPriceFilterList(params)
            if (priceFilter.isSuccess && !priceFilter.data.isNullOrEmpty()) {
                priceFilter.data[0].isSelected = true
            }
            _priceFilterListLiveData.postValue(priceFilter)
        }
    }

    /**
     * 获取优惠券信息
     */
    fun getCouponInfo(params: Map<String, String>) {
        viewModelScope.launch {
            val couponInfo = CouponRelatedGoodsRequest().getCouponInfo(params)
            if (couponInfo.isSuccess && !couponInfo.data.isNullOrEmpty()) {
                _couponInfoLiveData.postValue(BaseBean.newSuccessBaseBean(couponInfo.data[0]))
            } else {
                _couponInfoLiveData.postValue(BaseBean.newFailureBaseBean(null))
            }
        }
    }

    /**
     * 获取店铺列表
     */
    fun getShopList(params: Map<String, String>) {
        viewModelScope.launch {
            val shopList = CouponRelatedGoodsRequest().getShopList(params)

            _couponShopListLiveData.postValue(shopList)
        }
    }

    /**
     * 获取凑单页商品
     */
    fun getGoodsListByAppendParam(params: Map<String, String>) {
        //追加参数
        params.forEach {
            currGoodsListRequestParams[it.key] = it.value
        }
        currGoodsListRequestParams = currGoodsListRequestParams.filter {
            //除key=shopCodes外,value=""不传参
            it.value.isNotEmpty() || it.key == "shopCodes"
        }.toMutableMap()
        //重新请求需要从第一页开始(默认不传)
        if (currGoodsListRequestParams.containsKey("pageNum")) currGoodsListRequestParams.remove("pageNum")
        if (currGoodsListRequestParams.containsKey("pageSize")) currGoodsListRequestParams.remove("pageSize")
        //商家列表包含默认选择的商家，未选择商家则传isFirstRequest=true
        if (!currGoodsListRequestParams.containsKey("shopCodes")) {
            currGoodsListRequestParams["isFirstRequest"] = "true"
        } else if(currGoodsListRequestParams.containsKey("isFirstRequest")) {
            currGoodsListRequestParams.remove("isFirstRequest")
        }
        if (currGoodsListRequestParams["shopCodes"] == "") {
            currGoodsListRequestParams.remove("shopCodes")
        }
        getGoodsList(currGoodsListRequestParams, _couponRelatedGoodsListLiveData)
    }

    /**
     * 获取商品列表-加载更多
     */
    fun getGoodsListByLoadMore() {
        getGoodsList(currGoodsListRequestParams, _loadMoreGoodsListLiveData)
    }

    /**
     * 获取商品列表
     */
    private fun getGoodsList(params: MutableMap<String, String>, liveData: MutableLiveData<BaseBean<SearchResultOPBean>>) {
        viewModelScope.launch {
            val goodsList = CouponRelatedGoodsRequest().getCouponRelatedGoodsList(params)
            if (goodsList.isSuccess && goodsList.data.requestParams != null) {
                currGoodsListRequestParams = goodsList.data.requestMap
            }
            liveData.postValue(goodsList)
        }
    }

    /**
     * 获取凑单信息
     */
    fun getCartVoucherBean(merchantId: String, templateIds: String) {
        viewModelScope.launch {
            val bean = NetworkService.instance.getCartVoucherBean(merchantId, templateIds)
            _cartVoucherBeanLiveData.postValue(bean)
        }
    }

}