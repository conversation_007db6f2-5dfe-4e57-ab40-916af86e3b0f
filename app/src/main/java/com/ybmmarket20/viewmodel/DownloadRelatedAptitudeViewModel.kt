package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.DownloadRecordBean
import com.ybmmarket20.bean.RelatedAptitudeTag
import com.ybmmarket20.network.request.DownloadRelatedAptitudeRequest
import com.ybmmarket20.utils.SpUtil
import kotlinx.coroutines.launch

/*
 * 下载相关资质
 */
class DownloadRelatedAptitudeViewModel(app: Application): BaseViewModel(app) {

    private val _tagListLiveData = MutableLiveData<BaseBean<List<RelatedAptitudeTag>>>()
    val tagListLiveData: LiveData<BaseBean<List<RelatedAptitudeTag>>> = _tagListLiveData
    private val _submitResultLiveData = MutableLiveData<BaseBean<Any>>()
    val submitResultLiveData: LiveData<BaseBean<Any>> = _submitResultLiveData
    private val _recordLiveData = MutableLiveData<BaseBean<List<DownloadRecordBean>>>()
    val recordLiveData: LiveData<BaseBean<List<DownloadRecordBean>>> = _recordLiveData

    /**
     * 获取相关资质列表
     */
    fun getRelatedAptitudeList(orgId: String?, orderNo: String?) {
        viewModelScope.launch {
            showLoading()
            val tagList = DownloadRelatedAptitudeRequest().getRelatedAptitudeList(orderNo?: "", orgId?: "", SpUtil.getMerchantid())
            _tagListLiveData.postValue(tagList)
            dismissLoading()
        }
    }

    /**
     * 提交下载资质
     */
    fun submitDownloadAptitude(params: Map<String, String>) {
        viewModelScope.launch {
            showLoading()
            val submitResult = DownloadRelatedAptitudeRequest().submitDownloadAptitude(params)
            _submitResultLiveData.postValue(submitResult)
            dismissLoading()
        }
    }

    /**
     * 获取下载记录
     */
    fun getAptitudeDownloadRecordList(orderNo: String?) {
        viewModelScope.launch {
            showLoading()
            val recordList = DownloadRelatedAptitudeRequest().getAptitudeDownloadRecordList(orderNo?: "")
            _recordLiveData.postValue(recordList)
            dismissLoading()
        }
    }

}