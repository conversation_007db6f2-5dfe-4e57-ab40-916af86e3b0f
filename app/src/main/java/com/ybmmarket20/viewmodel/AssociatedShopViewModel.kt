package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.AssociatedShopBean
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.network.request.AssociateShopRequest
import com.ybmmarket20.utils.SpUtil
import kotlinx.coroutines.launch

/**
 * 关联店铺
 */
class AssociatedShopViewModel: ViewModel() {

    //关联店铺
    private val _associatedShopLiveData = MutableLiveData<BaseBean<AssociatedShopBean>>()
    val associatedShopLiveData: LiveData<BaseBean<AssociatedShopBean>> = _associatedShopLiveData

    fun associatedShop(poiId: String) {
        viewModelScope.launch {
            val associatedShop = AssociateShopRequest().associatedShop(poiId, SpUtil.getAccountId())
            val jumpUrl = if (associatedShop.isSuccess) {
                when (associatedShop.data.role) {
                    //店长
                    1 -> {
                        "ybmpage://aptitudebasicinfo?licenseStatus=1&from=1&merchantId=${associatedShop.data.merchantId}"
                    }
                    //店员
                    2 ->  "ybmpage://clerkaptitudeauthenticationactivity?status=0&merchant_id=${associatedShop.data.merchantId}"
                    else -> null
                }
            } else {
                null
            }
            associatedShop.data?.jumpUrl = jumpUrl
            _associatedShopLiveData.postValue(associatedShop)
        }
    }
}