package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.GiftSelectNormalResponse
import com.ybmmarket20.network.request.GiftSelectRequest
import kotlinx.coroutines.launch

/**
 * @class   GiftSelctBottomDialogVM
 * <AUTHOR>
 * @date  2024/9/11
 * @description
 */
class GiftSelectBottomDialogVM(appLike: Application):BaseViewModel(appLike){

    private val _giftData: MutableLiveData<BaseBean<GiftSelectNormalResponse>> = MutableLiveData()
    val giftData: LiveData<BaseBean<GiftSelectNormalResponse>> = _giftData

    private val _giftPoolSelect: MutableLiveData<Pair<BaseBean<Any>,Int>> = MutableLiveData()
    val giftPoolSelect: LiveData<Pair<BaseBean<Any>,Int>> = _giftPoolSelect

    private val _giftPoolNoSelect: MutableLiveData<Pair<BaseBean<Any>,Int>> = MutableLiveData()
    val giftPoolNoSelect: LiveData<Pair<BaseBean<Any>,Int>> = _giftPoolNoSelect


    private val _giftGiveUpSelect: MutableLiveData<Pair<Boolean,BaseBean<Any>>> = MutableLiveData()
    val giftGiveUpSelect: LiveData<Pair<Boolean,BaseBean<Any>>> = _giftGiveUpSelect

    private val _giftChangeAmount: MutableLiveData<Pair<Int,Int>> = MutableLiveData()
    val giftChangeAmount: LiveData<Pair<Int,Int>> = _giftChangeAmount


    /**
     * 获取赠品信息
     * @param promoId String?
     * @param bizSource Int
     */
    fun requestGiftData(promoId:String?,bizSource:Int){

        viewModelScope.launch {

            showLoading()
            val result = GiftSelectRequest().requestGiftData(hashMapOf(
                    Pair("promoId",promoId?:""),
                    Pair("bizSource", bizSource),
            ))

         _giftData.postValue(result)
            dismissLoading()
        }
    }

    suspend fun selectItemGiftPool(params: HashMap<String, Any>, position: Int){
        showLoading()
        val result = GiftSelectRequest().selectItemGiftPool(params)
        _giftPoolSelect.postValue(Pair(result,position))
        dismissLoading()
    }

    suspend fun cancelItemGiftPool(params: HashMap<String, Any>, position: Int){
        showLoading()
        val result = GiftSelectRequest().cancelItemGiftPool(params)

        _giftPoolNoSelect.postValue(Pair(result,position))
        dismissLoading()
    }

    suspend fun addAutoGiveUpActFlag(params:HashMap<String,Any>){
        showLoading()
        _giftGiveUpSelect.postValue(Pair(true,GiftSelectRequest().addAutoGiveUpActFlag(params)))
        dismissLoading()
    }
    suspend fun deleteAutoGiveUpActFlag(params:HashMap<String,Any>){
        showLoading()
        _giftGiveUpSelect.postValue(Pair(false,GiftSelectRequest().deleteAutoGiveUpActFlag(params)))
        dismissLoading()
    }

    suspend fun changeGiftPool(params: HashMap<String, Any>, position: Int, amount: Int){
        showLoading()
        val result = GiftSelectRequest().changeGiftPool(params)
        if (result.isSuccess){
            _giftChangeAmount.postValue(Pair(position,amount))
        }
        dismissLoading()
    }

}