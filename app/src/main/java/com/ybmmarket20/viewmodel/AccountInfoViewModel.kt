package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ybm.app.utils.BugUtil
import com.ybmmarket20.bean.AccountBean
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.LoginInfo
import com.ybmmarket20.bean.LoginMerchantInfo
import com.ybmmarket20.bean.LoginWithMerchantBean
import com.ybmmarket20.bean.MerchantInfo
import com.ybmmarket20.bean.PrivacyAgreementsBean
import com.ybmmarket20.bean.ReceiveMoneyAccountInfo
import com.ybmmarket20.bean.RegisterStatusBean
import com.ybmmarket20.bean.WechatOauthInfo
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.common.util.Abase
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.ConstantData
import com.ybmmarket20.constant.SP_KEY_LOGIN_IS_KA
import com.ybmmarket20.constant.SP_KEY_LOGIN_IS_SAAS
import com.ybmmarket20.db.AccountTable
import com.ybmmarket20.db.info.HandlerGoodsDao
import com.ybmmarket20.network.request.AccountInfoRequest
import com.ybmmarket20.network.request.RealNameAuthenticationRequest
import com.ybmmarket20.utils.AccountStatus
import com.ybmmarket20.utils.AuditStatusSyncUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.YbmPushUtil
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.xyyreport.XyyReportManager
import com.ybmmarket20.xyyreport.session.SessionManager
import com.ybmmarketkotlin.utils.ChCrypto
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import ly.count.android.sdk.Countly
import ly.count.android.sdk.XyyApmCly

class AccountInfoViewModel(app: Application) : BaseViewModel(app) {

    val _liveData = MutableLiveData<ReceiveMoneyAccountInfo>()
    val liveData: LiveData<ReceiveMoneyAccountInfo> = _liveData

    //登陆
    private val _loginLiveData = MutableLiveData<BaseBean<LoginInfo>>()
    val loginLiveData: LiveData<BaseBean<LoginInfo>> = _loginLiveData
    //授权：微信相关token
    private val _oauthLiveData = MutableLiveData<BaseBean<WechatOauthInfo>>()
    val oauthLiveData: LiveData<BaseBean<WechatOauthInfo>> = _oauthLiveData
    // 账号绑定微信状态查询
    private val _wechatBindStatus = MutableLiveData<Int>()
    val wechatBindStatus: LiveData<Int> = _wechatBindStatus
    // 账号手机号码查询
    private val _phoneMobileLiveData = MutableLiveData<String>()
    val phoneMobileLiveData: LiveData<String> = _phoneMobileLiveData

    //登陆带店铺信息
    private val _loginWithMerchantLiveData = MutableLiveData<BaseBean<LoginWithMerchantBean>>()
    val loginWithMerchantLiveData: LiveData<BaseBean<LoginWithMerchantBean>> =
        _loginWithMerchantLiveData

    //获取店铺信息
    private val _getMerchantInfoLiveData = MutableLiveData<BaseBean<LoginMerchantInfo>>()
    val getMerchantInfoLiveData: LiveData<BaseBean<LoginMerchantInfo>> = _getMerchantInfoLiveData

    //AccountId
//    private val _getAccountIdLiveData = MutableLiveData<BaseBean<AccountIdBean>>()
//    val getAccountIdLiveData: LiveData<BaseBean<AccountIdBean>> = _getAccountIdLiveData

//    private val _loginAgreementLiveData = MutableLiveData<BaseBean<LoginAgreementBean>>()
//    val loginAgreementLiveData: LiveData<BaseBean<LoginAgreementBean>> = _loginAgreementLiveData

    var pageNo: Int = 1
    val agreementsList: ArrayList<PrivacyAgreementsBean> by lazy { ArrayList<PrivacyAgreementsBean>() }

    /**
     * 获取收款账户信息
     */
    fun getReceiveMoneyAccountInfo(orderNo: String?) {
        viewModelScope.launch(Dispatchers.IO) {
            val receiveMoneyAccountInfo =
                AccountInfoRequest().getReceiveMoneyAccountInfo(orderNo ?: "")
            _liveData.postValue(receiveMoneyAccountInfo?.data)
        }
    }

    /**
     * 获取登录和店铺信息
     */
    fun login(
        mobileNumber: String,
        password: String,
        isGetMerchantInfo: Boolean = false,
        merchantId: String = ""
    ) {
        viewModelScope.launch {
            val loginResult = loginRequest(mobileNumber, password)
            if (!loginResult.isSuccess || 1 == loginResult.data?.needUpdatePassword) {
                _loginLiveData.postValue(loginResult)
                return@launch
            }
            if (isGetMerchantInfo) {
                val merchantInfoResult = getMerchantInfo(merchantId, mobileNumber)
                _loginLiveData.postValue(if (merchantInfoResult.isSuccess) {
                    loginResult.apply { data.isCrawler = merchantInfoResult.data.isCrawler }
                } else BaseBean.newFailureBaseBean(null))
                return@launch
            } else {
                if (LoginSucceedState.getLoginState(loginResult.data) is LoginSucceedState.SingleMerchantAndAuditedState) {
                    val merchantInfoResult =
                        getMerchantInfo(loginResult.data.merchantId ?: "", mobileNumber)
                    _loginLiveData.postValue(if (merchantInfoResult.isSuccess) {
                        loginResult.apply { data.isCrawler = merchantInfoResult.data.isCrawler }
                    } else BaseBean.newFailureBaseBean(null))
                    return@launch
                }
            }
            _loginLiveData.postValue(loginResult)
        }
    }

    /**
     * 跳转微信绑定页面
     */
    fun goBindWechat(tmpToken:String){
        viewModelScope.launch {
            val oauthInfo = wechatOauth(tmpToken)
            if(!oauthInfo.isSuccess){
                _oauthLiveData.postValue(oauthInfo)
                return@launch
            }
            val wechatBindAccountStatus = wechatBindAccountStatus(oauthInfo.data.access_token?:"",oauthInfo.data.openid?:"")
            if(wechatBindAccountStatus.isSuccess){
                if(wechatBindAccountStatus.data.status == 0){
                    _oauthLiveData.postValue(oauthInfo)
                    getPhoneMobile()
                    return@launch
                }else{
                    ToastUtils.showLong(wechatBindAccountStatus.data.msg)
                }
            }else{
                BaseBean.newFailureBaseBean(null)
            }
        }
    }

    /**
     * 获取登录和店铺信息
     */
    fun loginByWechat(
        tmpToken: String,
    ) {
        viewModelScope.launch {
            val oauthInfo = wechatOauth(tmpToken)
            if(!oauthInfo.isSuccess){
                _oauthLiveData.postValue(oauthInfo)
                return@launch
            }
            val loginResult = loginByWechatRequest(oauthInfo.data)
            if (!loginResult.isSuccess) {
                _loginLiveData.postValue(loginResult)
                return@launch
            }
            // 未绑定账号，需要绑定
            if(0 == loginResult.data.state){
                _oauthLiveData.postValue(oauthInfo)
                return@launch
            }else{
                if (LoginSucceedState.getLoginState(loginResult.data) is LoginSucceedState.SingleMerchantAndAuditedState) {
                    val merchantInfoResult =
                        getMerchantInfo(loginResult.data.merchantId ?: "", ChCrypto.aesDecrypt(loginResult.data.account))
                    _loginLiveData.postValue(if (merchantInfoResult.isSuccess) {
                        loginResult.apply { data.isCrawler = merchantInfoResult.data.isCrawler }
                    } else BaseBean.newFailureBaseBean(null))
                    return@launch
                }
            }
            _loginLiveData.postValue(loginResult)
        }
    }

    fun loginByWechat(
        accessToken: String?,
        openId:String?,
        unionid:String?
    ) {
        viewModelScope.launch {
            val loginResult = loginByWechatRequest(WechatOauthInfo(openid = openId, access_token = accessToken, unionid = unionid))
            if (!loginResult.isSuccess) {
                _loginLiveData.postValue(loginResult)
                return@launch
            }
            if (LoginSucceedState.getLoginState(loginResult.data) is LoginSucceedState.SingleMerchantAndAuditedState) {
                val merchantInfoResult =
                    getMerchantInfo(loginResult.data.merchantId ?: "", ChCrypto.aesDecrypt(loginResult.data.account))
                _loginLiveData.postValue(if (merchantInfoResult.isSuccess) {
                    loginResult.apply { data.isCrawler = merchantInfoResult.data.isCrawler }
                } else BaseBean.newFailureBaseBean(null))
                return@launch
            }
            _loginLiveData.postValue(loginResult)
        }
    }

    /**
     * 通过merchantId获取店铺信息
     */
    fun getMerchantInfoWithMerchantId(merchantId: String, phone: String) {
        viewModelScope.launch {
            val merchantInfo = getMerchantInfo(merchantId, phone)
            _getMerchantInfoLiveData.postValue(merchantInfo)
        }
    }

    /**
     * 通过merchantId获取店铺信息
     */
    fun getMerchantInfoWithMerchantIdUnSave(merchantId: String, phone: String, jumpUrl: String) {
        viewModelScope.launch {
            val merchantInfo = getMerchantInfoUnSave(merchantId, phone, jumpUrl)
            _getMerchantInfoLiveData.postValue(merchantInfo)
        }
    }

    /**
     * 登陆
     */
    private suspend fun loginRequest(mobileNumber: String, password: String): BaseBean<LoginInfo> {
        val loginInfo = AccountInfoRequest().login(ChCrypto.aesEncrypt(mobileNumber), password)
        if (loginInfo.isSuccess && 1 != loginInfo.data?.needUpdatePassword) {
            saveLoginInfo(loginInfo.data, mobileNumber, password,LoginInfo.LOGIN_TYPE_DEFAULT)
        }
        return loginInfo
    }

    /**
     * 账号绑定微信状态查询
     */
    fun accountBindWechatStatus() {
        viewModelScope.launch {
            val wechatBindStatus = AccountInfoRequest().accountBindWechatStatus()
            _wechatBindStatus.postValue(wechatBindStatus.data?.status?:0)
        }
    }

    /**
     * 微信绑定账号状态查询
     */
    private suspend fun wechatBindAccountStatus(accessToken:String,openid:String):BaseBean<RegisterStatusBean> {
        val wechatBindStatus = AccountInfoRequest().wechatBindAccountStatus(accessToken,openid)
        return wechatBindStatus
    }

    /**
     * 账户手机号查询
     */
    private suspend fun getPhoneMobile() {
        val bean = RealNameAuthenticationRequest().getPhoneInfo()
        _phoneMobileLiveData.postValue(bean.data?.mobile?:SpUtil.getLoginPhone()?:"")
    }

    /**
     * 微信获取授权token
     */
    suspend fun wechatOauth(tempToken: String): BaseBean<WechatOauthInfo> {
        val loginInfo = AccountInfoRequest().wechatOauth(tempToken)
        return loginInfo
    }

    /**
     * 绑定账号
     */


    /**
     * 微信授权登录、注册
     */
    private suspend fun loginByWechatRequest(data: WechatOauthInfo): BaseBean<LoginInfo> {
        val loginInfo = AccountInfoRequest().loginByWechat(mapOf(
            "openid" to data.openid,
            "accessToken" to data.access_token,
            "unionid" to data.unionid
        ))
        // token不为空，才算是登录成功，否则可能为注册
        if (loginInfo.isSuccess && !loginInfo.data.token.isNullOrEmpty()) {
            saveLoginInfo(loginInfo.data, ChCrypto.aesDecrypt(loginInfo.data.account), loginInfo.data.password?:"", LoginInfo.LOGIN_TYPE_WECHAT)
        }
        return loginInfo
    }

    /**
     * 获取店铺信息：手机密码登录，微信登录，选择店铺
     */
    private suspend fun getMerchantInfo(
        merchantId: String,
        phone: String
    ): BaseBean<LoginMerchantInfo> {
        val loginMerchantInfoResult = AccountInfoRequest().getMerchantInfo(merchantId)
        if (loginMerchantInfoResult.isSuccess) {
            loginMerchantInfoResult.data.phone = phone
            saveMerchantInfo(
                loginMerchantInfoResult.data,
                merchantId,
                loginMerchantInfoResult.data.isCrawler,
            )
        }
        return loginMerchantInfoResult
    }

    /**
     * 获取店铺信息
     */
    private suspend fun getMerchantInfoUnSave(
        merchantId: String,
        phone: String,
        jumpUrl: String
    ): BaseBean<LoginMerchantInfo> {
        val loginMerchantInfoResult = AccountInfoRequest().getMerchantInfo(merchantId)
        if (loginMerchantInfoResult.isSuccess) {
            loginMerchantInfoResult.data.phone = phone
            if (jumpUrl.startsWith("ybmpage://main")) {
                saveMerchantInfo(
                    loginMerchantInfoResult.data,
                    merchantId,
                    loginMerchantInfoResult.data.isCrawler
                )
            }
        }
        return loginMerchantInfoResult
    }

    /**
     * 获取accountId
     */
    fun getAccountId() {
        viewModelScope.launch {
            val accountId = AccountInfoRequest().getAccountId()
            if (accountId.isSuccess) {
                SpUtil.setAccountId(accountId.data.accountId)
            }
        }
    }

    /**
     * 获取登录协议信息
     * type 1:隐私协议 2:用户协议 0 获取url id
     */
    fun getLoginAgreement(type: Int) {
        viewModelScope.launch {
            val loginAgreementInfo = AccountInfoRequest().getLoginAgreement()
            if (type == 1) {
                if (loginAgreementInfo.getData() != null && loginAgreementInfo.getData().privacyPolicy != null && !loginAgreementInfo.getData().privacyPolicy.url.isNullOrEmpty()) {
                    RoutersUtils.open("ybmpage://commonwebviewactivity?url=" + loginAgreementInfo.getData().privacyPolicy.url);
                } else {
                    RoutersUtils.open("ybmpage://commonwebviewactivity?url=" + AppNetConfig.PRIVACE)
                }
            } else if (type == 2) {
                if (loginAgreementInfo.getData() != null && loginAgreementInfo.getData().userServiceAgreement != null && !loginAgreementInfo.getData().userServiceAgreement.url.isNullOrEmpty()) {
                    RoutersUtils.open("ybmpage://commonwebviewactivity?url=" + loginAgreementInfo.getData().userServiceAgreement.url);
                } else {
                    RoutersUtils.open("ybmpage://commonwebviewactivity?url=" + AppNetConfig.CLAUSE)
                }
            } else if (type == 0) {
                agreementsList.clear()
                if (loginAgreementInfo.getData() != null && loginAgreementInfo.getData().userServiceAgreement != null && loginAgreementInfo.getData().userServiceAgreement.id != null) {
                    val privacyAgreementsBean = PrivacyAgreementsBean(
                        loginAgreementInfo.getData().userServiceAgreement.id,
                        loginAgreementInfo.getData().userServiceAgreement.version
                    )
                    agreementsList.add(privacyAgreementsBean)
                }
                if (loginAgreementInfo.getData() != null && loginAgreementInfo.getData().privacyPolicy != null && loginAgreementInfo.getData().privacyPolicy.id != null) {
                    val privacyAgreementsBean = PrivacyAgreementsBean(
                        loginAgreementInfo.getData().privacyPolicy.id,
                        loginAgreementInfo.getData().privacyPolicy.version
                    )
                    agreementsList.add(privacyAgreementsBean)
                }
            }
        }
    }

    fun saveLoginAgreement(paramsMap: Map<String, String>) {
        viewModelScope.launch {
             AccountInfoRequest().saveLoginAgreement(paramsMap)
        }
    }
    /**
     * 保存店铺信息
     */
    private fun saveMerchantInfo(
        merchantInfo: LoginMerchantInfo,
        merchantId: String,
        isCrawler: Boolean = false
    ) {
        try {
            SessionManager.get().newSession()
            SpUtil.setMerchantid(merchantId)
            BugUtil.updateUserId(SpUtil.getMerchantid())
            SpUtil.writeBoolean(SP_KEY_LOGIN_IS_SAAS, merchantInfo.saasUser == 1)
            SpUtil.setMerchantInfo(merchantInfo)
            val baseInfo = MerchantInfo.BaseInfo()
            baseInfo.provinceCode = merchantInfo.provinceCode
            baseInfo.province = merchantInfo.province
            baseInfo.cityCode = merchantInfo.cityCode
            baseInfo.city = merchantInfo.city
            baseInfo.areaCode = merchantInfo.areaCode
            baseInfo.district = merchantInfo.district
//                baseInfo.channelNames = loginMerchantInfo.channelNames
            SpUtil.writeString(ConstantData.PROVINCECODE, merchantInfo.provinceCode)
            SpUtil.writeString(ConstantData.PROVINCE, merchantInfo.province)
            XyyIoUtil.identify(merchantId, baseInfo)
            XyyReportManager.setSignInfo(Abase.getContext(), merchantId, SpUtil.getAccountId())
            XyyIoUtil.track(XyyIoUtil.ACTION_LOGIN)
            //选择账户后设置
            AuditStatusSyncUtil.getInstance().setLicenseStatusOnly(merchantInfo.licenseStatus)
            SpUtil.setValidityStatus(merchantInfo.validity)
            //后台获取购物车的信息
//            YbmCommand.getCartListForBack()
            //更新历史登陆账户在本地的记录
            val accountBean = AccountBean()
            accountBean.userName = merchantInfo.realName
            accountBean.password = SpUtil.getPw()
            accountBean.merchantId = merchantId
            accountBean.shopName = merchantInfo.shopName
            accountBean.phone = merchantInfo.phone
            accountBean.address = merchantInfo.fullAddress
            accountBean.addTime = "${System.currentTimeMillis()}"
            accountBean.loginType = SpUtil.getLoginType()
            AccountTable.update(YBMAppLike.getApp().currActivity, accountBean)
            //绑定pushtoken
            if (!isCrawler) {
                YbmPushUtil.bindPushtoken()
            }
        } catch (e: Exception) {
            BugUtil.sendBug(Throwable("登录信息缺失"))
        }
    }

    /**
     * 存储登陆信息
     */
    private fun saveLoginInfo(loginData: LoginInfo, mobileNo: String, password: String,loginType:String?="") {
        loginData.mobileNo = mobileNo
        loginData.password = password
        loginData.loginSucceedUrl = getLoginSucceedRouter(loginData)

        //登录信息存储
        SpUtil.setLoginInfo(loginData)
        //优惠劵大礼包是否显示
        SpUtil.writeInt("show_ad_collect_pop", 1)
        SpUtil.writeInt("show_dialog_in_pay_result", 0)
        // 是否是ka用户
        SpUtil.writeBoolean(SP_KEY_LOGIN_IS_KA, false)
//        SpUtil.setMerchantid(loginData.merchantId)
        SpUtil.setToken(loginData.token)
        SpUtil.setAccountId(loginData.accountId)
        SpUtil.setLoginPhone(mobileNo)
        SpUtil.setPw(password)
        SpUtil.setLoginType(loginType)
        // 登录成功之后，需要往apm更新用户信息
        if (Countly.sharedInstance().isInitialized) {
            XyyApmCly.getInstance().login(SpUtil.getLoginPhone(), SpUtil.getMerchantid())
        }
        HandlerGoodsDao.getInstance().create4Sp()
        //后台线程获取用户信息并保存(多用户切换时用到)
//                YbmCommand.getUserInfoForBack(mobileNumber, password)

//        val accountBean = AccountBean()
//        accountBean.userName = ""
//        accountBean.password = SpUtil.getPw()
//        accountBean.merchantId = ""
//        accountBean.shopName = ""
//        accountBean.phone = loginData.mobileNo
//        accountBean.address = ""
//        accountBean.addTime = "${System.currentTimeMillis()}"
//        AccountTable.update(YBMAppLike.getApp().currActivity, accountBean)
    }

    /**
     * 登陆带用户信息：本地缓存多账号切换
     */
    fun loginWithMerchant(mobileNumber: String, password: String, merchantId: String,loginType:String?) {
        viewModelScope.launch {
            val loginInfo = AccountInfoRequest().login(ChCrypto.aesEncrypt(mobileNumber), password,loginType)
            if (!loginInfo.isSuccess || 1 == loginInfo.data?.needUpdatePassword) {
                _loginWithMerchantLiveData.postValue(
                    BaseBean.newFailureBaseBean(
                        LoginWithMerchantBean(loginInfo.data,null)
                    )
                )
                return@launch
            }
            val loginMerchantInfoResult = AccountInfoRequest().getMerchantInfo(
                merchantId,
                loginInfo.data.accountId ?: "",
                loginInfo.data.token ?: ""
            )
            if (loginMerchantInfoResult.isSuccess) {
                val loginWithMerchantBean =
                    LoginWithMerchantBean(loginInfo.data, loginMerchantInfoResult.data)
                loginWithMerchantBean.loginInfo?.let { saveLoginInfo(it, mobileNumber, password,loginType) }
                loginWithMerchantBean.loginMerchantInfo?.let {
                    saveMerchantInfo(
                        it,
                        merchantId,
                        loginMerchantInfoResult.data.isCrawler
                    )
                }
                _loginWithMerchantLiveData.postValue(
                    BaseBean.newSuccessBaseBean(
                        loginWithMerchantBean
                    )
                )
                return@launch
            }
            _loginWithMerchantLiveData.postValue(BaseBean.newFailureBaseBean(LoginWithMerchantBean()))
        }
    }

    /**
     * 登陆成功后的跳转路由
     */
    private fun getLoginSucceedRouter(loginData: LoginInfo): String {
        return LoginSucceedState.getLoginState(loginData)?.getRouterUrl() ?: ""
    }

    /**
     * 登陆成功状态
     */
    sealed class LoginSucceedState {
        abstract fun getRouterUrl(): String

        companion object {
            fun getLoginState(loginData: LoginInfo): LoginSucceedState? {
                return if (loginData.shopCount == 1 && loginData.isAudit) {
                    //关联的店铺数量等于1并且已经审核通过,跳转首页
                    SingleMerchantAndAuditedState
                } else if (loginData.shopCount == 1 && !loginData.isAudit) {
                    //关联的店铺数量等于1并且未审核通过,跳转选择登录店铺页
                    SingleMerchantAndUnAuditState
                } else if (loginData.shopCount > 1) {
                    //关联的店铺数量大于1，跳转选择登录店铺页
                    MultipleMerchantsState
                } else if (loginData.shopCount < 1) {
                    //关联的店铺数量小于1，跳转关联店铺页
                    NoMerchantState
                } else null
            }
        }

        //关联的店铺数量等于1并且已经审核通过
        object SingleMerchantAndAuditedState : LoginSucceedState() {
            //跳转首页
            override fun getRouterUrl(): String {
                return if (AccountStatus.mStatus == 3) "ybmpage://main"
                else "ybmpage://main?action_cart_style=true"
            }
        }

        //关联的店铺数量等于1并且未审核通过
        object SingleMerchantAndUnAuditState : LoginSucceedState() {
            //跳转选择登录店铺页
            override fun getRouterUrl(): String = "ybmpage://selectloginshop"
        }

        //关联的店铺数量大于1
        object MultipleMerchantsState : LoginSucceedState() {
            //跳转选择登录店铺页
            override fun getRouterUrl(): String = "ybmpage://selectloginshop"
        }

        //关联的店铺数量小于1
        object NoMerchantState : LoginSucceedState() {
            //跳转关联店铺页
            override fun getRouterUrl(): String = "ybmpage://linkshop"
        }
    }
}