package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CompanyLicenseBean
import com.ybmmarket20.bean.CompanyLicenseItem
import com.ybmmarket20.network.request.LicenseRequirementRequest
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import java.io.Closeable
import kotlin.coroutines.CoroutineContext

/**
 * 随货资质需求
 */
class LicenseRequirementViewModel(appLike: Application) : BaseViewModel(appLike) {

    private var mYBMCloseableCoroutineScope: YBMCloseableCoroutineScope? = null

    private val _licenseRequirementLiveData = MutableLiveData<BaseBean<CompanyLicenseBean>>()

    val licenseRequirementLiveData: LiveData<BaseBean<CompanyLicenseBean>> =
        _licenseRequirementLiveData

    fun getCompanyLicense(shopType: String) {
        mYBMCloseableCoroutineScope = YBMCloseableCoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)
        mYBMCloseableCoroutineScope!!.launch {
            val companyLicenseList = LicenseRequirementRequest().getCompanyLicenseListWithType(shopType)
            _licenseRequirementLiveData.postValue(companyLicenseList)
        }
    }

    fun setCompanyLicense(
        selectCompanyMap: Map<String, MutableList<CompanyLicenseItem>>,
        shopCode: String,
        companyName: String
    ) {
        val companyLicenseList = selectCompanyMap[shopCode]
        val companyLicenseBean = CompanyLicenseBean(companyName, companyLicenseList?: mutableListOf())
        val result = BaseBean.newSuccessBaseBean(companyLicenseBean)
        _licenseRequirementLiveData.postValue(result)
    }

    fun closeCoroutineScope() {
        mYBMCloseableCoroutineScope?.close()
    }
}

class YBMCloseableCoroutineScope(context: CoroutineContext) : Closeable, CoroutineScope {
    override val coroutineContext: CoroutineContext = context

    override fun close() {
        coroutineContext.cancel()
    }
}