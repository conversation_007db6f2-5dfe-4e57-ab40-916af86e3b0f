package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.HomeAlertBean
import com.ybmmarket20.network.request.HomeAlertRequest
import kotlinx.coroutines.launch


//资质弹窗
const val HOME_ALERT_TYPE_LICENSE: Int = 1
//cms弹窗
const val HOME_ALERT_TYPE_CMS: Int = 2
//大转盘弹窗
const val HOME_ALERT_TYPE_WHEEL: Int = 3

/**
 * 首页弹窗
 */
class HomeAlertViewModel(app: Application): BaseViewModel(app) {

    private val _homeAlertLiveData = MutableLiveData<BaseBean<HomeAlertBean>>()
    val homeAlertLiveData: LiveData<BaseBean<HomeAlertBean>> = _homeAlertLiveData

    fun requestAlert(params: Map<String, String>) {
        viewModelScope.launch {
            val requestHomeAlert = HomeAlertRequest().requestHomeAlert(params)
            _homeAlertLiveData.postValue(requestHomeAlert)
        }
    }

}