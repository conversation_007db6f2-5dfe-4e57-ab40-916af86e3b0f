package com.ybmmarket20.viewmodel

import android.text.TextUtils
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ybmmarket20.bean.CheckOrderDetailBean
import com.ybmmarket20.bean.CheckOrderDetailRowsBean
import com.ybmmarket20.bean.CompanyLicenseItem
import com.ybmmarket20.bean.PaymentSuiXinPinSkusBean
import com.ybmmarket20.bean.RefundProductListBean
import com.ybmmarket20.bean.RefundProductListBean.ITEMTYPE_CONTENT
import com.ybmmarket20.bean.RefundProductListBean.ITEMTYPE_PACKAGE_CONTENT
import com.ybmmarket20.bean.payment.PaymentItemBean

class PaymentGoodsViewModel: ViewModel() {

    //店铺商品信息
    private var paymentItemList: MutableList<PaymentItemBean>? = null
    private val shopCompanyCodeMap: MutableMap<String, String> = mutableMapOf()
    //选中的资质
    private val selectedLicenseSet: MutableSet<String> = mutableSetOf()
    //选中的药检报告
    private val selectedReportSet: MutableSet<String> = mutableSetOf()
    //选中的企业资质
    private val selectedCompanyMap = mutableMapOf<String, MutableList<CompanyLicenseItem>>()
    //企业名称
    var staticShopName = ""

    private val _goodsListLiveData = MutableLiveData<MutableList<RefundProductListBean>>()
    val goodsListLiveData: LiveData<MutableList<RefundProductListBean>> = _goodsListLiveData

    private val _licenseResultLiveData = MutableLiveData<Any>()
    val licenseResultLiveData: LiveData<Any> = _licenseResultLiveData

    private val _goodsLicenseIsShowLiveData = MutableLiveData<Boolean>()
    val goodsLicenseIsShowLiveData = _goodsLicenseIsShowLiveData

    fun resetGoodsList() {
        _goodsListLiveData.postValue(mutableListOf())
    }

    fun setPaymentItemList(list: MutableList<PaymentItemBean>) {
        val tempList =  paymentItemList
        paymentItemList = list.filter {
            if (it.isExpanded)!it.isGroup else true
        }.map { new ->
            if (TextUtils.isEmpty(new.shopCode)) {
                return
            }
            shopCompanyCodeMap[new.shopCode] = new.companyCode
            new
        }.toMutableList()
        if (!tempList.isNullOrEmpty()) {
            paymentItemList?.forEach {new ->
                tempList.forEach {old ->
                    if (!old.licenseStr.isNullOrEmpty() && old.shopCode == new.shopCode) {
                        new.licenseStr = old.licenseStr
                    }
                    if (!old.remarks.isNullOrEmpty() && old.shopCode == new.shopCode) {
                        new.remarks = old.remarks
                    }
                }
            }
        }
    }

    fun addSuiXinPinGoods(paymentSuiXinPinSkusBean: PaymentSuiXinPinSkusBean, shopCode: String?) {
        try {
            val suiXinPinItemList = paymentSuiXinPinSkusBean.items
                    ?.filter { it.qty > 0 }
                    ?.map {suiXinPinBean ->
                        RefundProductListBean().apply {
                            productId = suiXinPinBean.skuId
                            productName = suiXinPinBean.name
                            spec = suiXinPinBean.spec
                            imageUrl = suiXinPinBean.imageUrl
                        }
                    }
            paymentItemList?.get(0)?.setDetailListOrigin()
            paymentItemList?.get(0)?.detailList?.addAll(suiXinPinItemList?: listOf())
            generateLicenseResult(shopCode)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun getGoodsList(shopCode: String?, keyWord: String? = null) {
        paymentItemList?.forEach { paymentItemBean ->
            paymentItemBean?.detailList?.forEach {refundProductListBean ->
                refundProductListBean.companyType = paymentItemBean.getIsThirdCompany()
                refundProductListBean.isFbpShop = paymentItemBean.isFbpShop
            }
        }
        (paymentItemList
            ?.find { shopCode == it.shopCode }
            ?.detailList
            ?.filter { it.itemType == ITEMTYPE_CONTENT || it.itemType == ITEMTYPE_PACKAGE_CONTENT }
            ?.filter {
                if (keyWord.isNullOrEmpty()) true
                else it.productName.contains(keyWord)
            }
            ?.toMutableList()
            ?: mutableListOf()).let(_goodsListLiveData::postValue)
    }

    fun isSelectLicense(id: String) = selectedLicenseSet.contains(id)

    fun isSelectReport(id: String) = selectedReportSet.contains(id)

    fun changeLicenseStatus(skuId: String?) {
        if (skuId == null) return
        if (isSelectLicense(skuId)) {
            selectedLicenseSet.remove(skuId)
        } else {
            selectedLicenseSet.add(skuId)
        }
    }

    fun changeReportStatus(skuId: String?) {
        if (skuId == null) return
        if (isSelectReport(skuId)) {
            selectedReportSet.remove(skuId)
        } else {
            selectedReportSet.add(skuId)
        }
    }

    private fun selectCompanyLicense(shopCode: String?, item: CompanyLicenseItem?) {
        if (shopCode == null || item == null) return
        if (!selectedCompanyMap.containsKey(shopCode)) {
            selectedCompanyMap[shopCode] = mutableListOf()
        }
        val selectedSet = selectedCompanyMap[shopCode]
        selectedSet?.add(item)
    }

    private fun unSelectCompanyLicense(shopCode: String?, item: CompanyLicenseItem?) {
        if (shopCode == null || item == null) return
        if (!selectedCompanyMap.containsKey(shopCode)) {
            return
        }
        val selectedSet = selectedCompanyMap[shopCode]
        selectedSet?.remove(item)
    }

    fun isSelectCompanyLicense(shopCode: String?, itemType: CompanyLicenseItem?): Boolean {
        if (itemType == null || shopCode == null || !selectedCompanyMap.containsKey(shopCode)) {
            return false
        }
        val selectedSet = selectedCompanyMap[shopCode]
        return selectedSet?.contains(itemType)?: false
    }

    fun changeCompanyLicenseState(shopCode: String?, item: CompanyLicenseItem?) {
        val isSelected = isSelectCompanyLicense(shopCode, item)
        if (isSelected) {
            unSelectCompanyLicense(shopCode, item)
        } else {
            selectCompanyLicense(shopCode, item)
        }
    }

    fun generateLicenseResult(shopCode: String?) {
        val companyLicenseList = selectedCompanyMap[shopCode]?.map {
            it.itemName
        }?.toMutableList()?: mutableListOf()
        val goodsLicenseList = paymentItemList
            ?.find { it.shopCode == shopCode }
            ?.detailList
            ?.filter { !it.productName.isNullOrEmpty() }
            ?.filter { isSelectReport(it.productId) || isSelectLicense(it.productId) }
            ?.toMutableList()
            ?.map { it.productName }?: mutableListOf()
        val licenseStr = mutableListOf<String?>().apply {
            addAll(companyLicenseList)
            addAll(goodsLicenseList)
        }.joinToString(",")
        paymentItemList?.find { it.shopCode == shopCode }
            ?.licenseStr = licenseStr
        _licenseResultLiveData.postValue(Any())
    }

    fun clearData() {
        paymentItemList?.clear()
        selectedLicenseSet.clear()
        selectedReportSet.clear()
        selectedCompanyMap.clear()
        resetGoodsList()
    }

    /**
     * 获取企业资质请求参数
     */
    fun getCompanyLicenseJson(): String? {
        val companyLicenseMapList = selectedCompanyMap.filter {
            it.value.isNotEmpty()
        }.map { entry ->
            val enterpriseCredential = entry.value.filter { !it.itemType.isNullOrEmpty()}
                .joinToString(",", transform = { it.itemType.toString() })
            mapOf(
                "orgId" to shopCompanyCodeMap[entry.key],
                "enterpriseCredential" to enterpriseCredential
                )
        }
        return if (companyLicenseMapList.isEmpty()) {
            null
        } else {
            Gson().toJson(companyLicenseMapList, object:TypeToken<List<Map<String, String>>>(){}.type).toString()
        }
    }

    /**
     * 获取商品资质请求参数
     */
    fun getGoodsLicenseJson(): String? {
        val goodsLicenseMapList = paymentItemList
            ?.flatMap { it.detailList.orEmpty() }
            ?.filter { it.itemType == ITEMTYPE_CONTENT || it.itemType == ITEMTYPE_PACKAGE_CONTENT }
            ?.map {
                val isSelectedLicense = isSelectLicense(it.productId)
                val isSelectedReport = isSelectReport(it.productId)
                val productCredential = mutableListOf<String>().apply {
                    if (isSelectedReport) add("1")
                    if (isSelectedLicense) add("2")
                }.joinToString(",")
                return@map mapOf(
                    "skuId" to it.productId,
                    "productCredential" to productCredential
                )
            }?.filter { !it["productCredential"].isNullOrEmpty() }
            ?.toMutableList()
            ?: mutableListOf()
        return if (goodsLicenseMapList.isEmpty()) {
            null
        } else {
            Gson().toJson(goodsLicenseMapList, object:TypeToken<List<Map<String, String>>>(){}.type).toString()
        }
    }

    fun setStaticData(orderDetailBean: CheckOrderDetailBean) {
        //添加paymentItemList数据
        val goodsList = mutableListOf<CheckOrderDetailRowsBean>()
        val packageList = orderDetailBean.packageList?.flatMap { 
            it.orderDetailList
        }?: mutableListOf()
        goodsList.addAll(packageList)
        goodsList.addAll(orderDetailBean.detailList?:mutableListOf())
        val productList = goodsList.filter {
            it.productCredential?.contains("1") == true || it.productCredential?.contains("2") == true
        }.map {bean->
            RefundProductListBean().apply {
                productId = bean.getProductId().toString()
                productName = bean.productName
                spec = bean.spec
                imageUrl = bean.imageUrl
            }
        }.toMutableList()

        _goodsLicenseIsShowLiveData.postValue(productList.isEmpty())
        val paymentItemBean = PaymentItemBean().apply {
            detailList = productList
            shopCode = orderDetailBean.orgId
        }
        paymentItemList = mutableListOf(paymentItemBean)
        getGoodsList(orderDetailBean.orgId)
        //添加选中的药检报告和企业资质
        goodsList.forEach {
            if (it.productCredential?.contains("1") == true) {
                selectedReportSet.add("${it.getProductId()}")
            }
            if (it.productCredential?.contains("2") == true) {
                selectedLicenseSet.add("${it.getProductId()}")
            }
        }
        //添加企业资质
        val companyLicenseList = orderDetailBean.corpCredential?.split(",")
            ?.mapIndexed { index, str ->
                CompanyLicenseItem(str, "$index")
            }?.toMutableList()?: mutableListOf()
        selectedCompanyMap[orderDetailBean.orgId?: ""] = companyLicenseList
    }

    fun getSelectedCompanyMap(): Map<String, MutableList<CompanyLicenseItem>> = selectedCompanyMap

}