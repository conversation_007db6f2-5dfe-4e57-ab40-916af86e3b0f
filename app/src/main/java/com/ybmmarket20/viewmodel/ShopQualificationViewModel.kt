package com.ybmmarket20.viewmodel

import android.os.Bundle
import androidx.core.content.ContextCompat
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.R
import com.ybmmarket20.bean.AptitudeXyyBean
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.common.util.Abase
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.view.ShopQualificationItem
import com.ybmmarket20.view.ShowBottomSheetDialog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class ShopQualificationViewModel: ViewModel() {

    val shopQualificationViewModel = MutableLiveData<BaseBean<AptitudeXyyBean>>()

    private val _callPhoneLiveData: MutableLiveData<String> = MutableLiveData()
    val callPhoneLiveData: LiveData<String> = _callPhoneLiveData

    var params: String = ""

    /**
     * 初始化数据
     */
    fun initData(bundle: Bundle?) {
        bundle?.let {
            params = it.getString("params")?: ""
        }
    }

    /**
     * 获取自营店铺列表
     */
    fun getSelfQualification() {
        viewModelScope.launch {
            val result = withContext(Dispatchers.IO) {
                NetworkService.instance.getSelfQualification(SpUtil.getMerchantid(), params)
            }

            result.data?.isSelf = true
            result.data?.shopCode = params
            shopQualificationViewModel.postValue(result)
        }
    }


    /**
     * 获取pop店铺列表
     */
    fun getPopQualification() {
        viewModelScope.launch {
            val result = withContext(Dispatchers.IO) {
                NetworkService.instance.getPopQualification(params)
            }
            result.data?.isSelf = false

            result.data?.corporationItemList = result.data?.corporation?.let {
                val list = arrayListOf<ShopQualificationItem>()
                if (!it.name.isNullOrEmpty()) {
                    list.add(ShopQualificationItem("企业名称:", it.name))
                }
                if (!it.phone.isNullOrEmpty()) {
                    list.add(ShopQualificationItem("客服电话:", it.phone,
                        drawableRight = ContextCompat.getDrawable(Abase.getContext(), R.drawable.icon_aptitude_phone),
                        clickListener = { num ->
                            _callPhoneLiveData.postValue(num)
                        }
                    ))
                }
                if (!it.businessScope.isNullOrEmpty()) {
                    list.add(ShopQualificationItem("经营范围:", it.businessScope))
                }
                list
            }
            result.data?.shopNoticeItemList = result.data?.shopNotice?.let {
                val list = arrayListOf<ShopQualificationItem>()
                if (!it.expressType.isNullOrEmpty()) {
                    list.add(ShopQualificationItem("快递类型:", it.expressType))
                }
                if (!it.expressRemarks.isNullOrEmpty()) {
                    list.add(ShopQualificationItem(null, it.expressRemarks))
                }
                if (!it.orderHandleTime.isNullOrEmpty()) {
                    list.add(ShopQualificationItem(null, it.orderHandleTime))
                }
                if (!it.deliveryHandleTime.isNullOrEmpty()) {
                    list.add(ShopQualificationItem(null, it.deliveryHandleTime))
                }
                list
            }
            shopQualificationViewModel.postValue(result)
        }
    }

}