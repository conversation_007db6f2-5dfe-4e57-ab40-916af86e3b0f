package com.ybmmarket20.viewmodel

import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.SearchAggsBean
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.network.NetworkService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext

class SpecFilterViewModel: ViewModel() {

    val specFilterLiveData: MutableLiveData<BaseBean<SearchAggsBean>> = MutableLiveData()
    var mCallback: ((block: BaseBean<SearchAggsBean>?) -> Unit)? = null

//    /**
//     * 获取规格数据
//     */
//    fun getSpecFilterData(specFilterRequestData: SpecFilterRequestData) {
//        viewModelScope.launch(Dispatchers.IO) {
//            val data = NetworkService.instance.getSpecFilterData(specFilterRequestData.merchantId?: "",
//                specFilterRequestData.keyword?: "",
//                specFilterRequestData.type?: "",
//                specFilterRequestData.shopCode?: "")
//            specFilterLiveData.postValue(data)
//        }
//    }

    /**
     * 获取规格数据
     */
    fun getSpecFilterData1(params: MutableMap<String, String?>) {
        viewModelScope.launch(Dispatchers.IO) {
            val map = mutableMapOf<String, String>()
            params.forEach {
                map[it.key] = it.value?: ""
            }
            Log.i("getSpecFilterData", map.toString())
            val data = NetworkService.instance.getSpecFilterData(map)
            withContext(Dispatchers.Main) {
                specFilterLiveData.postValue(data)
                mCallback?.invoke(data)
            }
        }
    }

    fun getSpecFilterData(params: MutableMap<String, String?>) {
        viewModelScope.launch(Dispatchers.IO) {
            val paramsMap = RequestParams()
            params.forEach {
                paramsMap.putWithoutEncode(it.key, it.value?: "")
            }
            val data = getSpecFilterDataApi(paramsMap)
            withContext(Dispatchers.Main) {
                specFilterLiveData.postValue(data)
                mCallback?.invoke(data)
            }
        }
    }

    private suspend fun getSpecFilterDataApi(params: RequestParams):BaseBean<SearchAggsBean> {
        return suspendCancellableCoroutine {
            HttpManager.getInstance().post(AppNetConfig.SORTNET_aggs, params, object: BaseResponse<SearchAggsBean>() {

                override fun onSuccess(
                    content: String?,
                    obj: BaseBean<SearchAggsBean>?,
                    t: SearchAggsBean?
                ) {
                    super.onSuccess(content, obj, t)
                    specFilterLiveData.postValue(obj)
                    mCallback?.invoke(obj)
                }
            })
        }
    }

    fun setListener(callback: ((block: BaseBean<SearchAggsBean>?) -> Unit)?) {
        mCallback = callback
    }

    data class SpecFilterRequestData(
        var merchantId: String?,
        var keyword: String?,
        var type: String?,
        var shopCode: String?,
        var orgId: String?,
        var categoryId: String?
    )
}