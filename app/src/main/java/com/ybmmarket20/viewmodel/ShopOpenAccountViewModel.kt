package com.ybmmarket20.viewmodel

import android.os.Bundle
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.ClientResourcesList
import com.ybmmarket20.bean.OpenAccount
import com.ybmmarket20.bean.OpenAccountMapping

import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.view.ShopQualificationItem
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class ShopOpenAccountViewModel : ViewModel() {

    val openAccountLiveData = MutableLiveData<BaseBean<OpenAccountMapping>>()
    var orgId: String = ""

    fun initData(bundle: Bundle?) {
        orgId = bundle?.getString("params") ?: ""
    }

    /**
     * 获取开户数据
     */
    fun getShopOpenAccountData() {
        viewModelScope.launch(Dispatchers.IO) {
            val result =
                NetworkService.instance.getShopOpenAccountData(SpUtil.getMerchantid(), orgId)
            openAccountLiveData.postValue(mappingData(result))
        }
    }

    /**
     * 映射数据
     */
    private fun mappingData(src: BaseBean<OpenAccount>): BaseBean<OpenAccountMapping> {
        val desData = OpenAccountMapping()
        if (src.isSuccess) {
            val srcData = src.data
            srcData?.apply {
                desData.companyName = corporationVo?.name ?: ""
                desData.customerServiceNumber = corporationVo?.phone ?: ""
                desData.operatingRange = corporationVo?.businessScope ?: ""
                desData.electronicType = clientAccountVo?.electronicType ?: 0
                desData.pagerType = clientAccountVo?.pagerType ?: 0
                desData.electronicExplain = clientAccountVo?.electronicExplain ?: ""
                desData.pagerExplain = clientAccountVo?.pagerExplain ?: ""
                desData.receiver = clientAccountVo?.receiver ?: ""
                desData.receiverPhone = clientAccountVo?.phone ?: ""
                desData.address = clientAccountVo?.address ?: ""
                desData.remark = clientAccountVo?.remark ?: ""
                desData.openAccountInstruction = clientAccountVo?.explanationContent ?: ""
                desData.openAccountData = getGroupAndChildData(clientResourcesVO ?: mutableListOf())
                desData.openAccountModeList = mutableListOf()
                if (desData.electronicType != 0) {
                    //电子版
                    val electronicList = listOf(
                        ShopQualificationItem("电子版开户"),
                        ShopQualificationItem(desc = desData.electronicExplain)
                    )
                    desData.openAccountModeList!!.addAll(electronicList)
                }

                if (desData.pagerType != 0) {
                    //纸质版
                    val paperList = listOf(
                        ShopQualificationItem("纸质版开户"),
                        ShopQualificationItem(desc = desData.pagerExplain),
                        ShopQualificationItem("收件人: ", desData.receiver),
                        ShopQualificationItem("电话: ", desData.receiverPhone),
                        ShopQualificationItem("收件地址: ", desData.address),
                        ShopQualificationItem("备注: ", desData.remark)
                    )
                    desData.openAccountModeList!!.addAll(paperList)
                }
            }
        }
        val result = BaseBean<OpenAccountMapping>()
        result.code = src.code
        result.status = src.status
        result.data = desData
        return result
    }

    /**
     * 获取
     */
    private fun getGroupAndChildData(clientResourcesVOs: MutableList<ClientResourcesList>): MutableList<ClientResourcesList> =
        clientResourcesVOs.map {
            val group = ClientResourcesList()
            group.subItems.clear()
            group.resourceName = it.resourceName
            group.subItems.add(it)
            group.isGroup = true
            group
        }.toMutableList()


}