package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.AuthenticationProcessingInfo
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.network.request.AuthenticationProcessingRequest
import kotlinx.coroutines.launch

/**
 * 查询审核进行中信息
 */
class AuthenticationProcessingViewModel: ViewModel() {

    //审核进行中
    private val _authenticationProcessingInfoLiveData = MutableLiveData<BaseBean<AuthenticationProcessingInfo>>()
    val authenticationProcessingInfoLiveData: LiveData<BaseBean<AuthenticationProcessingInfo>> = _authenticationProcessingInfoLiveData

    //查询审核进行中信息
    fun queryAuthenticationProcessingInfo(accountId: String, merchantId: String) {
        viewModelScope.launch {
            val queryAuthenticationProcessingInfo =
                AuthenticationProcessingRequest().queryAuthenticationProcessingInfo(accountId, merchantId)
            _authenticationProcessingInfoLiveData.postValue(queryAuthenticationProcessingInfo)
        }
    }

}