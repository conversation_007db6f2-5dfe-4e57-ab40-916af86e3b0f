package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RebateVoucherStartActInfoBean
import com.ybmmarket20.network.request.RebateVoucherRequest
import kotlinx.coroutines.launch

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2025/7/11 16:12
 *    desc   :
 */
class RebateVoucherViewModel : ViewModel() {
    private val _rebateVoucherInfoLiveData =
        MutableLiveData<BaseBean<RebateVoucherStartActInfoBean>>()
    val rebateVoucherInfoLiveData: LiveData<BaseBean<RebateVoucherStartActInfoBean>> =
        _rebateVoucherInfoLiveData

    fun getRebateVoucherInfo() {
        viewModelScope.launch {
            val liveData =
                RebateVoucherRequest().getRebateVoucherInfo()
            _rebateVoucherInfoLiveData.postValue(liveData)
        }
    }
}