package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.xyy.canary.utils.LogUtil
import com.ybmmarket20.bean.*
import com.ybmmarket20.network.request.Mine2CommonToolsRequest
import com.ybmmarket20.utils.SpUtil
import kotlinx.coroutines.launch

/**
 * 我的页面viewmodel
 */
class Mine2ViewModel(app: Application): BaseViewModel(app) {

    //常用工具
    private val _commonToolsLiveData: MutableLiveData<BaseBean<List<UseToolsBean>>> = MutableLiveData()
    val commonToolsLiveData: LiveData<BaseBean<List<UseToolsBean>>> = _commonToolsLiveData
    //订单气泡数量
    private val _ordersBubbleCountData: MutableLiveData<BaseBean<OrderStatusNumber>> = MutableLiveData()
    val ordersBubbleCountData: LiveData<BaseBean<OrderStatusNumber>> = _ordersBubbleCountData

    //个人中心展位
    private val _boothLiveData = MutableLiveData<BaseBean<BoothData>>()
    val boothLiveData: LiveData<BaseBean<BoothData>> = _boothLiveData

    //平安进件
    private val _pingAnPrePayLiveData = MutableLiveData<BaseBean<PingAnPrePay>>()
    val pingAnPrePayLiveData: LiveData<BaseBean<PingAnPrePay>> = _pingAnPrePayLiveData

    //京东采购金融
    private val _jdPrePayLiveData = MutableLiveData<BaseBean<String>>()
    val jdPrePayLiveData: LiveData<BaseBean<String>> = _jdPrePayLiveData

    //农行链e贷
    private val _nongEPayLiveData = MutableLiveData<BaseBean<PayNongData>>()
    val nongEPrePayLiveData: LiveData<BaseBean<PayNongData>> = _nongEPayLiveData

    //购物金充值
    private val _shoppingGoldRechargeBeanData: MutableLiveData<BaseBean<ShoppingGoldRechargeBean>> = MutableLiveData()
    val shoppingGoldRechargeBeanData: LiveData<BaseBean<ShoppingGoldRechargeBean>> = _shoppingGoldRechargeBeanData

    /**
     * 获取常用工具数据
     */
    fun getCommonTools() {
        viewModelScope.launch {
            val commonTools = Mine2CommonToolsRequest().getCommonTools(SpUtil.getMerchantid())
            _commonToolsLiveData.postValue(commonTools)
        }
    }

    /**
     * 获取订单气泡数量
     */
    fun getOrdersBubbleCount() {
        viewModelScope.launch {
            val ordersBubbleCountBean = Mine2CommonToolsRequest().getOrdersBubbleCount(SpUtil.getMerchantid())
            _ordersBubbleCountData.postValue(ordersBubbleCountBean)
        }
    }

    /**
     * 获取个人中心展位
     */
    fun getBoothData(sceneType: String) {
        viewModelScope.launch {
            val boothData = Mine2CommonToolsRequest().getBoothData(sceneType)
            _boothLiveData.postValue(boothData)
        }
    }

    /**
     * 获取平安进件参数
     */
    fun getPingAnPrePayUrl() {
        viewModelScope.launch {
            val pingAnPrePay = Mine2CommonToolsRequest().getPingAnPrePay(SpUtil.getMerchantid())
//            if (pingAnPrePay.isSuccess) {
//                _pingAnPrePayLiveData.postValue(BaseBean.newSuccessBaseBean("${AppNetConfig.PING_AN_URL}?thirdApplyNo=${pingAnPrePay.data.thirdApplyNo}&channelCode=${pingAnPrePay.data.channelCode}"))
//                return@launch
//            }
            _pingAnPrePayLiveData.postValue(pingAnPrePay)
        }
    }

    /**
     * 获取京东采购金融联合登录
     */
    fun getJDPayLogin() {
        viewModelScope.launch {
            val jdPrePay = Mine2CommonToolsRequest().getJDPrePayUrl(SpUtil.getMerchantid())
            if (!jdPrePay.data?.url.isNullOrEmpty()) {
                val jdLoginResult = Mine2CommonToolsRequest().jdLogin(SpUtil.getMerchantid(), jdPrePay.data!!.url!!)
                _jdPrePayLiveData.postValue(jdLoginResult)
                return@launch
            }
            _jdPrePayLiveData.postValue(BaseBean.newFailureBaseBean(""))
        }
    }

    /**
     * 获取京东采购金融联合登录
     */
    fun getJDPayLogin(url: String) {
        viewModelScope.launch {
            val jdLoginResult = Mine2CommonToolsRequest().jdLogin(SpUtil.getMerchantid(), url)
            _jdPrePayLiveData.postValue(jdLoginResult)
        }
    }

    /**
     * 获取农行申请开通 h5
     */
    fun getNongH5Url() {
        viewModelScope.launch {
            val nongEPayResult = Mine2CommonToolsRequest().getNongH5Url(SpUtil.getMerchantid())
            _nongEPayLiveData.postValue(nongEPayResult)
        }
    }

    fun getShoppingGoldRechargeBean() {
        viewModelScope.launch {
            val shoppingGoldRechargeBean = Mine2CommonToolsRequest().getShoppingGoldRechargeBean(SpUtil.getMerchantid())
            _shoppingGoldRechargeBeanData.postValue(shoppingGoldRechargeBean)
        }
    }
}