package com.ybmmarket20.viewmodel

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CartDataBean
import com.ybmmarket20.bean.CartGoodsInfo
import com.ybmmarket20.bean.PaymentSuiXinPinSkusItemBean
import com.ybmmarket20.bean.RecommendSuiXinPinBean
import com.ybmmarket20.bean.SpellGroupGoodsItem
import com.ybmmarket20.bean.SpellGroupRecommendGoodsBean
import com.ybmmarket20.network.request.JDPayRequest
import com.ybmmarket20.utils.SpUtil
import kotlinx.coroutines.launch
import java.math.BigDecimal

/**********************页面跳转类型*****************/
//正常商品跳转提单页
const val SPELL_GROUP_RECOMMEND_DEFAULT = 0
//随心拼立即拼团
const val SPELL_GROUP_RECOMMEND_RIGHT_NOW = 1
//随心拼跳转到选品页面
const val SPELL_GROUP_RECOMMEND_SELECT_GOODS = 2
// 加入购物车
const val SPELL_GROUP_RECOMMEND_ADD_TO_CART = 3
/**************************************************/

/*************************当前页面******************/
//随心拼发起页面
const val CURRENT_PAGE_DEFAULT = 0
//提单页
const val CURRENT_PAGE_PAYMENT = 2
//选择推荐品页面
const val CURRENT_PAGE_SELECT_RECOMMEND_GOODS = 1
/**************************************************/

/***********************路由类型********************/
//关闭页面
//const val ROUTER_TYPE_FINISH = 0
//跳转页面
//const val ROUTER_TYPE_JUMP = 1
/**************************************************/

/**
 * 随心拼
 */
class SpellGroupRecommendGoodsViewModel: ViewModel() {

    //跳转路径类型
    val mJumpPathType = SpellGroupRecommendGoodsStatusBean()
    //随心拼购物车数据
    private val _spellGroupRecommendGoodsLiveData = MutableLiveData<SpellGroupRecommendGoodsBean>()
    val spellGroupRecommendGoodsLiveData: LiveData<SpellGroupRecommendGoodsBean> = _spellGroupRecommendGoodsLiveData
    //顺手买购物车数据
    private val _spellGroupWithoutGoodsLiveData = MutableLiveData<SpellGroupRecommendGoodsBean>()
    val spellGroupWithoutGoodsLiveData: LiveData<SpellGroupRecommendGoodsBean> = _spellGroupWithoutGoodsLiveData
    //店铺code
    var shopCode: String = ""
    // orgId，用于pop店铺的商详、楼层数据请求
    var orgId: String = ""
    //主品skuId
    var mainGoodsSkuId: String = ""
    //主品数量
    var mainGoodsCount: String = ""
    //是否是三方店铺 0:自营 1:pop
    var isThirdCompany: Int = 0
    //主品PId
    var mainGoodsPId: String? = ""
    //顺手买一件数据
    private val _buySomethingBeanLiveData = MutableLiveData<BaseBean<List<PaymentSuiXinPinSkusItemBean>>>()
    val buySomethingBeanLiveData: LiveData<BaseBean<List<PaymentSuiXinPinSkusItemBean>>> =
        _buySomethingBeanLiveData
    private val _buySomethingBeanPopLiveDate = MutableLiveData<BaseBean<List<PaymentSuiXinPinSkusItemBean>>>()
    val buySomethingBeanPopLiveData: LiveData<BaseBean<List<PaymentSuiXinPinSkusItemBean>>> =
        _buySomethingBeanPopLiveDate
    private val _changeCartForPromotionBeanLiveData = MutableLiveData<BaseBean<CartDataBean>>()
    val changeCartForPromotionBeanLiveData: LiveData<BaseBean<CartDataBean>> =
        _changeCartForPromotionBeanLiveData
    /**
     * 清理随心拼推荐为0的品
     */
    fun clearRecommendGoods() {
        val spellGroupRecommendGoodsBean = _spellGroupRecommendGoodsLiveData.value
        spellGroupRecommendGoodsBean?.let { bean ->
            val mapping = bean.goodsIdMapping
            val rowsBean = bean.rowsBean
            var isClear = false
            mapping.forEach {
                if (it.value == 0) {
                    isClear = true
                    return@forEach
                }
            }
            if (!isClear) return@let
            val resultKeyList = mapping.filterKeys { mapping[it] == 0 }
            resultKeyList.forEach {
                mapping.remove(it.key)
            }
            rowsBean.removeAll { !mapping.containsKey(it.skuId) }
        }
        spellGroupRecommendGoodsBean?.isUpdateData = false
//        spellGroupRecommendGoodsBean.let(_spellGroupRecommendGoodsLiveData::postValue)
    }
    /**
     * 清理顺手买推荐为0的品
     */
    fun clearWithoutGoods() {
        val spellGroupWithoutGoodsBean = _spellGroupWithoutGoodsLiveData.value
        spellGroupWithoutGoodsBean?.let { bean ->
            val mapping = bean.goodsIdMapping
            val rowsBean = bean.rowsBean
            var isClear = false
            mapping.forEach {
                if (it.value == 0) {
                    isClear = true
                    return@forEach
                }
            }
            if (!isClear) return@let
            val resultKeyList = mapping.filterKeys { mapping[it] == 0 }
            resultKeyList.forEach {
                mapping.remove(it.key)
            }
            rowsBean.removeAll { !mapping.containsKey(it.skuId) }
        }
        spellGroupWithoutGoodsBean?.isUpdateData = false
//        spellGroupWithoutGoodsBean.let(_spellGroupWithoutGoodsLiveData::postValue)
    }

    /**
     * 更新数据
     */
    fun updateData(spellGroupRecommendGoodsBean: SpellGroupRecommendGoodsBean, isUpdate: Boolean = false) {
        spellGroupRecommendGoodsBean.isUpdateData = isUpdate
        _spellGroupRecommendGoodsLiveData.postValue(spellGroupRecommendGoodsBean)
    }
    /**
     * 更新数据
     */
    fun updateWithoutData(spellGroupRecommendGoodsBean: SpellGroupRecommendGoodsBean, isUpdate: Boolean = false) {
        spellGroupRecommendGoodsBean.isUpdateData = isUpdate
        _spellGroupWithoutGoodsLiveData.postValue(spellGroupRecommendGoodsBean)
    }

    /**
     * 设置跳转类型
     */
    fun registerJumpType(jumpPathType: Int) {
        mJumpPathType.jumpPathType = jumpPathType
    }

    /**
     * 添加到购物车
     * @param rows 商品数据
     * @param isAdd true: 加购 false: 减购
     * @param goodsAmount 添加的数量
     */
    fun addSpellGroupRecommendCart(rows: SpellGroupGoodsItem, isAdd: Boolean, goodsAmount: Int = 1) {
        Log.e("guan","${rows.goodsTitle} ${isAdd} ${goodsAmount}")
        val spellGroupRecommendGoodsBean = addSpellGroupRecommendCartWithoutPost(rows, isAdd, goodsAmount)?: return
        _spellGroupRecommendGoodsLiveData.postValue(spellGroupRecommendGoodsBean)
    }
    /**
     * 添加到购物车
     * @param rows 商品数据
     * @param isAdd true: 加购 false: 减购
     * @param goodsAmount 添加的数量
     */
    fun addWithoutShopCart(rows: SpellGroupGoodsItem, isAdd: Boolean, goodsAmount: Int = 1) {
        val spellGroupRecommendGoodsBean = addWithoutCartWithoutPost(rows, isAdd, goodsAmount)?: return
        _spellGroupWithoutGoodsLiveData.postValue(spellGroupRecommendGoodsBean)
    }

    fun addSpellGroupRecommendCartForList(list: MutableList<RecommendSuiXinPinBean>) {
        if (list.isEmpty()) return
        var resultBean: SpellGroupRecommendGoodsBean? = null
        list.forEach {
            val rowsBean = it.rowsBean
            val item = SpellGroupGoodsItem(
                rowsBean.imageUrl,
                rowsBean.markerUrl,
                rowsBean.showName,
                rowsBean.actSuiXinPin?.suiXinPinPrice ?: "0",
                rowsBean.productUnit,
                "${rowsBean.fob}",
                0,
                rowsBean.productId,
                rowsBean.stepNum,
                rowsBean.isSplit,
                source = rowsBean.sourceType,
                nearEffect = null
            )
            val spellGroupRecommendGoodsBean = addSpellGroupRecommendCartWithoutPost(item, it.isAdd, it.count)
            if (spellGroupRecommendGoodsBean != null) resultBean = spellGroupRecommendGoodsBean
        }
        resultBean?.let {
            _spellGroupRecommendGoodsLiveData.postValue(it)
        }
    }


    /**
     * 添加购物车数据，只加购，不更新到页面
     */
    fun addSpellGroupRecommendCartWithoutPost(rows: SpellGroupGoodsItem, isAdd: Boolean, goodsAmount: Int = 1): SpellGroupRecommendGoodsBean? {
        Log.e("guan","${rows.goodsTitle} ${isAdd} ${goodsAmount}")
        val spellGroupRecommendGoodsBean = spellGroupRecommendGoodsLiveData.value?: SpellGroupRecommendGoodsBean()
        val mapping = spellGroupRecommendGoodsBean.goodsIdMapping
        var cartGoodsInfo = spellGroupRecommendGoodsBean.cartGoodsInfo
        if (cartGoodsInfo == null) cartGoodsInfo = CartGoodsInfo()
        if (!mapping.containsKey(rows.skuId)) {
            //随心拼购物车中不含该商品
            if (isAdd) {
                mapping[rows.skuId?: ""] = goodsAmount
                spellGroupRecommendGoodsBean.rowsBean.add(rows)
            }
            else {
                //无商品无法减购
                return null
            }
        } else {
            //随心拼购物车已有该商品
            val cartGoodsAmount = mapping[rows.skuId?: ""]?: 0
            if (isAdd) {
                mapping[rows.skuId?: ""] = cartGoodsAmount + goodsAmount
            } else {
                mapping[rows.skuId?: ""] = cartGoodsAmount - goodsAmount
            }
        }
        //随心拼商品商品数量
        if (isAdd) {
            cartGoodsInfo.goodsTotalCount += goodsAmount
        } else {
            cartGoodsInfo.goodsTotalCount -= goodsAmount
        }
        //随心拼商品总价
        val originPrice = BigDecimal(cartGoodsInfo.totalPrice)
        val newPrice = BigDecimal(rows.goodsPrice).multiply(BigDecimal(goodsAmount))
        val totalPrice = (if (isAdd) originPrice.plus(newPrice) else originPrice.minus(newPrice)).toString()
        cartGoodsInfo.totalPrice = totalPrice
        spellGroupRecommendGoodsBean.isUpdateData = true
        //随心拼商品种类
        cartGoodsInfo.goodsCategoriesCount = mapping.count { it.value != 0 }
//        _spellGroupRecommendGoodsLiveData.postValue(spellGroupRecommendGoodsBean)
        return spellGroupRecommendGoodsBean;
    }
    /**
     * 添加购物车数据，只加购，不更新到页面
     */
    fun addWithoutCartWithoutPost(rows: SpellGroupGoodsItem, isAdd: Boolean, goodsAmount: Int = 1): SpellGroupRecommendGoodsBean? {
        val spellGroupRecommendGoodsBean = spellGroupWithoutGoodsLiveData.value?: SpellGroupRecommendGoodsBean()
        val mapping = spellGroupRecommendGoodsBean.goodsIdMapping
        var cartGoodsInfo = spellGroupRecommendGoodsBean.cartGoodsInfo
        if (cartGoodsInfo == null) cartGoodsInfo = CartGoodsInfo()
        if (!mapping.containsKey(rows.skuId)) {
            //随心拼购物车中不含该商品
            if (isAdd) {
                mapping[rows.skuId?: ""] = goodsAmount
                spellGroupRecommendGoodsBean.rowsBean.add(rows)
            }
            else {
                //无商品无法减购
                return null
            }
        } else {
            //随心拼购物车已有该商品
            val cartGoodsAmount = mapping[rows.skuId?: ""]?: 0
            if (isAdd) {
                mapping[rows.skuId?: ""] = cartGoodsAmount + goodsAmount
            } else {
                mapping[rows.skuId?: ""] = cartGoodsAmount - goodsAmount
            }
        }
        //随心拼商品商品数量
        if (isAdd) {
            cartGoodsInfo.goodsTotalCount += goodsAmount
        } else {
            cartGoodsInfo.goodsTotalCount -= goodsAmount
        }
        //随心拼商品总价
        val originPrice = BigDecimal(cartGoodsInfo.totalPrice)
        val newPrice = BigDecimal(rows.goodsPrice).multiply(BigDecimal(goodsAmount))
        val totalPrice = (if (isAdd) originPrice.plus(newPrice) else originPrice.minus(newPrice)).toString()
        cartGoodsInfo.totalPrice = totalPrice
        spellGroupRecommendGoodsBean.isUpdateData = true
        //随心拼商品种类
        cartGoodsInfo.goodsCategoriesCount = mapping.count { it.value != 0 }
        return spellGroupRecommendGoodsBean;
    }

    /**
     * 获取随心拼路由
     */
    private fun getRouter(currentPage: Int, isFinish: Boolean, routerParams: Map<String, String> = hashMapOf()): String {
        return when (currentPage) {
            //随心拼发起页面
            CURRENT_PAGE_DEFAULT -> RouterState.DefaultState
            //提单页面
            CURRENT_PAGE_PAYMENT -> RouterState.PaymentState
            //选品页面
            CURRENT_PAGE_SELECT_RECOMMEND_GOODS -> RouterState.SelectGoodsState
            else -> RouterState.DefaultState
        }.getRouter(isFinish, mJumpPathType, routerParams)
    }

    /**
     * 获取跳转路由
     */
    fun getJumpRouter(currentPage: Int, routerParams: Map<String, String> = hashMapOf()): String {
        return getRouter(currentPage, false, routerParams)
    }

    /**
     * 获取finish路由
     */
    fun getFinishRouter(currentPage: Int, routerParams: Map<String, String> = hashMapOf()): String {
        return getRouter(currentPage, true, routerParams)
    }

    data class SpellGroupRecommendGoodsStatusBean(
        // TODO guanchong 增加jumpPathType
        var jumpPathType: Int = SPELL_GROUP_RECOMMEND_DEFAULT
    )

    /**
     * 随心拼跳转相关
     */
    sealed class RouterState {
        open fun getRouter(isFinish: Boolean, recommendGoodsStatusBean: SpellGroupRecommendGoodsStatusBean, routerParams: Map<String, String>): String = ""
        
        open fun getRouterState(currentPage: Int): RouterState = DefaultState

        /**
         * 为url添加参数
         */
        fun generateRouterUrl(url: String, params: Map<String, String>): String {
            var routerUrl = url
            params.forEach {
                routerUrl = if (routerUrl.contains("?")) {
                    "$routerUrl&${it.key}=${it.value}"
                } else {
                    "$routerUrl?${it.key}=${it.value}"
                }
            }
            return routerUrl
        }

        /**
         * 随心拼启动页
         */
        object DefaultState : RouterState() {
            override fun getRouter(
                isFinish: Boolean,
                recommendGoodsStatusBean: SpellGroupRecommendGoodsStatusBean,
                routerParams: Map<String, String>
            ): String {
                return when (recommendGoodsStatusBean.jumpPathType) {
                    SPELL_GROUP_RECOMMEND_RIGHT_NOW -> generateRouterUrl("ybmpage://payment", routerParams)
                    SPELL_GROUP_RECOMMEND_SELECT_GOODS -> generateRouterUrl("ybmpage://spellgrouprecommendselectedgoodsactivity", routerParams)
                    SPELL_GROUP_RECOMMEND_DEFAULT -> generateRouterUrl("ybmpage://payment", routerParams)
                    else -> ""
                }
            }
        }

        /**
         * 提单页面
         */
        object PaymentState : RouterState() {
            override fun getRouter(
                isFinish: Boolean,
                recommendGoodsStatusBean: SpellGroupRecommendGoodsStatusBean,
                routerParams: Map<String, String>
            ): String {
                return when (recommendGoodsStatusBean.jumpPathType) {
                    SPELL_GROUP_RECOMMEND_RIGHT_NOW -> {
                        if (isFinish) {
                            //弹框清空数据后退出
                            "ybmaction://clearsuixinpindata"
                        } else {
                            generateRouterUrl("ybmpage://spellgrouprecommendselectedgoodsactivity", routerParams)
                        }
                    }
                    SPELL_GROUP_RECOMMEND_SELECT_GOODS -> generateRouterUrl("ybmaction://finish", routerParams)
                    else -> {
                        if  (isFinish) {
                            generateRouterUrl("ybmaction://finish", routerParams)
                        } else ""
                    }
                }
            }
        }

        /**
         * 专区选品页面
         */
        object SelectGoodsState : RouterState() {
            override fun getRouter(
                isFinish: Boolean,
                recommendGoodsStatusBean: SpellGroupRecommendGoodsStatusBean,
                routerParams: Map<String, String>
            ): String {
                return when (recommendGoodsStatusBean.jumpPathType) {
                    SPELL_GROUP_RECOMMEND_RIGHT_NOW -> generateRouterUrl("ybmaction://finish", routerParams)
                    SPELL_GROUP_RECOMMEND_SELECT_GOODS -> {
                        if (isFinish) {
                            //弹框清空数据后退出
                            "ybmaction://clearsuixinpindata"
                        } else {
                            generateRouterUrl("ybmpage://payment", routerParams)
                        }
                    }
                    else -> {
                        if  (isFinish) {
                            generateRouterUrl("ybmaction://finish", routerParams)
                        } else ""
                    }
                }
            }
        }
    }

    /**
     * 顺手买一件数据
     */
    fun getBuySomethingData(buySomethingCasuallyInfo:String) {
        viewModelScope.launch {
            val buySomethingData = JDPayRequest().getBuySomethingData(SpUtil.getMerchantid(),buySomethingCasuallyInfo)
            _buySomethingBeanLiveData.postValue(buySomethingData)
        }
    }

    /**
     * 顺手买一件弹窗数据
     */
    fun getBuySomethingPopData(buySomethingCasuallyInfo: String) {
        viewModelScope.launch {
            val buySomethingData =
                JDPayRequest().getBuySomethingData(SpUtil.getMerchantid(), buySomethingCasuallyInfo)
            _buySomethingBeanPopLiveDate.postValue(buySomethingData)
        }
    }

    fun clearBuySomethingPopData() {
        viewModelScope.launch {
            _buySomethingBeanPopLiveDate.postValue(BaseBean.newSuccessBaseBean(mutableListOf()))
        }
    }

    /**
     * 限购数量查询接口
     */
    fun getChangeCartForPromotion(
        skuId: String,
        amount: Int,
        bean: SpellGroupGoodsItem,
        isAdd: Boolean,
        goodsAmount: Int = 1
    ) {
        viewModelScope.launch {
            val liveData =
                JDPayRequest().getChangeCartForPromotion(SpUtil.getMerchantid(), skuId, amount)
            if (liveData.isSuccess && liveData.getData().qty == amount) {
                addWithoutShopCart(
                    bean,
                    isAdd,
                    goodsAmount
                )
            } else {
                _changeCartForPromotionBeanLiveData.postValue(liveData)
            }
        }
    }
}