package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.xyyio.analysis.util.DeviceInfoUtils
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.JDPWBean
import com.ybmmarket20.network.request.JDPayRequest
import com.ybmmarket20.network.request.QueryDeviceStatusOnPayRequest
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarketkotlin.utils.ChCrypto
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.util.*

class JDPWSettingViewModel(app: Application) : BaseViewModel(app){

    private val _jDPWSettingLiveData = MutableLiveData<BaseBean<JDPWBean>>()
    val jDPWSettingLiveData: LiveData<BaseBean<JDPWBean>> = _jDPWSettingLiveData

    private val _registerFingerprintLiveData = MutableLiveData<BaseBean<Any>>()
    val registerFingerprintLiveData: LiveData<BaseBean<Any>> = _registerFingerprintLiveData

    fun queryPWSettingStatus() {
        viewModelScope.launch {
            val queryPWSettingStatus = JDPayRequest().queryPWSettingStatus()
            _jDPWSettingLiveData.postValue(queryPWSettingStatus)
        }
    }

    /**
     * 查询支付密码设置状态（包含指纹状态）
     */
    fun queryPWSettingStatusWithFingerprint(checkType: Int) {
        viewModelScope.launch {
            val queryPWSettingStatusDeferred = async { JDPayRequest().queryPWSettingStatus() }
            val queryDeviceStatusOnPayDeferred =
                async { QueryDeviceStatusOnPayRequest().queryDeviceStatusOnPay() }
            val queryPWSettingStatus = queryPWSettingStatusDeferred.await()
            val queryDeviceStatusOnPay = queryDeviceStatusOnPayDeferred.await()
            if (queryPWSettingStatus.isSuccess && queryDeviceStatusOnPay.isSuccess) {
                queryPWSettingStatus.data.deviceStatusOnPay = queryDeviceStatusOnPay.data
            }
            queryPWSettingStatus.data?.checkType = checkType
            _jDPWSettingLiveData.postValue(queryPWSettingStatus)
        }
    }

    /**
     * 获取指纹开启状态
     * 需要通过后端状态和本地状态都开启才会开启
     */
    fun getPayFingerprintStatus(): Boolean =
        SpUtil.getPayFingerprintStatus() && _jDPWSettingLiveData.value?.data?.deviceStatusOnPay?.deviceStatus == 1

    /**
     * 注册指纹
     */
    fun registerAndUpdateFingerprint(status: String) {
        viewModelScope.launch {
            val charset = "UTF-8"
            val formatType = "JSON"
            val version = "1.0"
            val signType = "SHA-256"
            val timestamp = System.currentTimeMillis()
            val data = JSONObject().apply {
                put("deviceStatus", status)
                put("deviceId", DeviceInfoUtils.getDeviceId(getApplication()))
            }.toString()
            val dataEncrypted = ChCrypto.jdPayAesFingerprintEncryptWithKey(data)
            val sign = "charset=$charset&data=$dataEncrypted&formatType=$formatType&signType=$signType&timestamp=$timestamp&version=$version&key=quztwFr2HIl5a1GG"
            val signEncrypted = ChCrypto.shaEncrypt256(sign.uppercase(Locale.getDefault()))?: ""
            val params = mutableMapOf(
                "data" to dataEncrypted,
                "sign" to signEncrypted,
                "charset" to charset,
                "formatType" to formatType,
                "version" to version,
                "signType" to signType
            )
            val registerFingerprint = QueryDeviceStatusOnPayRequest().registerAndUpdateFingerprint(params, "$timestamp")
            _registerFingerprintLiveData.postValue(registerFingerprint)
        }
    }
}