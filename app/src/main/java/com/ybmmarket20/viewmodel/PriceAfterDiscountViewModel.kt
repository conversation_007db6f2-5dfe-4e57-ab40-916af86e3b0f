package com.ybmmarket20.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.network.NetworkService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 折后价
 */
class PriceAfterDiscountViewModel : ViewModel() {

    val liveData: MutableLiveData<Map<String, String>> = MutableLiveData()

    /**
     * 根据商品id获取折后价
     */
    fun getPriceAfterDiscount(rowsList: MutableList<RowsBean>?) {
        viewModelScope.launch() {
            rowsList?.apply {
                val ids = asSequence()
                    .filter { it.actPt != null && it.actPt.assembleStatus != 1 }
                    .map { it.id }
                    .joinToString(",")
                val priceAfterDiscountList = NetworkService.instance.getPriceAfterDiscount(ids).data
                val priceAfterDiscountMap = priceAfterDiscountList.associate { it.skuId to it.price }
            }
        }
    }

}