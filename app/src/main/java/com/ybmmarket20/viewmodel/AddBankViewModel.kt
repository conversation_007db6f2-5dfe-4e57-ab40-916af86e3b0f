package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.*
import com.ybmmarket20.network.request.JDPayRequest
import kotlinx.coroutines.launch

class AddBankViewModel(val app: Application): BaseViewModel(app) {

    //头部是否展开
    var isHeaderExpand: Boolean = true
    //底部是否展开
    var isFooterExpand: Boolean = false
    //激活输入输入框
    private var isActiveEdit: Boolean = false

    lateinit var signBankBeanData: SignBankBean

    //通过头部切换
    private val _signBankListLiveData = MutableLiveData<BaseBean<SignBankBean>>()
    val signBankListLiveData: LiveData<BaseBean<SignBankBean>> = _signBankListLiveData
    //根据卡号校验是否已经绑卡(重复绑定)
    private val _cardBindLiveData = MutableLiveData<BaseBean<BankCardBindState>>()
    val cardBindLiveData: LiveData<BaseBean<BankCardBindState>> = _cardBindLiveData
    //点击一键签约银行卡
    private val _payTypeItemClickLiveData = MutableLiveData<Pair<String?, String?>>()
    val payTypeItemClickLiveData: LiveData<Pair<String?, String?>> = _payTypeItemClickLiveData

    //按卡号绑卡确认
    private val _bankCardInfoLiveData = MutableLiveData<BaseBean<BankCardInfo>>()
    val bankCardInfoLiveData: LiveData<BaseBean<BankCardInfo>> = _bankCardInfoLiveData

    /**
     * 获取全部列表（通过网络获取）
     */
    fun getSignBankInitList() {
        viewModelScope.launch {
            val queryOneKeySignBankList = JDPayRequest().queryOneKeySignBankList()
            if (queryOneKeySignBankList.isSuccess) {
                val data = queryOneKeySignBankList.data
                signBankBeanData = data
                data.bankNodeList = mutableListOf()
                if (data.bankList?.isNotEmpty() == true) {
                    data.bankNodeList!!.add(BankCardItemHeader("以下银行，免输卡号").apply { itemType = ADD_BANK_CARD_HEADER})
                    data.bankList.take(data.showBankCount).forEach {
                        data.bankNodeList!!.add(it.apply { itemType = ADD_BANK_CARD_TYPE})
                    }
                    if (data.bankList.size > data.showBankCount) {
                        //银行卡的数量多与需要展示的数量才展示"查看全部xx家银行"
                        data.bankNodeList!!.add(BankCardItemFooter("", data.bankList.size).apply { itemType = ADD_BANK_CARD_FOOTER})
                    }
                    data.bankNodeList!!.add(BankCardItemInputNum("输入卡号添加").apply { itemType = ADD_BANK_CARD_INPUT_NUM})
                }
            }
            _signBankListLiveData.postValue(queryOneKeySignBankList)
        }
    }

    /**
     * 通过头部切换
     */
    fun switchByExpandHeader() {
        isHeaderExpand = true
        isActiveEdit = false
        signBankBeanData.bankNodeList = mutableListOf()
        if (signBankBeanData.bankList?.isNotEmpty() == true) {
            signBankBeanData.bankNodeList!!.add(BankCardItemHeader("以下银行，免输卡号", true).apply { itemType = ADD_BANK_CARD_HEADER})
            signBankBeanData.bankList!!.take(
                if (isFooterExpand) {
                    signBankBeanData.bankList!!.size
                } else {
                    signBankBeanData.showBankCount
                }
            ).forEach {
                signBankBeanData.bankNodeList!!.add(it.apply { itemType = ADD_BANK_CARD_TYPE})
            }
            if (!isFooterExpand) {
                signBankBeanData.bankNodeList!!.add(BankCardItemFooter("", signBankBeanData.bankList!!.size).apply { itemType = ADD_BANK_CARD_FOOTER})
            }
            signBankBeanData.bankNodeList!!.add(BankCardItemInputNum("输入卡号添加", false).apply { itemType = ADD_BANK_CARD_INPUT_NUM})
        }
        val queryOneKeySignBankList = _signBankListLiveData.value
        queryOneKeySignBankList?.data = signBankBeanData
        _signBankListLiveData.postValue(queryOneKeySignBankList!!)
    }

    /**
     * 通过底部切换
     */
    fun switchByExpandFooter() {
        isFooterExpand = true
        signBankBeanData.bankNodeList = mutableListOf()
        if (signBankBeanData.bankList?.isNotEmpty() == true) {
            signBankBeanData.bankNodeList!!.add(BankCardItemHeader("以下银行，免输卡号").apply { itemType = ADD_BANK_CARD_HEADER})
            signBankBeanData.bankList!!.take(
                signBankBeanData.bankList!!.size
            ).forEach {
                signBankBeanData.bankNodeList!!.add(it.apply { itemType = ADD_BANK_CARD_TYPE})
            }
            signBankBeanData.bankNodeList!!.add(BankCardItemInputNum("输入卡号添加", false).apply { itemType = ADD_BANK_CARD_INPUT_NUM})
        }
        val queryOneKeySignBankList = _signBankListLiveData.value
        queryOneKeySignBankList?.data = signBankBeanData
        _signBankListLiveData.postValue(queryOneKeySignBankList!!)
    }

    /**
     * 通过激活输入框切换
     */
    fun switchByActiveEdit() {
        isHeaderExpand = false
        isActiveEdit = true
        signBankBeanData.bankNodeList = mutableListOf()
        if (signBankBeanData.bankList?.isNotEmpty() == true) {
            signBankBeanData.bankNodeList!!.add(BankCardItemHeader("以下银行，免输卡号", false).apply { itemType = ADD_BANK_CARD_HEADER})
            signBankBeanData.bankNodeList!!.add(BankCardItemInputNum("输入卡号添加", true).apply { itemType = ADD_BANK_CARD_INPUT_NUM})
        }
        val queryOneKeySignBankList = _signBankListLiveData.value
        queryOneKeySignBankList?.data = signBankBeanData
        _signBankListLiveData.postValue(queryOneKeySignBankList!!)
    }

    /**
     * 根据卡号校验是否已经绑卡(重复绑定)
     */
    fun checkCardBindState(cardNo: String) {
        viewModelScope.launch {
            showLoading()
            val checkCardBindState = JDPayRequest().checkCardBindState(cardNo)
            checkCardBindState.data?.cardNo = cardNo
            _cardBindLiveData.postValue(checkCardBindState)
        }
    }

    /**
     * 点击一键签约银行卡
     */
    fun payTypeItemClick(routerUrl: String) {
        val dialogTips = _signBankListLiveData.value?.data?.dialogTips
        _payTypeItemClickLiveData.postValue(Pair(dialogTips, routerUrl))
    }

    /**
     * 点击输入框
     */
    fun payTypeInputNumClick() {
        val dialogTips = _signBankListLiveData.value?.data?.dialogTips
        _payTypeItemClickLiveData.postValue(Pair(dialogTips, null))
    }

    /**
     * 按卡号绑卡确认
     */
    fun queryBankCardInfo(cardNo: String) {
        viewModelScope.launch {
            val queryBankCardInfo = JDPayRequest().queryBankCardInfo(cardNo)
            if (queryBankCardInfo.isSuccess && queryBankCardInfo.data != null) {
                queryBankCardInfo.data.bankCardNo = cardNo
            }
            _bankCardInfoLiveData.postValue(queryBankCardInfo)
        }
    }
}