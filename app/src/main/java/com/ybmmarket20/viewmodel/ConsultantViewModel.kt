package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.ConsultantBean
import com.ybmmarket20.bean.OrderDetailProductListData
import com.ybmmarket20.network.request.ConsultantServiceRequest
import com.ybmmarket20.network.request.OrderDetailProductRequest
import kotlinx.coroutines.launch

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2024/10/28 14:39
 *    desc   :
 */
class ConsultantViewModel(val app: Application) : AndroidViewModel(app) {
    private val _onOrderProductLiveData = MutableLiveData<BaseBean<ConsultantBean>>()
    val onOrderProductLiveData: LiveData<BaseBean<ConsultantBean>> =
        _onOrderProductLiveData

    /**
     * 查询专属顾问
     */
    fun getSalesInfo() {
        viewModelScope.launch {
            val orderProduct = ConsultantServiceRequest().getSalesInfo()
            _onOrderProductLiveData.postValue(orderProduct)
        }
    }
}