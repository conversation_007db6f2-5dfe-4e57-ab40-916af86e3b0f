package com.ybmmarket20.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.RefundReason
import com.ybmmarket20.network.NetworkService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 退款原因
 */
class RefundReasonViewModel: ViewModel() {

    val refundReasonLiveData = MutableLiveData<MutableList<RefundReason>>()

    /**
     * 获取退款原因列表
     */
    fun getRefundReasonList(orderNo: String, merchantId: String, refundOrderDetailList: String?) {
        viewModelScope.launch(Dispatchers.IO) {
            val refundReasonList = NetworkService.instance.getRefundReasonList(orderNo, merchantId, refundOrderDetailList?: "")
            refundReasonLiveData.postValue(refundReasonList.data?: mutableListOf())
        }
    }

}