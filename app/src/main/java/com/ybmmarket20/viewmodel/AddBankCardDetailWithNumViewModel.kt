package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.*
import com.google.gson.Gson
import com.ybmmarket20.bean.*
import com.ybmmarket20.db.AccountTable
import com.ybmmarket20.network.request.JDPayRequest
import com.ybmmarket20.utils.SpUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class AddBankCardDetailWithNumViewModel(val app: Application): AndroidViewModel(app) {

    private val _bankCardInfoLiveData = MutableLiveData<BaseBean<BankCardInfo>>()
    val bankCardInfoLiveData: LiveData<BaseBean<BankCardInfo>> = _bankCardInfoLiveData

    private val _identityInfoLiveData = MutableLiveData<BaseBean<IdentityInfo>>()
    val identityInfoLiveData: LiveData<BaseBean<IdentityInfo>> = _identityInfoLiveData

    private val _applyBindCardWithNumLiveData = MutableLiveData<BaseBean<BindCardInfo>>()
    val applyBindCardWithNumLiveData: LiveData<BaseBean<BindCardInfo>> = _applyBindCardWithNumLiveData

    /**
     * 按卡号绑卡确认
     */
    fun queryBankCardInfo(cardNo: String) {
        viewModelScope.launch {
            val queryBankCardInfo = JDPayRequest().queryBankCardInfo(cardNo)
            _bankCardInfoLiveData.postValue(queryBankCardInfo)
        }
    }

    /**
     * 查询实名认证信息
     */
    fun queryIdentityInfo() {
        viewModelScope.launch {
            val queryIdentityInfo = JDPayRequest().queryIdentityInfo()
            _identityInfoLiveData.postValue(queryIdentityInfo)
        }
    }

    /**
     * （按卡号）提交绑卡申请
     */
    fun applyBindCardWithNum(params: Map<String, String>, bindCardNum: String) {
        viewModelScope.launch {
            val applyBindCardWithNum = JDPayRequest().applyBindCardWithNum(params)
            val bindCardInfo = Gson().fromJson(Gson().toJson(params), BindCardInfo::class.java)
            if (applyBindCardWithNum.isSuccess) {
                bindCardInfo.contractNo = applyBindCardWithNum.data.contractNo
                bindCardInfo.cardNum = bindCardNum
                _applyBindCardWithNumLiveData.postValue(BaseBean.newSuccessBaseBean(bindCardInfo))
            } else {
                _applyBindCardWithNumLiveData.postValue(BaseBean.newFailureBaseBean(BindCardInfo()))
            }
        }
    }
}