package com.ybmmarket20.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.ServiceType
import com.ybmmarket20.network.NetworkService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 退款服务类型
 */
class SelectServiceTypeViewModel: ViewModel() {

    val selectServiceTypeLiveData = MutableLiveData<MutableList<ServiceType>>()

    fun getServiceType(orderNo: String?) {
        viewModelScope.launch(Dispatchers.IO) {
            val serviceTypeList = NetworkService.instance.getServiceType(orderNo?: "")
            selectServiceTypeLiveData.postValue(serviceTypeList.data?: mutableListOf())
        }
    }
}