package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.IdentityInfo
import com.ybmmarket20.bean.LoginAgreementBean
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.request.JDPayRequest
import com.ybmmarket20.utils.RoutersUtils
import kotlinx.coroutines.launch
import okhttp3.Call
import okhttp3.Callback
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import java.io.IOException
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

class ElsePageViewModel(app: Application) : BaseViewModel(app) {

    private val _identityInfoLiveData = MutableLiveData<BaseBean<IdentityInfo>>()
    val identityInfoLiveData: LiveData<BaseBean<IdentityInfo>> = _identityInfoLiveData

    /**
     * 查询实名认证信息
     */
    fun queryIdentityInfo() {
        viewModelScope.launch {
            val queryIdentityInfo = JDPayRequest().queryIdentityInfo()
            _identityInfoLiveData.postValue(queryIdentityInfo)
        }
    }

    /**
     * 获取登录协议信息
     * type 1:隐私协议 2:用户协议 0 初始化时获取url id
     */
    fun getLoginAgreement(type: Int) {
        viewModelScope.launch {
            RoutersUtils.open(getPrivacyPolicy(type))
        }
    }

    private suspend fun getPrivacyPolicy(type: Int) = suspendCoroutine { uCont ->
        val client = OkHttpClient().newBuilder().build()
        val request = Request.Builder().url("${AppNetConfig.getApiHost()}app/loginAgreement/getLoginAgreement").apply {
            addHeader("terminalType", "1")
        }.get().build()

        client.newCall(request).enqueue(object: Callback{
            override fun onFailure(call: Call, e: IOException) {
                handlePrivacyPolicyResult(type, false).let(uCont::resume)
            }

            override fun onResponse(call: Call, response: Response) {
                val resultStr = response.body?.string()
                if (response.code != 200 || resultStr.isNullOrEmpty()) handlePrivacyPolicyResult(type, false)
                val result = Gson().fromJson<BaseBean<LoginAgreementBean>>(resultStr,
                    object : TypeToken<BaseBean<LoginAgreementBean>>() {}.type)
                if (result.isSuccess) {
                    handlePrivacyPolicyResult(type, true, if (type == 1) result.data.privacyPolicy.url else result.data.userServiceAgreement.url)
                } else {
                    handlePrivacyPolicyResult(type, false)
                }.let(uCont::resume)
            }
        })
    }

    fun handlePrivacyPolicyResult(type: Int, isSuccess: Boolean, successRouter: String? = null): String {
        val router = if (isSuccess) {
            successRouter
        } else {
            if(type == 1) {
                AppNetConfig.PRIVACE
            } else {
                AppNetConfig.CLAUSE
            }
        }
        return "ybmpage://commonwebviewactivity?url=$router"
    }
}