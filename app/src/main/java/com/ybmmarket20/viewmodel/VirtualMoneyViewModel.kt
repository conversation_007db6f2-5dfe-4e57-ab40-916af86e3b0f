package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.ShoppingGoldRechargeBean
import com.ybmmarket20.bean.VirtualMoney
import com.ybmmarket20.bean.VirtualMoneyData
import com.ybmmarket20.network.request.Mine2CommonToolsRequest
import com.ybmmarket20.network.request.VirtualMoneyRequest
import com.ybmmarket20.utils.SpUtil
import kotlinx.coroutines.launch

/**
 * 我的购物金
 */
class VirtualMoneyViewModel: ViewModel() {

    private val _virtualMoneyListLiveData: MutableLiveData<BaseBean<VirtualMoneyData>> = MutableLiveData()
    val virtualMoneyListLiveData: LiveData<BaseBean<VirtualMoneyData>> = _virtualMoneyListLiveData

    private val _virtualMoneyLiveData: MutableLiveData<BaseBean<VirtualMoney>> = MutableLiveData()
    val virtualMoneyLiveData = _virtualMoneyLiveData

    //购物金充值
    private val _shoppingGoldRechargeBeanData: MutableLiveData<BaseBean<ShoppingGoldRechargeBean>> = MutableLiveData()
    val shoppingGoldRechargeBeanData: LiveData<BaseBean<ShoppingGoldRechargeBean>> = _shoppingGoldRechargeBeanData

    fun getVirtualMoneyList(offset: String, limit: String, virtualGoldType: String) {
        viewModelScope.launch {
            val result = VirtualMoneyRequest().getMyVirtualMoneyList(offset, limit, virtualGoldType)
            _virtualMoneyListLiveData.postValue(result)
        }
    }

    fun getVirtualMoney() {
        viewModelScope.launch {
            val result = VirtualMoneyRequest().getMyVirtualMoney(SpUtil.getMerchantid())
            _virtualMoneyLiveData.postValue(result)
        }
    }

    fun getShoppingGoldRechargeBean() {
        viewModelScope.launch {
            val shoppingGoldRechargeBean = Mine2CommonToolsRequest().getShoppingGoldRechargeBean(SpUtil.getMerchantid())
            _shoppingGoldRechargeBeanData.postValue(shoppingGoldRechargeBean)
        }
    }
}