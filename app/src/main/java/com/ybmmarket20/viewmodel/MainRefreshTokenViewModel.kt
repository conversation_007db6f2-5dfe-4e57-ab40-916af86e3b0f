package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.viewModelScope
import com.tencent.tddiag.logger.TDLog
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.network.request.AccountInfoRequest
import com.ybmmarket20.network.request.MainRefreshTokenRequest
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.WxCodeSaveUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2025/1/9 15:24
 *    desc   :
 */
class MainRefreshTokenViewModel(val app: Application) : BaseViewModel(app) {
    //刷新 token
    fun refreshToken() {
        //如果已经是最新 token，就不用走刷新 token 接口了
        if (SpUtil.getToken().length > 100 && SpUtil.getToken().contains("eyJh")) {
            return
        }
        viewModelScope.launch(Dispatchers.IO) {
            val refreshToken = MainRefreshTokenRequest().refreshToken()
            refreshToken.data?.token?.let {
                SpUtil.setToken(it)
            }
        }
    }

    /**
     * 绑定微信
     */
    fun bindWechatCustomer() {
        val value = WxCodeSaveUtil.getInstance(app).getValue()
        if (value.isNotEmpty()) {
            viewModelScope.launch {
                val bindValue = AccountInfoRequest().bindWechatCustomer(value)
                if (bindValue.isSuccess) {
                    WxCodeSaveUtil.getInstance(app).saveValue("")
                    ToastUtils.showShort("绑定成功")
                }
            }
        }
    }
}