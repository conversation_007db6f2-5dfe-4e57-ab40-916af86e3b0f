package com.ybmmarket20.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.SearchResultBean
import com.ybmmarket20.network.request.SameGoodsForShopRequest
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @date 2022/4/15
 * @description 店铺同款商品
 */
class SearchSameGoodsForShopViewModel: ViewModel() {

    private val _liveDataViewModel = MutableLiveData<SearchResultBean>()
    val liveDataViewModel = _liveDataViewModel.map { it }

    /**
     * 获取店铺同款商品
     */
    fun getSameGoodsForShopData(paramsMap: Map<String, String>) {
        viewModelScope.launch {
            val result = SameGoodsForShopRequest().getSameGoodsForShopData(paramsMap)
            _liveDataViewModel.postValue(result.data)
        }
    }

}