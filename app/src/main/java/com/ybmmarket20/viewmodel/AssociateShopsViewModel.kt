package com.ybmmarket20.viewmodel

import android.app.Application
import android.os.Handler
import android.os.Looper
import android.os.Message
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.ybmmarket20.bean.*
import com.ybmmarket20.network.request.AssociateShopRequest
import com.ybmmarket20.network.request.AssociatedShopsRequest
import com.ybmmarket20.utils.SpUtil
import kotlinx.coroutines.launch

/**
 * 查询已关联的店铺
 */
class AssociateShopsViewModel(appLike: Application) : BaseViewModel(appLike) {
    //查询已关联的店铺
    private val _queryAssociateShopsLiveData = MutableLiveData<BaseBean<SelectLoginShopInfoWithPage>>()
    val queryAssociateShopsLiveData: LiveData<BaseBean<SelectLoginShopInfoWithPage>> = _queryAssociateShopsLiveData
    //状态切换
    private val _switchStatusLiveData = MutableLiveData<BaseBean<SelectLoginShopInfoWithPage>>()
    val switchStatusLiveData: LiveData<BaseBean<SelectLoginShopInfoWithPage>> = _switchStatusLiveData
    //取消关联店铺
    private val _cancelAssociatedShopLiveData = MutableLiveData<BaseBean<CancelAssociatedPositionBean>>()
    val cancelAssociatedShopLiveData: LiveData<BaseBean<CancelAssociatedPositionBean>> = _cancelAssociatedShopLiveData
    //获取店铺信息
    private val _getAddShopInfoLiveData = MutableLiveData<BaseBean<AddShopInfoBean>>()
    val getAddShopInfoLiveData: LiveData<BaseBean<AddShopInfoBean>> = _getAddShopInfoLiveData
    //获取店铺信息
    private val _checkOctLiveData = MutableLiveData<BaseBean<CheckOcrStatusBean>>()
    val checkOctLiveData: LiveData<BaseBean<CheckOcrStatusBean>> = _checkOctLiveData
    //审核通过
    private val _checkSubmitStatusLiveData = MutableLiveData<BaseBean<RegisterAddShopBean>>()
    val checkSubmitStatusLiveData: LiveData<BaseBean<RegisterAddShopBean>> = _checkSubmitStatusLiveData
    var pageNo: Int = 1
    var list: MutableList<SelectLoginShopInfo> = mutableListOf()
    //ocr识别信息
    private var mSubmitOcrResult: CheckOcrStatusBean? = null
    //工商信息
    private var mAddShopInfoBean: AddShopInfoBean? = null

    /**
     * 查询已关联的店铺
     */
    fun queryAssociatedShops() {
        viewModelScope.launch {
            val queryAssociatedShops = AssociatedShopsRequest().queryAssociatedShops("$pageNo", "20")
            if (queryAssociatedShops.isSuccess) {
                queryAssociatedShops.data.pageNo = pageNo
                if (!queryAssociatedShops.data.isEnd()) pageNo++
                val shopList = queryAssociatedShops.data.list
                shopList?.forEach {
                    val shopState = SelectLoginShopState.getLoginShopState(
                        it.merchantStatus,
                        it.associateStatus,
                        it.merchantId?: "",
                        it.merchantAvailableStatus
                    )
                    it.tagBean = shopState?.getTagBean()
                    it.routerUrl = shopState?.getRouterUrl()
                    if (it.merchantId == SpUtil.getMerchantid()) it.editStatus = 1
                }
                shopList?.let { list.addAll(it) }
            }
            _queryAssociateShopsLiveData.postValue(queryAssociatedShops)
        }
    }

    /**
     * 切换到编辑状态
     */
    fun switchStatusToEdit() {
        switchStatus(2)
    }

    /**
     * 切换到普通状态
     */
    fun switchStatusToNormal() {
        switchStatus(0)
    }

    /**
     * 切换到选中状态
     */
    fun switchStatusToSelected(position: Int) {
        switchStatus(1, position)
    }

    /**
     * 切换状态
     * @param status 0:普通状态 1：选中状态 2：编辑状态
     * @param position 更新位置 -1：全部
     */
    private fun switchStatus(status: Int, position: Int = -1) {
        val shopInfo = _queryAssociateShopsLiveData.value
        list.forEachIndexed {index, item ->
            if (status == 0) {
                //普通状态
                if (item.merchantId == SpUtil.getMerchantid()) {
                    item.editStatus = 1
                } else item.editStatus = 0
            } else if (status == 1) {
                //选中状态
                if (index == position){
                    item.editStatus = 1
                } else item.editStatus = 0
            } else if (status == 2) {
                //编辑状态
                item.editStatus = 2
            }
        }
        shopInfo?.let(_switchStatusLiveData::postValue)
    }

    /**
     * 选择登录店铺状态
     */
    sealed class SelectLoginShopState {

        /**
         * 获取标签样式
         */
        open fun getTagBean(): TagBean? = null

        /**
         * 获取路由
         */
        open fun getRouterUrl(): String? = null

        companion object {

            /**
             * 获取当前状态
             */
            fun getLoginShopState(merchantStatus: Int, associateStatus: Int, merchantId: String, merchantAvailableStatus: Int = 0): SelectLoginShopState? =
                if (merchantAvailableStatus == 3) {
                    //冻结状态
                    FreezeState
                } else if (associateStatus == 1) {
                    //待提审
                    PendingProcess(merchantId)
                } else if (associateStatus == 4 || associateStatus == 5 || merchantStatus == 1) {
                    //审核中
                    ProcessingState(merchantStatus, associateStatus, merchantId)
                } else if (associateStatus == 3 || merchantStatus == 3) {
                    //审核未通过
                    UnPassedState(merchantStatus, associateStatus, merchantId)
                } else if (associateStatus == 2 && merchantStatus == 2) {
                    //审核通过
                    PassedState
                } else null
        }

        object FreezeState : SelectLoginShopState() {
            override fun getTagBean(): TagBean = TagBean().apply {
                text = "已冻结"
                bgColor = "#0DFE2021"
                textColor = "#FFFE2021"
                borderColor = "#73FE2021"
            }
        }

        //审核通过
        object PassedState: SelectLoginShopState() {
            override fun getRouterUrl(): String {
                return "ybmpage://main"
            }
        }

        //审核未通过
        class UnPassedState(private val merchantStatus: Int, private val associateStatus: Int, val merchantId: String) :
            SelectLoginShopState() {
            override fun getTagBean(): TagBean = TagBean().apply {
                text = "审核未通过"
                bgColor = "#0DFE2021"
                textColor = "#FFFE2021"
                borderColor = "#73FE2021"
            }

            override fun getRouterUrl(): String? = if (merchantStatus == 3) {
                //店铺审核不通过
                "ybmpage://linkshop"
            } else if (associateStatus == 3) {
                //凭据审核不通过
                "ybmpage://clerkaptitudeauthenticationactivity?merchant_id=$merchantId&status=1"
            } else null
        }

        //审核中
        class ProcessingState(private val merchantStatus: Int, private val associateStatus: Int, val merchantId: String) :
            SelectLoginShopState() {
            override fun getTagBean(): TagBean = TagBean().apply {
                text = if (merchantStatus == 1) "店铺信息审核中" else "资质审核中"
                bgColor = "#0D00B377"
                textColor = "#FF00B377"
                borderColor = "#8000B377"
            }

            override fun getRouterUrl(): String? = if (merchantStatus == 1) {
                //店铺审核中
                "ybmpage://shopinfoauthenticationprocessing?merchantId=$merchantId&status=2"
            } else if (associateStatus == 4 || associateStatus == 5) {
                "ybmpage://associatedshopauthenticationprocessing?merchantId=$merchantId&status=2"
            } else null
        }

        //待提审
        class PendingProcess(val merchantId: String) :
            SelectLoginShopState() {
            override fun getTagBean(): TagBean = TagBean().apply {
                text = "待提审"
                bgColor = "#0DFF8C1A"
                textColor = "#FFFF8C19"
                borderColor = "#80FF8C1A"
            }

            override fun getRouterUrl(): String = "ybmpage://clerkaptitudeauthenticationactivity?merchant_id=$merchantId&status=0"
        }
    }

    /**
     * 取消关联店铺
     */
    fun cancelAssociateMerchant(merchantId: String, position: Int) {
        viewModelScope.launch {
            val cancelAssociateMerchant = AssociatedShopsRequest().cancelAssociateMerchant(merchantId)
            cancelAssociateMerchant.data = CancelAssociatedPositionBean(position)
            _cancelAssociatedShopLiveData.postValue(cancelAssociateMerchant)
        }
    }

    /**
     * 获取添加店铺的店铺信息
     */
    fun getAddShopInfo(params: Map<String, String>) {
        viewModelScope.launch {
            val addShopInfo = AssociatedShopsRequest().getAddShopInfo(params)
            if (addShopInfo.isSuccess) {
                mAddShopInfoBean = addShopInfo.data
            }
            _getAddShopInfoLiveData.postValue(addShopInfo)
        }
    }

    /**
     * 提交orc
     */
    fun submitOcr(params: Map<String, String>) {
        viewModelScope.launch {
            val submitOcrResult = AssociatedShopsRequest().submitOct(params)
            if (submitOcrResult.isSuccess && submitOcrResult.data != null && !submitOcrResult.data.dataKey.isNullOrEmpty()) {
                checkOcrStatus(submitOcrResult.data.dataKey!!, submitOcrResult.data.type)
            } else {
                dismissLoading()
            }
        }
    }

    //OCR检查的开始时间
    private var ocrCheckStartTime = 0L
    //OCR检查的最大时长
    private val ocrCheckTotalTime = 60 * 1_000

    private val ocrHandler = object: Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            val dataKey = (msg.obj?: "").toString()
            val type = msg.arg1
            viewModelScope.launch {
                val ocrStatus = AssociatedShopsRequest().checkOctStatus(dataKey, "$type")
                if (ocrStatus.isSuccess) {
                    if (ocrStatus.data.status == 2) {
                        //检查通过
                        dismissLoading()
                        mSubmitOcrResult = ocrStatus.data
                    } else if(ocrStatus.data.status == 3) {
                        //检查未完成，继续检查
                        checkOcrStatus(dataKey, type)
                    } else if(System.currentTimeMillis() - ocrCheckStartTime >= ocrCheckTotalTime) {
                        //检查超时
                        dismissLoading()
                    } else {
                        dismissLoading()
                    }
                } else {
                    dismissLoading()
                }
            }
        }
    }

    /**
     * 检查ocr状态
     */
    fun checkOcrStatus(dataKey: String, type: Int) {
        ocrCheckStartTime = System.currentTimeMillis()
        val msg = Message.obtain()
        msg.obj = dataKey
        msg.arg1 = type
        ocrHandler.sendMessageDelayed(msg, 3_000)
    }

    /**
     * 获取工商信息和ocr信息
     */
    fun getSubmitAndOcrInfo(): Pair<String?, String?> {
        var submitStr: String? = null
        var ocrStr: String? = null
        if (mSubmitOcrResult != null) {
            ocrStr = Gson().toJson(mSubmitOcrResult)
        }
        if (mAddShopInfoBean != null) {
            submitStr = Gson().toJson(mAddShopInfoBean)
        }
        return submitStr to ocrStr
    }

    //提交结果检查的开始时间
    private var submitCheckStartTime = 0L
    //提交结果检查的最大时长
    private val submitCheckTotalTime = 30 * 1_000

    private val submitHandler = object: Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            val msgObj = msg.obj
            viewModelScope.launch {
                val registerAddShopBean: RegisterAddShopBean = msgObj as RegisterAddShopBean
                val submitStatus = AssociatedShopsRequest().checkSubmitStatus(registerAddShopBean.merchantId?: "")
                if (submitStatus.isSuccess && submitStatus.data != null) {
                    if (submitStatus.data.auditStatus == 2) {
                        //审核通过
                        _checkSubmitStatusLiveData.postValue(BaseBean.newSuccessBaseBean(registerAddShopBean))
                    } else if(System.currentTimeMillis() - submitCheckStartTime >= submitCheckTotalTime) {
                        dismissLoading()
                        _checkSubmitStatusLiveData.postValue(BaseBean.newFailureBaseBean(registerAddShopBean))
                    } else {
                        checkSubmitStatus(registerAddShopBean)
                    }
                } else {
                    dismissLoading()
                }
            }
        }
    }

    /**
     * 检查提交信息
     */
    fun checkSubmitStatus(registerAddShopBean: RegisterAddShopBean) {
        submitCheckStartTime = System.currentTimeMillis()
        val msg = Message.obtain()
        msg.obj = registerAddShopBean
        submitHandler.sendMessageDelayed(msg, 2_000)
    }

    /**
     * 重置工商信息
     */
    fun resetShopInfoAutomatically(){
        mAddShopInfoBean = null
    }

    fun resetOCRInfoAutomatically() {
        mSubmitOcrResult = null
    }


}