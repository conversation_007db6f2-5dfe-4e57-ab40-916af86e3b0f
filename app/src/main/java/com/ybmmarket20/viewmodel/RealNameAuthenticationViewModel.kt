package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.network.request.RealNameAuthenticationRequest
import com.ybmmarket20.utils.SpUtil
import kotlinx.coroutines.launch

/**
 * 实名认证ViewModel
 */
class RealNameAuthenticationViewModel: ViewModel() {

    //手机验证码
    private val _phoneCodeLiveData = MutableLiveData<Pair<Boolean, String>>()
    val phoneCodeLiveData: LiveData<Pair<Boolean, String>> = _phoneCodeLiveData

    private val _submitLiveData = MutableLiveData<Pair<Boolean, String>>()
    val submitLiveData: LiveData<Pair<Boolean, String>> = _submitLiveData

    private val _phoneMobileLiveData = MutableLiveData<String>()
    val phoneMobileLiveData: LiveData<String> = _phoneMobileLiveData

    /**
     * 获取手机验证码
     */
    fun getPhoneCode() {
        viewModelScope.launch {
            val bean = RealNameAuthenticationRequest().sendClearIdentityVerifyCode()
            _phoneCodeLiveData.postValue(Pair(bean.isSuccess, bean.msg ?: ""))
        }
    }

    fun submitCancel(code: String) {
        viewModelScope.launch {
            val bean = RealNameAuthenticationRequest().clearAccountIdentity(code)
            if (bean.isSuccess) {
                _submitLiveData.postValue(Pair(true, "实名认证清除成功"))
            } else {
                _submitLiveData.postValue(Pair(false, bean.msg ?: ""))
            }
        }
    }

    fun getPhoneMobile() {
        viewModelScope.launch {
            val bean = RealNameAuthenticationRequest().getPhoneInfo()
            _phoneMobileLiveData.postValue(bean.data?.mobile?:SpUtil.getLoginPhone()?:"")
        }
    }

}