package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.*
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.IdentityInfo
import com.ybmmarket20.bean.OneKeySignApply
import com.ybmmarket20.db.AccountTable
import com.ybmmarket20.network.request.JDPayRequest
import com.ybmmarket20.utils.SpUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import retrofit2.http.Field

class AddBankCardDetailViewModel(val app: Application): AndroidViewModel(app) {

    private val _onKeySignApplyLiveData = MutableLiveData<BaseBean<OneKeySignApply>>()
    val onKeySignApplyLiveData: LiveData<BaseBean<OneKeySignApply>> = _onKeySignApplyLiveData

    private val _identityInfoLiveData = MutableLiveData<BaseBean<IdentityInfo>>()
    val identityInfoLiveData: LiveData<BaseBean<IdentityInfo>> = _identityInfoLiveData



    /**
     * 一键签约申请
     */
    fun oneKeySignApply(cardType: String, bankCode: String, bankName: String, idNo: String, idName: String) {
        viewModelScope.launch {
            val params = mutableMapOf(
                "cardType" to cardType,
                "bankCode" to bankCode,
                "bankName" to bankName,
            )
            if (!idNo.contains("*")) {
                params["idNo"] = idNo
                params["idName"] = idName
            }
            val oneKeySignApply = JDPayRequest().oneKeySignApply(params)
            _onKeySignApplyLiveData.postValue(oneKeySignApply)
        }
    }

    /**
     * 查询实名认证信息
     */
    fun queryIdentityInfo() {
        viewModelScope.launch {
            val queryIdentityInfo = JDPayRequest().queryIdentityInfo()
            _identityInfoLiveData.postValue(queryIdentityInfo)
        }
    }
}