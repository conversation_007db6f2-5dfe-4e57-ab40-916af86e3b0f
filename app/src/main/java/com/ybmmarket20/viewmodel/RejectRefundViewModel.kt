package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RejectRefundReasonWithState
import com.ybmmarket20.network.request.RejectRefundRequest
import kotlinx.coroutines.launch

class RejectRefundViewModel: ViewModel() {

    private val _rejectRefundLiveData = MutableLiveData<BaseBean<List<RejectRefundReasonWithState>>>()
    val rejectRefundLiveData: LiveData<BaseBean<List<RejectRefundReasonWithState>>> = _rejectRefundLiveData

    private val _auditOrderRefundLiveData = MutableLiveData<BaseBean<Nothing>>()
    val auditOrderRefundLiveData: LiveData<BaseBean<Nothing>> = _auditOrderRefundLiveData

    /**
     * 获取退款原因
     */
    fun getRejectRefundReason() {
        viewModelScope.launch {
            val result = RejectRefundRequest().getRejectRefundReason()
            if (!result.isSuccess) {
                BaseBean.newFailureBaseBean(emptyList<RejectRefundReasonWithState>()).also(_rejectRefundLiveData::postValue)
                return@launch
            }
            result.data.list.map {
                RejectRefundReasonWithState(it, false)
            }.let {
                BaseBean.newSuccessBaseBean(it)
            }.also(_rejectRefundLiveData::postValue)
        }
    }

    /**
     * 审核
     */
    fun auditOrderRefund(auditType: String, refundId: String, rejectReason: String = "", images: String = "", remarks: String = "") {
        viewModelScope.launch {
            val result = RejectRefundRequest().auditOrderRefund(auditType, refundId, rejectReason, images, remarks)
            _auditOrderRefundLiveData.postValue(result)
        }
    }
}