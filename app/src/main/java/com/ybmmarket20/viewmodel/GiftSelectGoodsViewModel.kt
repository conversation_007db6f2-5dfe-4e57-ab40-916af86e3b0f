package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.GiftSubTotalResponseBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SearchGiftSelectResponseBean
import com.ybmmarket20.bean.SearchResultBean
import com.ybmmarket20.bean.SearchRowsBean
import com.ybmmarket20.network.request.GiftSelectGoodsRequest
import com.ybmmarket20.utils.AdapterUtils.addLocalTimeForGiftSelect
import com.ybmmarket20.utils.AdapterUtils.addLocalTimeForRows
import kotlinx.coroutines.launch

/**
 * 赠品选择凑单页
 */
class GiftSelectGoodsViewModel(app: Application): BaseViewModel(app) {

    //当前获取商品列表的请求参数
    private var currGoodsListRequestParams = mutableMapOf<String, String>()

    //商品列表-追加参数
    private val _giftSelectGoodsListLiveData = MutableLiveData<BaseBean<SearchGiftSelectResponseBean>>()
    val giftSelectGoodsListLiveData: LiveData<BaseBean<SearchGiftSelectResponseBean>> = _giftSelectGoodsListLiveData

    //商品列表-加载更多
    private val _loadMoreGoodsListLiveData = MutableLiveData<BaseBean<SearchGiftSelectResponseBean>>()
    val loadMoreGoodsListLiveData: LiveData<BaseBean<SearchGiftSelectResponseBean>> = _loadMoreGoodsListLiveData

    private val _subTotalsLiveData = MutableLiveData<BaseBean<GiftSubTotalResponseBean>>()
    val subTotalsLiveData: LiveData<BaseBean<GiftSubTotalResponseBean>> = _subTotalsLiveData

    /**
     * 获取凑单页商品
     */
    fun getGoodsListByAppendParam(params: Map<String, String>,isFirstRequest:Boolean) {
        //追加参数
        params.forEach {
            currGoodsListRequestParams[it.key] = it.value
        }
//        currGoodsListRequestParams = currGoodsListRequestParams.filter {
//            //除key=shopCodes外,value=""不传参
//            it.value.isNotEmpty() || it.key == "shopCodes"
//        }.toMutableMap()
        //重新请求需要从第一页开始(默认不传)
        if (currGoodsListRequestParams.containsKey("pageNum")) currGoodsListRequestParams.remove("pageNum")
        if (currGoodsListRequestParams.containsKey("pageSize")) currGoodsListRequestParams.remove("pageSize")
        if (isFirstRequest) {
            currGoodsListRequestParams["isFirstRequest"] = "true"
        } else if(currGoodsListRequestParams.containsKey("isFirstRequest")) {
            currGoodsListRequestParams.remove("isFirstRequest")
        }
        getGoodsList(currGoodsListRequestParams, _giftSelectGoodsListLiveData)
    }

    /**
     * 获取商品列表-加载更多
     */
    fun getGoodsListByLoadMore() {
        getGoodsList(currGoodsListRequestParams, _loadMoreGoodsListLiveData)
    }

    /**
     * 获取商品列表
     */
    private fun getGoodsList(params: MutableMap<String, String>, liveData: MutableLiveData<BaseBean<SearchGiftSelectResponseBean>>) {
        viewModelScope.launch {
            val goodsList = GiftSelectGoodsRequest().getGiftGoodsList(params)
            if (goodsList.isSuccess && goodsList.data.requestParams != null) {
                currGoodsListRequestParams = goodsList.data.requestMap
            }
            //设置拼团 秒杀时间
            if (goodsList.isSuccess){
                addLocalTimeForGiftSelect<SearchRowsBean>(goodsList.data?.rows)
            }
            liveData.postValue(goodsList)
        }
    }

     fun getSubTotal(params: MutableMap<String, String>) {
        viewModelScope.launch {
            val subTotal = GiftSelectGoodsRequest().getGiftSubTotal(params)
            _subTotalsLiveData.postValue(subTotal)
        }
    }
}