package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.payment.PaymentConsumeRebateDetailBean
import com.ybmmarket20.network.request.RebateRatioRequest
import kotlinx.coroutines.launch

/**
 * 提单页消费返弹窗
 */
class RebateRatioModel: ViewModel() {

    private val _rebateRatioLiveData = MutableLiveData<BaseBean<PaymentConsumeRebateDetailBean>>()
    val rebateRatioLiveData: LiveData<BaseBean<PaymentConsumeRebateDetailBean>> = _rebateRatioLiveData

    fun getRebateRatioData(money: Number) {
        viewModelScope.launch {
            val rebateRatioLiveInfo = RebateRatioRequest().getRebateRatioData(money)
            _rebateRatioLiveData.postValue(rebateRatioLiveInfo)
        }
    }
}