package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.payment.RebateWeChatBindingBean
import com.ybmmarket20.network.request.RebateWeChatBindingRequest
import kotlinx.coroutines.launch

/**
 * 提单页消费返弹窗
 */
class RebateWeChatBindingModel: ViewModel() {

    private val _rebateWechatBindingLiveData = MutableLiveData<BaseBean<RebateWeChatBindingBean>>()
    val rebateWechatBindingLiveData: LiveData<BaseBean<RebateWeChatBindingBean>> = _rebateWechatBindingLiveData

    fun getRebateWechatBindingLiveData() {
        viewModelScope.launch {
            val rebateWechatBindingInfo = RebateWeChatBindingRequest().getRebateWeChatBindingData()
            _rebateWechatBindingLiveData.postValue(rebateWechatBindingInfo)
        }
    }
}