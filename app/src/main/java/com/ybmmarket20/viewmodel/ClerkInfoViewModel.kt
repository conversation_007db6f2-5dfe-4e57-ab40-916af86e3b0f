package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.ClerkInfoWithPage
import com.ybmmarket20.network.request.ClerkInfoRequest
import kotlinx.coroutines.launch

/**
 * 店员信息
 */

const val PAGE_SIZE = 20
class ClerkInfoViewModel: ViewModel() {

    private val _clerkInfoListLiveData = MutableLiveData<BaseBean<ClerkInfoWithPage>>()
    val clerkInfoListLiveData: LiveData<BaseBean<ClerkInfoWithPage>> = _clerkInfoListLiveData
    private var pageNo = 1

    fun getClerkInfoList(merchantId: String) {
        viewModelScope.launch {
            val clerkInfoList = ClerkInfoRequest().getClerkInfoList(merchantId, "$pageNo", "$PAGE_SIZE")
            if (clerkInfoList.isSuccess && clerkInfoList.data.list?.isNotEmpty() == true && pageNo != PAGE_SIZE) {
                clerkInfoList.data.pageNo = pageNo
                pageNo ++
            }
            _clerkInfoListLiveData.postValue(clerkInfoList)
        }
    }
}