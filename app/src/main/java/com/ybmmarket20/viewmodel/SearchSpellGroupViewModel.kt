package com.ybmmarket20.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.SearchSpellGroup
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.network.request.SearchSpellGroupRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 大搜获取拼团数据
 */
class SearchSpellGroupViewModel: ViewModel() {
    val searchSpellGroupLiveData = MutableLiveData<SearchSpellGroup>()

    fun getSearchSpellGroupData() = viewModelScope.launch(Dispatchers.IO) {
        val searchSpellGroupData = SearchSpellGroupRequest().getSearchSpellGroupData()
        searchSpellGroupData?.data?.let(searchSpellGroupLiveData::postValue)
    }
}