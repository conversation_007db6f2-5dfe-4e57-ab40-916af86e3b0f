package com.ybmmarket20.viewmodel

import android.app.Application
import android.text.TextUtils
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.activity.afterSales.activity.TIPS_TYPE_INVOICE
import com.ybmmarket20.activity.afterSales.activity.TIPS_TYPE_LICENSE
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CheckOrderDetailBean
import com.ybmmarket20.bean.PdfUrlBean
import com.ybmmarket20.bean.aftersales.AfterSalesLicenseGoods
import com.ybmmarket20.network.request.OrderDetailRequest
import com.ybmmarket20.utils.RoutersUtils
import kotlinx.coroutines.launch

/**
 * 订单详情
 */
class OrderDetailViewModel(appLike: Application) : BaseViewModel(appLike) {

    private val _formLiveData = MutableLiveData<BaseBean<MutableList<PdfUrlBean>>>()
    val formLiveData: LiveData<BaseBean<MutableList<PdfUrlBean>>> = _formLiveData

    private val _afterSaleTipsLiveData = MutableLiveData<String>()
    val afterSaleTipsLiveData: LiveData<String> = _afterSaleTipsLiveData

    /**
     * 获取仓库交接单和获取电子出库单pdfUrl
     */
    fun getFormPdfUrl(orderNo: String?, formType: String?, branchCode: String?) {
        viewModelScope.launch {
            val result: BaseBean<MutableList<PdfUrlBean>> = if (TextUtils.isEmpty(orderNo) || TextUtils.isEmpty(formType) || TextUtils.isEmpty(branchCode)) {
                BaseBean.newFailureBaseBean(mutableListOf(PdfUrlBean("", 0)))
            } else {
                OrderDetailRequest().getFormPdfUrl(orderNo!!, formType!!, branchCode!!).let {
                    if (it.isSuccess && it.data != null) {
                        val list = it.data.map { pdfUrl ->
                            PdfUrlBean(pdfUrl, formType.toInt())
                        }.toMutableList()
                        val beanBaseBean = BaseBean<MutableList<PdfUrlBean>>()
                        beanBaseBean.data = list
                        beanBaseBean.status = "success"
                        beanBaseBean
                    } else BaseBean.newFailureBaseBean(mutableListOf(PdfUrlBean("", 0)))
                }
            }
            _formLiveData.postValue(result)
        }
    }

    fun getAfterSalesInfo(orderDetailBean: CheckOrderDetailBean, tipsType: Int, tipsTitle: String = "") {
        viewModelScope.launch {
            showLoading()
            val afterSalesTips = OrderDetailRequest().getAfterSalesTips(orderDetailBean.orderNo, orderDetailBean.orgId)
            if (afterSalesTips.isSuccess) {
                val tipsContent = afterSalesTips.data
                val routerStr = if (!tipsContent.isNullOrEmpty()) {
                    //须知页面路由
                    "ybmpage://companyaftersalestips?orderNo=${orderDetailBean.orderNo}&orgName=${orderDetailBean.origName}&contentTips=${RoutersUtils.paramsToBase64(tipsContent)}&tipsType=$tipsType"
                } else if (tipsType == TIPS_TYPE_INVOICE) {
                    //发票相关售后路由
                    "ybmpage://invoiceaftersalesservice?orderNo=${orderDetailBean.orderNo}"
                } else if (tipsType == TIPS_TYPE_LICENSE) {
                    //选择需要补发的资质路由
                    "ybmpage://licenseaftersalesservice?orderNo=${orderDetailBean.orderNo}&orgName=${orderDetailBean.origName}"
                } else {
                    ""
                }
                _afterSaleTipsLiveData.postValue(routerStr)
            }
            dismissLoading()
        }
    }
    fun getAfterSalesInfo(orderDetailBean: CheckOrderDetailBean, tipsType: Int,applyInvoiceHtmlUrl:String?="",applyInvoiceAfterSaleHtmlUrl:String?="") {
        viewModelScope.launch {
            showLoading()
            val afterSalesTips = OrderDetailRequest().getAfterSalesTips(orderDetailBean.orderNo, orderDetailBean.orgId)
            if (afterSalesTips.isSuccess) {
                val tipsContent = afterSalesTips.data
                val routerStr = if (!tipsContent.isNullOrEmpty()) {
                    //须知页面路由
                    if (applyInvoiceHtmlUrl.isNullOrEmpty()){
                        "ybmpage://companyaftersalestips?orderNo=${orderDetailBean.orderNo}&orgName=${orderDetailBean.origName}&contentTips=${RoutersUtils.paramsToBase64(tipsContent)}&tipsType=$tipsType"
                    }else{
                        "ybmpage://commonh5activity?url=$applyInvoiceHtmlUrl&isShowCart=0"
                    }
                } else if (tipsType == TIPS_TYPE_INVOICE) {
                    //发票相关售后路由
                    if (applyInvoiceHtmlUrl.isNullOrEmpty()){
                        "ybmpage://invoiceaftersalesservice?orderNo=${orderDetailBean.orderNo}"
                    }else{
                        "ybmpage://commonh5activity?url=$applyInvoiceAfterSaleHtmlUrl&isShowCart=0"
                    }
                } else if (tipsType == TIPS_TYPE_LICENSE) {
                    //选择需要补发的资质路由
                    "ybmpage://licenseaftersalesservice?orderNo=${orderDetailBean.orderNo}&orgName=${orderDetailBean.origName}"
                } else {
                    ""
                }
                _afterSaleTipsLiveData.postValue(routerStr)
            }
            dismissLoading()
        }
    }

    private fun getGoodsListFromOrderDetail(orderDetailBean: CheckOrderDetailBean): MutableList<AfterSalesLicenseGoods> {
        return orderDetailBean.detailList?.map {
            AfterSalesLicenseGoods("${it.skuId}", it.productName, it.spec, it.imageUrl,
                checkReportState = false,
                licenseState = false
            )
        }?.toMutableList()?: mutableListOf()
    }

}