package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.BindCardWithNumInfo
import com.ybmmarket20.bean.VerifyCodeBean
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.db.AccountTable
import com.ybmmarket20.network.request.JDPayRequest
import com.ybmmarket20.utils.SpUtil
import kotlinx.coroutines.launch

class CheckReservePhoneNumViewModel(app: Application): BaseViewModel(app) {

    private val _applyBindCardWithNumLiveData = MutableLiveData<BaseBean<BindCardWithNumInfo>>()
    val applyBindCardWithNumLiveData: LiveData<BaseBean<BindCardWithNumInfo>> = _applyBindCardWithNumLiveData

    private val _confirmBindCardWithNumLiveData = MutableLiveData<BaseBean<Any>>()
    val confirmBindCardWithNumLiveData: LiveData<BaseBean<Any>> = _confirmBindCardWithNumLiveData

    private val _verifyCodeLiveData = MutableLiveData<BaseBean<VerifyCodeBean>>()
    val verifyCodeLiveData: LiveData<BaseBean<VerifyCodeBean>> = _verifyCodeLiveData

    private val _checkVerifyCodeLiveData = MutableLiveData<BaseBean<Any>>()
    val checkVerifyCodeLiveData = _checkVerifyCodeLiveData

    /**
     * （按卡号）提交绑卡申请
     */
    fun applyBindCardWithNum(params: Map<String, String?>) {
        viewModelScope.launch {
            val mapParams = mutableMapOf<String, String>().apply {
                params.forEach {
                    put(it.key, it.value?: "")
                }
            }
            val applyBindCardWithNum = JDPayRequest().applyBindCardWithNum(mapParams)
            _applyBindCardWithNumLiveData.postValue(applyBindCardWithNum)
        }
    }

    /**
     * 按卡号绑卡确认
     */
    fun confirmBindCardWithNum(params: Map<String, String?>) {
        viewModelScope.launch {
            val mapParams = mutableMapOf<String, String>().apply {
                params.forEach {
                    put(it.key, it.value?: "")
                }
            }
            val contractSignConfirm = JDPayRequest().contractSignConfirm(mapParams)
            _confirmBindCardWithNumLiveData.postValue(contractSignConfirm)
        }
    }

    /**
     * 发送验证码
     */
    fun sendVerifyCode() {
        viewModelScope.launch {
            val accountInfo =
                AccountTable.getAccount(YBMAppLike.getApp().currActivity, SpUtil.getMerchantid())
            val sendVerifyCode =
                JDPayRequest().sendVerifyCode(accountInfo.phone, "1")
            _verifyCodeLiveData.postValue(sendVerifyCode)
        }
    }

    /**
     * 验证验证码
     */
    fun checkVerifyCode(verifyCode: String) {
        viewModelScope.launch {
            val accountInfo =
                AccountTable.getAccount(YBMAppLike.getApp().currActivity, SpUtil.getMerchantid())
            val checkVerifyCode = JDPayRequest().checkVerifyCode(accountInfo.phone, verifyCode)
            _checkVerifyCodeLiveData.postValue(checkVerifyCode)
        }
    }
}