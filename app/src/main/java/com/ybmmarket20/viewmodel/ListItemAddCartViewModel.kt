package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.ListItemAddCardBean
import com.ybmmarket20.bean.RowsPriceDiscount
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.network.request.ListItemAddCartRequest
import kotlinx.coroutines.launch

class ListItemAddCartViewModel(app: Application): BaseViewModel(app) {

    private val _listItemAddCartLiveData = MutableLiveData<BaseBean<ListItemAddCardBean>>()
    val listItemAddCartLiveData: LiveData<BaseBean<ListItemAddCardBean>> = _listItemAddCartLiveData
    private val _afterDiscountLiveData = MutableLiveData<RowsPriceDiscount>()
    val afterDiscountLiveData: LiveData<RowsPriceDiscount> = _afterDiscountLiveData

    //请求计算运费的时间戳
    private var mAddCartTrailTimestamp: Long = 0L
    //请求折后价的时间戳
    private var mGetAfterDiscountTimestamp: Long = 0L

    fun addCartTrial(skuId: String, quantity: String, orgId: String, productPrice: String,productType : Int) {
        viewModelScope.launch {
            showLoading()
            val tempTimeStamp = System.currentTimeMillis()
            mAddCartTrailTimestamp = tempTimeStamp
            val addCartTrial = ListItemAddCartRequest().addCartTrial(skuId, quantity, orgId, productPrice,productType)
            if (addCartTrial.isSuccess) {
                addCartTrial.data.num = quantity
            }
            if (tempTimeStamp == mAddCartTrailTimestamp) {
                _listItemAddCartLiveData.postValue(addCartTrial)
            } else {
                _listItemAddCartLiveData.postValue(BaseBean.newFailureBaseBean(null))
            }
            dismissLoading()
        }
    }

    fun addCartTrial(skuId: String, quantity: String, orgId: String, productPrice: String,productType : Int, freeShippingFlag : Boolean) {
        viewModelScope.launch {
            val addCartTrial = ListItemAddCartRequest().addCartTrial(skuId, quantity, orgId, productPrice, productType, freeShippingFlag)
            _listItemAddCartLiveData.postValue(addCartTrial)
        }
    }

    /**
     * 获取折后价
     */
    fun getAfterDiscount(skuId: String) {
        viewModelScope.launch {
            val tempTimeStamp = System.currentTimeMillis()
            mGetAfterDiscountTimestamp = tempTimeStamp
            val afterDiscountPriceBean = ListItemAddCartRequest().getAfterDiscountPrice(skuId)
            if (afterDiscountPriceBean.isSuccess && afterDiscountPriceBean.data != null && afterDiscountPriceBean.data!!.isNotEmpty()) {
                val afterDiscount = afterDiscountPriceBean.data!![0]
                if (tempTimeStamp == mGetAfterDiscountTimestamp) {
                    _afterDiscountLiveData.postValue(afterDiscount)
                }
            }
        }
    }
}