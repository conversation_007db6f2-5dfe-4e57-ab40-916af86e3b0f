package com.ybmmarket20.viewmodel

import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import androidx.core.content.ContextCompat
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybm.app.common.BaseYBMApp
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.FreightTipItem
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.utils.SpUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 店铺-售后配送
 */
class ShopAfterSaleDistributionViewModel: ViewModel() {

    val afterSaleDistributionLiveData = MutableLiveData<BaseBean<SpannableStringBuilder>>()
    val afterSaleDistributionPopLiveData = MutableLiveData<BaseBean<String>>()
    var shopCode: String = ""
    var orgId: String = ""

    /**
     * 初始化数据
     */
    fun initData(bundle: Bundle?) {
        bundle?.let {
            shopCode = it.getString("params")?: ""
            orgId = it.getString("params")?: ""
        }
    }

    /**
     * 获取自营店铺-售后配送内容
     */
    fun getSelfAfterSaleDistributionContent() {
        viewModelScope.launch(Dispatchers.IO) {
            val result = NetworkService.instance.getSelfAfterSaleDistributionContent(SpUtil.getMerchantid(), shopCode)
            val spannableBuilderBaseBean = BaseBean<SpannableStringBuilder>()
            spannableBuilderBaseBean.code = result.code
            spannableBuilderBaseBean.status = result.status
            spannableBuilderBaseBean.data = assembleContent(result.data?.freightTemplateTipsList)
            afterSaleDistributionLiveData.postValue(spannableBuilderBaseBean)
        }
    }

    /**
     * 组装内容
     */
    private fun assembleContent(content: MutableList<FreightTipItem>?): SpannableStringBuilder {
        val span = SpannableStringBuilder("")
        content?.forEach { freightTipItem ->
            val titleSpan = SpannableStringBuilder(freightTipItem.title)
            val textColorSpan = ForegroundColorSpan(ContextCompat.getColor(BaseYBMApp.getAppContext(), R.color.color_292933))
            val textSizeSpan = AbsoluteSizeSpan(14, true)
            titleSpan.setSpan(textColorSpan, 0, titleSpan.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            titleSpan.setSpan(textSizeSpan, 0, titleSpan.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            titleSpan.append("\n")
            span.append(titleSpan)
            freightTipItem.freightTips?.forEach { contentStr ->
                val tipSpan = SpannableStringBuilder(contentStr)
                val tipTextColorSpan = ForegroundColorSpan(ContextCompat.getColor(BaseYBMApp.getAppContext(), R.color.color_676773))
                val tipSizeSpan = AbsoluteSizeSpan(12, true)
                tipSpan.setSpan(tipTextColorSpan, 0, tipSpan.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                tipSpan.setSpan(tipSizeSpan, 0, tipSpan.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                tipSpan.append("\n")
                span.append(tipSpan)
            }
            if (content.indexOf(freightTipItem) != content.size - 1) {
                span.append("\n")
            }
        }
        return span
    }

    /**
     * 获取Pop店铺-售后配送内容
     */
    fun getPopAfterSaleDistributionContent() {
        viewModelScope.launch(Dispatchers.IO) {
            val result = NetworkService.instance.getPopAfterSaleDistributionContent(orgId)
            val htmlBean = BaseBean<String>()
            htmlBean.code = result.code
            htmlBean.status = result.status
            htmlBean.data = result.data?: ""
            afterSaleDistributionPopLiveData.postValue(htmlBean)
        }
    }

}