package com.ybmmarket20.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.MyWealthItem
import com.ybmmarket20.bean.PayNongData
import com.ybmmarket20.network.request.JDPayRequest
import kotlinx.coroutines.launch

class MyWealthViewModel: ViewModel() {

    private val _myWealthListLiveData: MutableLiveData<BaseBean<MutableList<MyWealthItem>>> = MutableLiveData()
    val myWealthListLiveData = _myWealthListLiveData
    private val _wealthPreLiveData: MutableLiveData<BaseBean<PayNongData>> = MutableLiveData()
    val wealthPreLiveData = _wealthPreLiveData

    fun queryMyWealthList() {
        viewModelScope.launch {
            val result = JDPayRequest().queryMyWealthList()
            _myWealthListLiveData.postValue(result)
        }
    }

    fun xydPreApply(creditChannel:Int = 4) {
        viewModelScope.launch {
            val result = JDPayRequest().xydPreApply(creditChannel)
            _wealthPreLiveData.postValue(result)
        }
    }
}