package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.PayDialogBean
import com.ybmmarket20.network.request.PayResultRequest
import kotlinx.coroutines.launch

/**
 * 支付后弹窗
 */
class PayDialogViewModel: ViewModel() {

    private val _payDialogLiveData: MutableLiveData<BaseBean<PayDialogBean>> = MutableLiveData()
    val payDialogLiveData: LiveData<BaseBean<PayDialogBean>> = _payDialogLiveData




    fun getPayDialog(orderNo: String?, payCode: String?) {
        viewModelScope.launch {
            if (orderNo == null || payCode == null){
                _payDialogLiveData.postValue(BaseBean.newFailureBaseBean(PayDialogBean(null, null, null)))
            } else {
                val result = PayResultRequest().getPayDialog(orderNo, payCode)
                _payDialogLiveData.postValue(result)
            }
        }
    }
}