package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.network.request.UnBindBankRequest
import kotlinx.coroutines.launch

/**
 * 解绑银行卡
 */
class UnBindBankCardViewModel(app: Application): BaseViewModel(app) {

    private val _unBindCardLiveData = MutableLiveData<BaseBean<Any>>()
    val unBindCardLiveData: LiveData<BaseBean<Any>> = _unBindCardLiveData

    fun unBindBankCard(reqNo: String) {
        viewModelScope.launch {
            val unBindBankCard = UnBindBankRequest().unBindBankCard(reqNo)
            _unBindCardLiveData.postValue(unBindBankCard)
        }
    }
}