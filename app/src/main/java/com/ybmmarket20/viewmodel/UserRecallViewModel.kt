package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.UserRecallBean
import com.ybmmarket20.network.request.UserRecallRequest
import kotlinx.coroutines.launch

/**
 * 用户召回信息
 */
class UserRecallViewModel: ViewModel() {

    private val _userRecallLiveData = MutableLiveData<BaseBean<UserRecallBean>>()
    val userRecallLiveData: LiveData<BaseBean<UserRecallBean>> = _userRecallLiveData

    fun getUserRecallInfo() {
        viewModelScope.launch {
            val userRecallGuideInfo = UserRecallRequest().getUserRecallGuideInfo()
            _userRecallLiveData.postValue(userRecallGuideInfo)
        }
    }
}