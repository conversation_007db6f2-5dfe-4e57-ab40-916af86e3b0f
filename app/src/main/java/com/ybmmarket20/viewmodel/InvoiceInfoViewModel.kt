package com.ybmmarket20.viewmodel

import android.content.Intent
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybm.app.bean.NetError
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.InvoiceListBaseBean
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.utils.SpUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 发票
 */
class InvoiceInfoViewModel: ViewModel() {

    val invoiceInfoLiveData = MutableLiveData<BaseBean<InvoiceListBaseBean>>()
    val sendMailInfo = MutableLiveData<String>()
    val initDataLiveData = MutableLiveData<InitData>()

    var mInitData: InitData? = null

    data class InitData(
        var merchantId: String?,
        var orderId: String?,
        var orderNo: String?,
        var createOrderTime: String?,
        var payTime: String?,
        var billInfo: String?
    )


    /**
     * 处理初始化数据
     */
    fun initWithIntent(intent : Intent){
        mInitData = InitData(
            SpUtil.getMerchantid(),
            intent.getStringExtra("orderid")?: "",
            intent.getStringExtra("number")?: "",
            (intent.getStringExtra("time")?: "").replace("-", "/"),
            (intent.getStringExtra("paytime")?: "").replace("-", "/"),
            intent.getStringExtra("billInfo")?: ""
        ).apply(initDataLiveData::postValue)
    }

    /**
     * 获取发票信息
     */
    fun getInvoiceInfo(merchantId: String, orderNo: String) {
        viewModelScope.launch(Dispatchers.IO) {
            val invoiceInfo = NetworkService.instance.getInvoiceInfo(merchantId, orderNo)
            invoiceInfoLiveData.postValue(invoiceInfo)
        }
    }

    /**
     * 发送邮件
     */
    fun sendMail(email: String) {
//        viewModelScope.launch {
//        }
        sendEmailConfirm(mInitData?.merchantId?: "", email, mInitData?.orderId?:"")
    }

    private fun sendEmailConfirm(merchantId: String, content: String, mOrderid: String) {
        val str = "发送成功"
        val str_fail = "发送失败"
        val merchantid = SpUtil.getMerchantid()
        val params = RequestParams()
        params.put("merchantId", merchantid)
        params.put("orderId", mOrderid)
        params.put("email", content)
        HttpManager.getInstance()
            .post(AppNetConfig.QUERY_INVOICE_EMAIL, params, object : BaseResponse<BaseBean<String>>() {
                override fun onSuccess(
                    content: String?,
                    obj: BaseBean<BaseBean<String>>?,
                    t: BaseBean<String>?
                ) {
                    super.onSuccess(content, obj, t)
                    if (obj != null && obj.isSuccess) {
                        sendMailInfo.postValue(str)
                    }
                }

                override fun onFailure(error: NetError?) {
                    super.onFailure(error)
                    sendMailInfo.postValue(str_fail)
                }
            })
    }
}