package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.SearchStartRecommend
import com.ybmmarket20.network.request.SearchStartRecommendRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 热卖排行榜
 */
class SearchStartRecommendViewModel: ViewModel() {

    private val searchStartRecommendLiveData = MutableLiveData<SearchStartRecommend>()

    fun getSearchStartRecommendData() {
        viewModelScope.launch(Dispatchers.IO) {
            val searchStartRecommendData =
                SearchStartRecommendRequest().getSearchStartRecommendData()
            searchStartRecommendLiveData.postValue(searchStartRecommendData?.data)
        }
    }

    fun getLiveData(): LiveData<SearchStartRecommend> = searchStartRecommendLiveData
}