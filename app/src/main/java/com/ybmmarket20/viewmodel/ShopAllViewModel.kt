package com.ybmmarket20.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.ybmmarket20.bean.ShopHomeIndexBean

/**
 * pop商家公告
 */
class ShopAllViewModel: ViewModel() {
    private val _popShopNoticesLiveData = MutableLiveData<ShopHomeIndexBean.ShopNoticeVo>()
    val popShopNoticesLiveData: LiveData<ShopHomeIndexBean.ShopNoticeVo> = _popShopNoticesLiveData

    fun setShopNotices(notices: ShopHomeIndexBean.ShopNoticeVo) {
        _popShopNoticesLiveData.postValue(notices)
    }
}