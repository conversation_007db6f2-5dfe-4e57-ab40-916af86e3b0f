package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.aftersales.AFTER_SALES_INVOICE_TYPE_ERROR
import com.ybmmarket20.bean.aftersales.AFTER_SALES_INVOICE_TYPE_NO
import com.ybmmarket20.bean.aftersales.AFTER_SALES_INVOICE_TYPE_NORMAL
import com.ybmmarket20.bean.aftersales.AFTER_SALES_INVOICE_TYPE_SPECIAL
import com.ybmmarket20.bean.aftersales.AfterSalesAcceptElectronicInvoice
import com.ybmmarket20.bean.aftersales.AfterSalesBean
import com.ybmmarket20.bean.aftersales.AfterSalesInvoiceType
import com.ybmmarket20.bean.aftersales.AfterSalesNoBean
import com.ybmmarket20.bean.aftersales.AfterSalesSpecialInvoice
import com.ybmmarket20.bean.aftersales.AfterSalesTips
import com.ybmmarket20.bean.aftersales.AfterSalesUploadImage
import com.ybmmarket20.network.request.AfterSalesRequest
import kotlinx.coroutines.launch

/**
 * 售后
 */
class AfterSalesViewModel(appLike: Application) : BaseViewModel(appLike) {

    private val mAfterSalesBean: AfterSalesBean = AfterSalesBean()
    private val _afterSalesLiveData = MutableLiveData<AfterSalesBean>()
    val afterSalesLiveData: LiveData<AfterSalesBean> = _afterSalesLiveData

    private val _submitResultLiveData = MutableLiveData<BaseBean<AfterSalesNoBean>>()
    val submitResultLiveData: LiveData<BaseBean<AfterSalesNoBean>> = _submitResultLiveData

    /**
     * 根据发票类型获取发票信息
     */
    fun getInvoiceData(invoiceType: Int, orderNo: String) {
        viewModelScope.launch {
            when(invoiceType) {
                //未选中状态
                AFTER_SALES_INVOICE_TYPE_NORMAL -> {
                    addInvoiceType(invoiceType)
                    addCommon()
                    _afterSalesLiveData.postValue(mAfterSalesBean)
                }
                //无票
                AFTER_SALES_INVOICE_TYPE_NO -> {
                    addInvoiceType(invoiceType)
                    addCommon()
                    _afterSalesLiveData.postValue(mAfterSalesBean)
                }
                //错票
                AFTER_SALES_INVOICE_TYPE_ERROR -> {
                    //查看当前是否有错票信息，如果有这直接使用，如果没有通过网络获取
                    if (mAfterSalesBean.afterSalesErrorInvoiceInfoList == null) {
                        val queryErrorInvoiceInfoListBaseBean =
                            AfterSalesRequest().queryErrorInvoiceInfoList(orderNo)
                        if (queryErrorInvoiceInfoListBaseBean.isSuccess) {
                            mAfterSalesBean.afterSalesErrorInvoiceInfoList = queryErrorInvoiceInfoListBaseBean.data
                        }
                    }
                    //再次检查是否有错票信息
                    if (mAfterSalesBean.afterSalesErrorInvoiceInfoList != null) {
                        addInvoiceType(invoiceType)
                        addCommon()
                        _afterSalesLiveData.postValue(mAfterSalesBean)
                    }
                }
                //申请专票
                AFTER_SALES_INVOICE_TYPE_SPECIAL -> {
                    if (mAfterSalesBean.afterSalesSpecialInvoiceList == null) {
                        val querySpecialInvoiceInfoBaseBean =
                            AfterSalesRequest().querySpecialInvoiceInfo(orderNo)
                        if (querySpecialInvoiceInfoBaseBean.isSuccess) {
                            mAfterSalesBean.afterSalesSpecialInvoiceList = (querySpecialInvoiceInfoBaseBean.data?: AfterSalesSpecialInvoice()).toList()
                        }
                    }
                    if (mAfterSalesBean.afterSalesSpecialInvoiceList != null) {
                        addInvoiceType(invoiceType)
                        addInvoiceSpecialInvoiceInfoTitle()
                        addInvoiceSpecialInvoiceInfo()
                        addCommon()
                        _afterSalesLiveData.postValue(mAfterSalesBean)
                    }
                }
            }
        }
    }

    /**
     * 添加发票类型
     */
    private fun addInvoiceType(invoiceType: Int) {
        if (mAfterSalesBean.afterSalesInvoiceType == null) {
            mAfterSalesBean.afterSalesInvoiceType = AfterSalesInvoiceType()
        }
        mAfterSalesBean.afterSalesInvoiceType?.type = invoiceType
    }

    /**
     * 添加接受电子发票
     */
    private fun addInvoiceSpecialInvoiceInfoTitle() {
        if (mAfterSalesBean.afterSalesAcceptElectronicInvoice == null) {
            mAfterSalesBean.afterSalesAcceptElectronicInvoice = AfterSalesAcceptElectronicInvoice(true)
        }
    }

    /**
     * 添加核实电子专票
     */
    private fun addInvoiceSpecialInvoiceInfo() {
        if (mAfterSalesBean.afterSalesSpecialInvoiceList == null) {
            mAfterSalesBean.afterSalesSpecialInvoiceList = AfterSalesSpecialInvoice().toList()
        }
    }

    /**
     * 添加通用的模块
     * 补充说明和上传凭据
     */
    private fun addCommon() {
        if (mAfterSalesBean.afterSalesTips == null) {
            mAfterSalesBean.afterSalesTips = AfterSalesTips()
        }
        if (mAfterSalesBean.afterSalesUploadImage == null) {
            mAfterSalesBean.afterSalesUploadImage = AfterSalesUploadImage()
        }
    }

    /**
     * 提交发票售后
     */
    fun submitInvoiceAfterSles(orderNo: String) {
        val paramsGenerator = ParamsGenerator(mAfterSalesBean)
        paramsGenerator.addParams("orderNo", orderNo)
        val paramsMap = when(mAfterSalesBean.afterSalesInvoiceType?.type) {
            //无票
            AFTER_SALES_INVOICE_TYPE_NO -> {
                paramsGenerator
            }
            //错票
            AFTER_SALES_INVOICE_TYPE_ERROR -> {
                paramsGenerator
                    .addSelectedErrorInvoiceInfoParams()
            }
            //申请专票
            AFTER_SALES_INVOICE_TYPE_SPECIAL -> {
                paramsGenerator
                    .addAcceptSpecialElectronicInvoiceInfoParams()
                    .addSpecialInvoiceInfoParams()
            }
            else -> {
                null
            }
        }?.generateParams()
        viewModelScope.launch {
            paramsMap?.run {
                val submitInvoiceAfterSales = AfterSalesRequest().submitInvoiceAfterSales(this)
                _submitResultLiveData.postValue(submitInvoiceAfterSales)
            }
        }
    }

    class ParamsGenerator(private val afterSalesBean: AfterSalesBean) {

        private val mParams = mutableMapOf<String, String>()

        /**
         * 添加参数
         */
        fun addParams(key: String, value: String) {
            mParams[key] = value
        }

        /**
         * 生成
         */
        fun generateParams(): Map<String, String> {
            addInvoiceTypeParams()
            addAfterSalesTipsParams()
            addUploadImagePathsParams()
            return mParams
        }

        /**
         * 添加发票类型
         */
        fun addInvoiceTypeParams(): ParamsGenerator {
            mParams["subType"] = "${afterSalesBean.afterSalesInvoiceType?.type?: ""}"
            return this
        }

        /**
         * 选择发票中的错误信息
         */
        fun addSelectedErrorInvoiceInfoParams(): ParamsGenerator {
            val errorInfo = afterSalesBean.afterSalesErrorInvoiceInfoList?.filter { it.isSelected }
                    ?.map { it.itemType }
                ?.joinToString(",")
            if (!errorInfo.isNullOrEmpty()) {
                mParams["incorrectInvoiceType"] = errorInfo
            }
            return this
        }

        /**
         * 是否接受电子专票
         */
        fun addAcceptSpecialElectronicInvoiceInfoParams(): ParamsGenerator {
            val isSelected = afterSalesBean.afterSalesAcceptElectronicInvoice?.isAccepted?: true
            mParams["isElectronicInvoice"] = if (isSelected) "1" else "0"
            return this
        }

        /**
         * 专票信息
         */
        fun addSpecialInvoiceInfoParams(): ParamsGenerator {
            afterSalesBean.afterSalesSpecialInvoiceList?.forEach {
                mParams[it.key] = it.content?: ""
            }
            return this
        }

        /**
         * 补充说明
         */
        private fun addAfterSalesTipsParams(): ParamsGenerator {
            mParams["remarks"] = afterSalesBean.afterSalesTips?.tips?: ""
            return this
        }

        /**
         * 上传凭据
         */
        private fun addUploadImagePathsParams(): ParamsGenerator {
            val uploadImagePathList = afterSalesBean.afterSalesUploadImage?.imagePathList
            if (uploadImagePathList.isNullOrEmpty()) return this
            mParams["evidences"] = Gson().toJson(uploadImagePathList).toString()
            return this
        }





    }


}