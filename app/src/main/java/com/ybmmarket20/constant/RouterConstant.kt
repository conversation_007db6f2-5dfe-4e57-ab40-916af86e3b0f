package com.ybmmarket20.constant

//路由常量

//schema
const val ROUTER_SCHEME_YAOBANGMANG = "yaobangmang"
const val ROUTER_SCHEME_YBM100 = "ybm100"
const val ROUTER_SCHEME_YBMPAGE = "ybmpage"
const val ROUTER_SCHEME_HTTP = "http"

const val ROUTER_SCHEME_WITH_SYMBOL_YAOBANGMANG = "$ROUTER_SCHEME_YAOBANGMANG://"
const val ROUTER_SCHEME_WITH_SYMBOL_YBM100 = "$ROUTER_SCHEME_YBM100://"
const val ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE = "$ROUTER_SCHEME_YBMPAGE://"

//host
const val ROUTER_HOST_COMMON_H5 = "commonh5activity"
const val ROUTER_HOST_YBMMARKET20 = "ybmmarket20.com"
const val ROUTER_HOST_H5_RELEASE = "app-v4.ybm100.com"
const val ROUTER_HOST_H5_STAGE = "new-app.stage.ybm100.com"
const val ROUTER_HOST_H5_STAGE_NEW = "app-new.stage.ybm100.com"
const val ROUTER_HOST_H5_TEST = "new-app.test.ybm100.com"
const val ROUTER_HOST_CMS_RELEASE = "app.ybm100.com"
const val ROUTER_HOST_CMS_STAGE = "app.stage.ybm100.com"
const val ROUTER_HOST_CMS_TEST = "app.test.ybm100.com"

//path
const val ROUTER_PATH_COMMON_H5 = "/commonh5activity"
const val ROUTER_PATH_COMMON_H5_CACHE_URL = "?cache=0&url="
const val ROUTER_PATH_REGISTER = "/register"
const val ROUTER_PATH_PRODUCTDETAIL = "/productdetail"
const val ROUTER_PATH_COUPONAVAILABLEACTIVITY = "/couponavailableactivity"
const val ROUTER_PATH_SEARCHPRODUCT = "/searchproduct"
const val ROUTER_PATH_BINDWX = "/bindWX"


//query


//router
//登录
const val ROUTER_LOGIN = "${ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE}login"

//注册
const val ROUTER_REGISTER = "${ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE}register"

//语音搜索
const val ROUTER_VOICE_SEARCH_PRODUCT = "${ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE}searchvoiceactivity"

//扫码
const val ROUTER_CAPTURE = "${ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE}captureactivity"

const val OPENNEXTPAGEURLKEY = "openNextPageUrl"

const val CURRENT_PAGE = "current_page"
//调用扫描二维码页面ResultCode
const val CURRENT_PAGE_RESULT_QR = 201
//调用语音页面
const val CURRENT_PAGE_RESULT_VOICE = 202

//搜索
const val ROUTER_SEARCH_PRODUCT = "${ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE}searchproduct"
const val ROUTER_SEARCH_PRODUCT_OP = "${ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE}searchproductop"
const val ROUTER_SEARCH_PRODUCT_V2 = "${ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE}searchproductop"


//region 推荐清单
// 推荐清单首页 + 推荐清单历史详情
const val ROUTER_CRM_RECOMMEND = "ybmpage://crmrecommendactivity"

// 推荐清单历史
const val ROUTER_CRM_RECOMMEND_HISTORY = "ybmpage://crmrecommendhistoryactivity"
//endregion

//H5
const val ROUTER_COMMON_H5_ACTIVITY_CACHE_URL="${ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE}${ROUTER_HOST_COMMON_H5}${ROUTER_PATH_COMMON_H5_CACHE_URL}"

