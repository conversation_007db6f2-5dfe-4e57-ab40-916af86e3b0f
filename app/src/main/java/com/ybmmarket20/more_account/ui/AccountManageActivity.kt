package com.ybmmarket20.more_account.ui

import android.graphics.Color
import android.os.Bundle
import android.util.TypedValue
import android.view.View
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.github.mzule.activityrouter.annotation.Router
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.ybmmarket20.R

@Router("accountbasicinfo1")
class AccountManageActivity : FragmentActivity() {
    private val mTabText = listOf<String>("账号管理", "基本信息")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.account_manager_activity)
        findViewById<View>(R.id.iv_back).setOnClickListener{
            finish()
        }

        val viewpager = findViewById<ViewPager2>(R.id.viewPager)
        viewpager.adapter = FragmentsAdapter(this)

        val tabLayout = findViewById<TabLayout>(R.id.tab_layout)

        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener{
            private fun updateTabViewAppearance(tab: TabLayout.Tab?, size:Float, color : Int){
                val textView = tab?.customView as TextView
                textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, size)
                textView.setTextColor(color)
            }
            override fun onTabSelected(tab: TabLayout.Tab?) = updateTabViewAppearance(tab, 16f, Color.BLACK)

            override fun onTabUnselected(tab: TabLayout.Tab?) = updateTabViewAppearance(tab, 14f, Color.GRAY)

            override fun onTabReselected(tab: TabLayout.Tab?) { }
        })

        TabLayoutMediator(tabLayout, viewpager){ tab, position ->
            tab.setCustomView(R.layout.tablayout_custom_item)
            (tab.customView as TextView).text = mTabText[position]
        }.attach()

    }

    private inner class FragmentsAdapter(activity : FragmentActivity) : FragmentStateAdapter(activity){
        override fun getItemCount(): Int  = mTabText.size

        override fun createFragment(position: Int): Fragment = when(position){
            0 -> AccountManagerFragment()
            1 -> AccountBaseInfoFragment()
            else -> AccountManagerFragment() // TODO: 空值
        }
    }
}