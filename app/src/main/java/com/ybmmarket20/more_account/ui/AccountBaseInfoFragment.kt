package com.ybmmarket20.more_account.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.ybmmarket20.R

class AccountBaseInfoFragment : Fragment() {
    private lateinit var mViewModule: AccountMgrViewModel

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val root = inflater.inflate(R.layout.fragment_account_basic_info, container, false)

        mViewModule = ViewModelProvider(this).get(AccountMgrViewModel::class.java)
        mViewModule.mBasicInfoLiveData.observe(viewLifecycleOwner, Observer {
            val info = it?.info
            if (info != null) {
                mapOf(R.id.tv_company_name to info.realName
                , R.id.tv_invoice_company_name to info.realName
                , R.id.tv_company_type to info.customerTypeName
                , R.id.tv_company_address to info.registerAddress
                , R.id.tv_business_scope to info.scopeOfExperience
                , R.id.tv_invoice_company_name to info.realName
                , R.id.tv_invoice_type to info.invoiceName)
               .forEach { entry ->
                    root.findViewById<TextView>(entry.key).text = entry.value
                }
            }
        })

        return root
    }

    override fun onResume() {
        super.onResume()
        mViewModule.getAccountBasicInfo()
    }

}