package com.ybmmarket20.more_account.ui

import android.content.Intent
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.ybm.app.common.BaseYBMApp
import com.ybm.app.utils.BugUtil
import com.ybmmarket20.bean.AccountBean
import com.ybmmarket20.bean.AptitudeBasicInfoBean
import com.ybmmarket20.bean.Login
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.constant.SP_KEY_LOGIN_IS_KA
import com.ybmmarket20.db.AccountTable
import com.ybmmarket20.db.info.HandlerGoodsDao
import com.ybmmarket20.message.Message
import com.ybmmarket20.utils.*
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.xyyreport.session.SessionManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

//TODO: room替换数据库实现，使用BaseBean{cache, net, success ...}统一网络和数据库返回值，错误留给UI处理
//启动时应该自动初始化一次相关数据，避免每次零碎加载

class AccountMgrViewModel : ViewModel() {
    val mAccountList = MutableLiveData<ArrayList<AccountBean>>()
    val mBasicInfoLiveData = MutableLiveData<AptitudeBasicInfoBean>()
    private var mBasicInfo: AptitudeBasicInfoBean? = null

    fun loadAccountList() {
        viewModelScope.launch(Dispatchers.IO) {
            val accountList = AccountTable.getAllAccount(BaseYBMApp.getAppContext())
            mAccountList.postValue(accountList)
        }
    }

    fun removeAccount(accountBean: AccountBean, callback: (Boolean) -> Unit) {
        viewModelScope.launch {
            val result = withContext(Dispatchers.IO) {
                AccountTable.delete(YBMAppLike.getAppContext(), accountBean.merchantId)
            }
            callback(result) //callback操纵UI是个隐患，回调里需要判断
        }
    }

    //TODO: 登录流程需要整理统一一个controller和model，否则到处都要一样的代码
    fun switchAccount(accountBean: AccountBean) {
        viewModelScope.launch(Dispatchers.IO) {
            val login = NetworkService.instance.login(accountBean.userName, accountBean.password)
            if (login.isSuccess && login.data != null) {
                AuditStatusSyncUtil.getInstance().setLicenseStatusOnly(login.data.licenseStatus)
                SpUtil.setLoginPhone(accountBean.phone)
                //保存并更新登录的信息
                updateLogin(login.data)
                //清除usersp电话信息
                SpUtil.writeString("phone", "")
                SpUtil.writeInt("show_ad_collect_pop", 1)
                SpUtil.writeInt("show_dialog_in_pay_result", 0)
                // 是否是ka用户
                SpUtil.writeBoolean(SP_KEY_LOGIN_IS_KA, login.data.isKa)
                //清除密码强度的提示
                SpUtil.writeBoolean("already_mention", false)
                LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(Intent(IntentCanst.ACTION_AD_COLLECT_HINT_POP))
                //登陆广播
                LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(Intent(IntentCanst.ACTION_REFRESH_RECOMMEND))
                //登陆广播
                LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(Intent(IntentCanst.ACTION_LOGIN))
                //更新购物车数量广播
                LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(Intent(IntentCanst.ACTION_SHOPNUMBER))
                //购物车商品数
                HandlerGoodsDao.getInstance().create4Sp()
                //通知"我的"页面重新刷新数据
                LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(Intent(IntentCanst.ACTION_SWITCH_USER))
                LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(Intent(IntentCanst.ACTION_CHANGE_HOME_LAYOUT_TYPE))
                //通知"全部药品列表"页面重新刷新数据
                LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(Intent(IntentCanst.ACTION_BRAND_SET))
                //后台获取购物车的信息
                YbmCommand.getCartListForBack()
                YbmPushUtil.bindPushtoken()
                //重新获取消息数量
                Message.instance.findUnReadCount()
                loadAccountList()//TODO: 还要检查
                loadAccountBasicInfo()
            } else {
                //1表示是密码错误，需重新登录
                RoutersUtils.open("ybmpage://addaccount/1")
            }
        }
    }

    fun searchAccount(keyword: String) {
        viewModelScope.launch(Dispatchers.IO) {
            val accountList = AccountTable.getAllAccount(BaseYBMApp.getAppContext())
            accountList.removeAll {
                !it.shopName.contains(keyword)
            }
            mAccountList.postValue(accountList)
        }
    }

    fun getAccountBasicInfo(){
        if(mBasicInfo == null){
            loadAccountBasicInfo()
        } else{
            mBasicInfoLiveData.postValue(mBasicInfo!!)
        }
    }

    //TODO： 建立一个统一的缓存机制，就不用存在局部变量里了。
    private fun loadAccountBasicInfo() {
        viewModelScope.launch {
            val basicInfo = withContext(Dispatchers.IO) {
                NetworkService.instance.requestBasicInfo(SpUtil.getMerchantid())
            }
            mBasicInfo =  null
            mBasicInfoLiveData.postValue(basicInfo.data)
        }
    }


    private fun updateLogin(login: Login?) {
        if (login == null) {
            return
        }
        val merchantId = login.merchantId
        if (merchantId != null && java.lang.Long.parseLong(merchantId) > 0) {
            SessionManager.get().newSession()
            SpUtil.setMerchantid(merchantId + "")
            BugUtil.updateUserId(SpUtil.getMerchantid())
            SpUtil.setToken(login.token)
        }
    }
}
