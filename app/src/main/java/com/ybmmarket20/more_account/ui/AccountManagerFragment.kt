package com.ybmmarket20.more_account.ui

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.ybm.app.view.CommonRecyclerView
import com.ybmmarket20.R
import com.ybmmarket20.bean.AccountBean
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.view.DividerLine

interface AccountOptionsDelegater {
    fun doDeleteAccount(account: AccountBean)
    fun doSwitchAccount(account: AccountBean)
}

class AccountManagerFragment : Fragment(), AccountOptionsDelegater {
    private lateinit var mViewModel: AccountMgrViewModel

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val root = inflater.inflate(R.layout.fragment_account_manager_remaster, container, false)
        val recyclerView = root.findViewById<CommonRecyclerView>(R.id.rv_account_list)
        mViewModel = ViewModelProvider(this).get(AccountMgrViewModel::class.java)
        //列表元素点击事件
        val adapter = AccountListAdapter(this)

        recyclerView.setRefreshEnable(false)
        recyclerView.setShowAutoRefresh(false)
        recyclerView.setLoadMoreEnable(false)
        val divider = DividerLine(DividerLine.VERTICAL)
        divider.setSize(1)
        divider.setColor(-0xe0e0f)
        recyclerView.addItemDecoration(divider)
        recyclerView.setAdapter(adapter)

        //设置搜索框
        root.findViewById<TextView>(R.id.title_et).addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}

            override fun afterTextChanged(s: Editable) {
                mViewModel.searchAccount(s.toString())
            }
        })

        val updateModifyBtn = root.findViewById<View>(R.id.update_account_list)
        val noticeBannerView = root.findViewById<View>(R.id.tv_tip)

        fun switchEditMode(editable: Boolean) {
            //会盖住下面的两个按钮, 不额外做隐藏动作了
            updateModifyBtn.visibility = if (editable) View.VISIBLE else View.GONE
            noticeBannerView.visibility = if (editable) View.GONE else View.VISIBLE
//            adapter.mIsEdit = editable;
            adapter.setEditable(editable)
        }
        //结束编辑状态
        updateModifyBtn.setOnClickListener { switchEditMode(false) }
        //进入编辑状态
        root.findViewById<View>(R.id.tv_edit_account).setOnClickListener { switchEditMode(true) }
        //添加新账号
        root.findViewById<View>(R.id.tv_add_account).setOnClickListener { RoutersUtils.open("ybmpage://addaccount") }

        //设置数据监听
        mViewModel.mAccountList.observe(viewLifecycleOwner, Observer<ArrayList<AccountBean>> { beans ->
            //TODO: 退回数据库的问题
            adapter.replaceData(beans)
        })

        mViewModel.loadAccountList()

        return root
    }

    override fun doDeleteAccount(account: AccountBean) {
        AlertDialogEx(<EMAIL>).apply {
            setTitle("删除")
            setMessage("是否删除该账号？")
            setCancelButton("取消", null)
            setConfirmButton("确定"){ _, _ ->
                mViewModel.removeAccount(account) { success ->
                    if (isDetached) return@removeAccount//这里隐患在于仍然存在生命周期不同步的问题，但也不能每次响应都声明一个liveData
                    if (success) {
                        ToastUtils.showShort("删除成功")
                        mViewModel.loadAccountList()
                    } else {
                        ToastUtils.showShort("删除失败")
                    }
                }
            }
        }.show()
    }

    override fun doSwitchAccount(account: AccountBean) {
        val loginPhone = account.userName
        val loginPassword = account.password
        if(loginPhone.isNullOrEmpty() || loginPassword.isNullOrEmpty()){
            //其中"1"表示有错误，需要处理
            RoutersUtils.open("ybmpage://addaccount/1")
            return
        }

        mViewModel.switchAccount(account)
        //TODO 通用加载对话框
        //showProgress("正在切换账号")

    }


}