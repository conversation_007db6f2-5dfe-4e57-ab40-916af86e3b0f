package com.ybmmarket20.more_account.ui

import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.common.BaseYBMApp
import com.ybmmarket20.R
import com.ybmmarket20.bean.AccountBean
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.utils.SpUtil

class AccountListAdapter(private val mEventDelegate: AccountOptionsDelegater) : YBMBaseAdapter<AccountBean>(R.layout.item_account_manager, ArrayList<AccountBean>()) {
    sealed class EditorStatus(val adapter: YBMBaseAdapter<AccountBean>) { //这里不需要这么复杂的，但涉及到复杂逻辑的，状态模式比if-else的堆叠更容易维护
        fun enable() {
            adapter.notifyDataSetChanged()
        }

        abstract fun getEditIconResource(isSameMerchant: <PERSON>olean): Int
        abstract val editable: Boolean

        class Editable(adapter: Y<PERSON>BaseAdapter<AccountBean>, override val editable: Boolean = true) : EditorStatus(adapter) {
            override fun getEditIconResource(isSameMerchant: Boolean): Int =
                    if (isSameMerchant) R.drawable.icon_account_manager_checkbox_disselect else R.drawable.icon_delete_red
        }

        class UnEditable(adapter: YBMBaseAdapter<AccountBean>, override val editable: Boolean = false) : EditorStatus(adapter) {
            override fun getEditIconResource(isSameMerchant: Boolean): Int =
                    if (isSameMerchant) R.drawable.checkbox_select else R.drawable.transparent
        }
    }

    /*
        private var mIsEdit: Boolean = false
            set(value) {
                field = value
                notifyDataSetChanged()
            }
        这个也行
    */
    /*  或者代理方式
        private var mIsEdit: Boolean by Delegates.observable(false) { _, _, _ ->
            notifyDataSetChanged()
        }
     */
    private var mEditorStatus: EditorStatus = EditorStatus.UnEditable(this)

    fun setEditable(editabale: Boolean) {
        mEditorStatus = if (editabale) EditorStatus.Editable(this) else EditorStatus.UnEditable(this)
        mEditorStatus.enable()
    }

    override fun bindItemView(holder: YBMBaseHolder, bean: AccountBean) {
        holder.setText(R.id.tv_shop_name, bean.shopName)
        holder.setText(R.id.tv_shop_phone, "电话：" + bean.phone)
        holder.setText(R.id.tv_shop_address, "地址：" + bean.address)
        val contentView = holder.getConvertView()
        val ivStatue = holder.getView<ImageView>(R.id.iv_statue)
        val isTheSameMerchant = TextUtils.equals(SpUtil.getMerchantid(), bean.merchantId)

        ivStatue.setImageResource(mEditorStatus.getEditIconResource(isTheSameMerchant))
        contentView.isEnabled = !mEditorStatus.editable
        ivStatue.isEnabled = mEditorStatus.editable
        contentView.setOnClickListener(View.OnClickListener {
            if (isTheSameMerchant) {
                return@OnClickListener
            }
            val position = holder.adapterPosition
            if (mData.size > position) { //????不能吧
                mEventDelegate.doSwitchAccount(mData[position] as AccountBean)
                (BaseYBMApp.getApp() as YBMAppLike).saasOrderSourcePath = null//奇葩逻辑...先不动了
            }
        })

        ivStatue.setOnClickListener(View.OnClickListener {
            if (isTheSameMerchant) {
                return@OnClickListener
            }
            val position = holder.adapterPosition
            if (mData.size > position) {
                mEventDelegate.doDeleteAccount(mData[position] as AccountBean)
            }
        })
    }

}
