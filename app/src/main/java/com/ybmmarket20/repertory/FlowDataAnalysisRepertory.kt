package com.ybmmarket20.repertory

import com.ybmmarket20.bean.FlowDataAnalysisSId
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.SpUtil

/**
 * <AUTHOR>
 * @date 2020-04-22
 * @description 埋点相关接口
 */

/**
 * 获取SId
 */
fun getSId(callback: BaseResponse<FlowDataAnalysisSId>) {
    val params: RequestParams = RequestParams().also {
        it.put("merchantId", SpUtil.getMerchantid())
    }
    HttpManager.getInstance().post(AppNetConfig.FLOW_DATA_ANALYSIS_GET_SID, params, callback)
}


