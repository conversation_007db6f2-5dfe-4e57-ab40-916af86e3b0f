package com.ybmmarket20.repertory;

import com.ybm.app.bean.NetError;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.RecommendResultBean;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.WrapRecommendBean;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.utils.SpUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by dengmingjia on 2019/4/9
 * 推荐相关的数据仓库
 */
public class RecommendRepertory {
    private List<WrapRecommendBean> result = new ArrayList<>();
    private boolean hasRefresh;
    private RecommendListener mListener;

    /**
     * 是否有刷新，false 为流式
     *
     * @param hasRefresh
     */
    public RecommendRepertory(boolean hasRefresh, RecommendListener listener) {
        this.hasRefresh = hasRefresh;
        mListener = listener;
    }

    public void getRecommendData(boolean refresh) {

        RequestParams params = new RequestParams();
        params.put("merchantId", SpUtil.getMerchantid());
        params.put("downFreshCount", "1");
        params.put("upFreshCount", "1");

        HttpManager.getInstance().post(AppNetConfig.RECOMMENDED_SKU_FOR_YOU_FRONT, params, new BaseResponse<RecommendResultBean>() {
            @Override
            public void onFailure(NetError error) {
                if (mListener != null) {
                    mListener.onGetDataFail(refresh);
                }
            }

            @Override
            public void onSuccess(String content, BaseBean<RecommendResultBean> obj, RecommendResultBean recommendResultBean) {

                if (obj != null && obj.isSuccess()) {

                    if (recommendResultBean != null && recommendResultBean.rows != null && recommendResultBean.rows.size() > 0) {
                        List<WrapRecommendBean> wrapRecommendBeans = new ArrayList<>();
                        for (RecommendResultBean.RecommendListBean recommendListBean : recommendResultBean.rows) {
                            if (recommendListBean == null) break;
                            if (recommendListBean.skuDtoList != null && recommendListBean.skuDtoList.size() > 0) {
                                for (RowsBean rowsBean : recommendListBean.skuDtoList) {
                                    if (rowsBean != null) {
                                        rowsBean.zhugeEventName = XyyIoUtil.ACTION_HOME_R_PRODUCT;
                                        WrapRecommendBean wrapRecommendBean = new WrapRecommendBean();
                                        wrapRecommendBean.setItemType(WrapRecommendBean.CONTENT_NORMAL);
                                        wrapRecommendBean.setRowsBean(rowsBean);
                                        wrapRecommendBeans.add(wrapRecommendBean);
                                    }
                                }
                                if (recommendListBean.preferredBrandBuineseDto != null) {
                                    WrapRecommendBean wrapRecommendBean = new WrapRecommendBean();
                                    wrapRecommendBean.setItemType(WrapRecommendBean.CONTENT_COMBO);
                                    wrapRecommendBean.setBrand(true);
                                    List<RowsBean> list = recommendListBean.preferredBrandBuineseDto.skuVOList;
                                    if (list == null) {
                                        list = new ArrayList<>();
                                        recommendListBean.preferredBrandBuineseDto.skuVOList = list;
                                    }
//                                    recommendListBean.preferredBrandBuineseDto.skuVOList.addAll(list);
                                   /* if (list.size() > 5) {
                                        list = list.subList(0, 5);
                                        recommendListBean.preferredBrandBuineseDto.skuVOList = list;
                                    }*/
                                    for (RowsBean rowsBean : list) {
                                        // 首页推荐数据来源 埋点
                                        rowsBean.zhugeEventName = XyyIoUtil.ACTION_HOME_R_PRODUCT;
                                        rowsBean.setItemType(RowsBean.content_31);
                                    }

                                    RowsBean rb = new RowsBean();
                                    rb.appUrl = recommendListBean.preferredBrandBuineseDto.appUrl;
                                    rb.setItemType(RowsBean.content_32);
                                    list.add(rb);

                                    wrapRecommendBean.setBrandBean(recommendListBean.preferredBrandBuineseDto);
                                    wrapRecommendBeans.add(wrapRecommendBean);
                                }
                            }
                        }
                        if (wrapRecommendBeans.size() > 0) {
                            if (refresh) {
                                if (hasRefresh) {
                                    result.clear();
                                    result.addAll(wrapRecommendBeans);
                                } else {
                                    result.addAll(0, wrapRecommendBeans);
                                }
                            } else {
                                result.addAll(wrapRecommendBeans);
                            }

                        }
                    }
                    mListener.onGetData(result);
                } else {
                    if (mListener != null) {
                        mListener.onGetDataFail(refresh);
                    }
                }
            }
        });
    }

    public interface RecommendListener {
        void onGetData(List<WrapRecommendBean> wrapRecommendBeans);

        void onGetDataFail(boolean refresh);
    }
}
