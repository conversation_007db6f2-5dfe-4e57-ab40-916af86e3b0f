package com.ybmmarket20.activity

import android.Manifest
import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.text.Html
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.util.SparseArray
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.apkfuns.logutils.LogUtils
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.github.mzule.activityrouter.annotation.Router
import com.luck.picture.lib.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import com.luck.picture.lib.config.PictureMimeType
import com.tbruyelle.rxpermissions2.RxPermissions
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.bean.NetError
import com.ybm.app.utils.PermissionDialogUtil
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.activity.jdpay.SET_PAY_PASSWORD_SETTING
import com.ybmmarket20.adapter.FindSameGoodsAdapter
import com.ybmmarket20.adapter.UploadVoucherAdapter
import com.ybmmarket20.adapter.UploadVoucherPicType
import com.ybmmarket20.adapter.UploadVoucherType
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.BoothData
import com.ybmmarket20.bean.BoothDetail
import com.ybmmarket20.bean.CouponInfoBean
import com.ybmmarket20.bean.ENTRY_TYPE_ALBUM
import com.ybmmarket20.bean.FILE_TYPE_IMAGE
import com.ybmmarket20.bean.FILE_TYPE_MIX
import com.ybmmarket20.bean.FILE_TYPE_VIDEO
import com.ybmmarket20.bean.ImPackUrlBean
import com.ybmmarket20.bean.PayResultBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SelectFileBean
import com.ybmmarket20.common.AdDialog4
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.StringUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.YBMPayUtil
import com.ybmmarket20.utils.analysis.AnalysisConst.PayResultSteady.ACTION_PAY_RESULT_COUPON_CLICK
import com.ybmmarket20.utils.analysis.AnalysisConst.PayResultSteady.ACTION_PAY_RESULT_STREAMER_CLICK
import com.ybmmarket20.utils.analysis.AnalysisConst.PayResultSteady.PAY_COUPON_EXPOSURE
import com.ybmmarket20.utils.analysis.AnalysisConst.PayResultSteady.PAY_IMAGE_EXPOSURE
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.utils.analysis.addAnalysisRequestParams
import com.ybmmarket20.utils.analysis.flowDataPageCommoditySearch
import com.ybmmarket20.utils.setTitleAndTag
import com.ybmmarket20.view.PayResultPopWindow
import com.ybmmarket20.view.homesteady.HomeSteadyBannerView
import com.ybmmarket20.view.homesteady.HomeSteadyStreamerView
import com.ybmmarketkotlin.adapter.GoodListAdapterNew
import com.ybmmarketkotlin.viewmodel.PayResultViewModel
import kotlinx.android.synthetic.main.activity_pay_result.rv_result
import kotlinx.android.synthetic.main.activity_pay_result.smartrefresh
import kotlinx.android.synthetic.main.activity_pay_result.tv_tips
import org.json.JSONObject
import java.io.File

@Router("payresultactivity/:orderId/:payway/:amount", "payresultactivity", "payresultactivity/:orderId/:payway/:amount/:orderNo")
class PayResultActivity : PayResultAnalysisActivity() {

    private var mHeaderDatas: MutableList<PayResultBean> = mutableListOf()
    private var mHeaderAdapter: ResultHeaderAdapter? = null

    private var mCouponTitleDatas: MutableList<String> = mutableListOf()
    private var mCouponTitleAdapter: TitleAdapter? = null

    private var mCouponDatas: MutableList<CouponInfoBean> = mutableListOf()
    private var mCouponAdapter: CouponAdapter? = null

    private var mHotDatas: MutableList<BoothData> = mutableListOf()
    private var mHotAdapter: HotAdapter? = null

    private var mGoodListTitleDatas: MutableList<String> = mutableListOf()
    private var mGoodListTitleAdapter: TitleAdapter? = null

    private var mGoodlistDatas: MutableList<RowsBean> = mutableListOf()
//    private var mGoodListAdapter: GoodListAdapterNew? = null
    private var mGoodListAdapter: FindSameGoodsAdapter? = null

    lateinit var concatAdapter: ConcatAdapter

    private var payway: String? = null
    protected var orderId: String? = null
    protected var orderNo: String? = null
    protected var orgId: String? = null

    private lateinit var mViewModel: PayResultViewModel

    //选中图片List
    private val uploadUrlList = arrayListOf<String>()

    //上传图片成功的List
    private val uploadSuccessList = arrayListOf<UploadVoucherPicType>()

    private val uploadVoucherAdapter: UploadVoucherAdapter by lazy {
        UploadVoucherAdapter().apply {
            dataList = arrayListOf()
            uploadVoucherClickListener = object : UploadVoucherAdapter.UploadVoucherClickListener {
                override fun addClick() {
                    val selectFileBean = SelectFileBean(
                            ENTRY_TYPE_ALBUM,
                            if (dataList.size>3) 0 else 4-dataList.size,
                            FILE_TYPE_IMAGE)
                    val rxPermissions = RxPermissions(this@PayResultActivity)
                    if (rxPermissions.isGranted(Manifest.permission.READ_EXTERNAL_STORAGE) && rxPermissions.isGranted(Manifest.permission.WRITE_EXTERNAL_STORAGE) && rxPermissions.isGranted(Manifest.permission.CAMERA)) {
                        requestPermission(selectFileBean)
                    } else {
                        PermissionDialogUtil.showPermissionInfoDialog(
                                this@PayResultActivity,
                                "药帮忙App需要申请存储权限和相机权限，用于拍照并存储照片") {
                            requestPermission(selectFileBean)
                        }
                    }
                }

                override fun deleteClick(data:UploadVoucherPicType) {
                    uploadSuccessList.remove(data)
                    mViewModel.submitVoucher(orderId?:"",SpUtil.getMerchantid()?:"",(uploadSuccessList.map { it.url } as? ArrayList<String>)?: arrayListOf())
                }

                override fun picClick(url:String, view:View) {
                    YbmPhotoViewActivity.launch(
                            this@PayResultActivity,
                            url,
                            view)
                }
            }
        }
    }

    override fun getContentViewId() = R.layout.activity_pay_result

    override fun initData() {
        super.initData()
        payway = intent.getStringExtra("payway")

        orderId = intent.getStringExtra("orderId")
        orderNo = intent.getStringExtra("orderNo")

        setTitle("订单提交成功")

        // 设置title右边的点击图标和点击事件
        setRigthImg({ showHelpDialog(AlertDialogEx.OnClickListener { dialog, button -> dialog.dismiss() }) }, R.drawable.icon_pay_result_help)

        // 第一次进入本页面时  展示的提示弹框
//        if (SpUtil.readInt("show_dialog_in_pay_result", 0) != 1) {
//            SpUtil.writeInt("show_dialog_in_pay_result", 1)
//            showSaveDialog(AlertDialogEx.OnClickListener { _, _ -> PayResultPopWindow().show(tv_tips) })
//        }

        //头部adapter

        mHeaderAdapter = ResultHeaderAdapter(R.layout.activity_pay_result_kotlin_header, mHeaderDatas)

        //
        mCouponTitleDatas.add("更多福利券")
        mCouponTitleAdapter = TitleAdapter(R.layout.activity_pay_result_list_title, mCouponTitleDatas)

        //红包adapter     (在线支付才会有)
        mCouponAdapter = CouponAdapter(mCouponDatas)
        // 热区adapter    (在线支付才会有)
        mHotAdapter = HotAdapter(R.layout.activity_pay_result_hot, mHotDatas)
        //
        mGoodListTitleDatas.add("热销精选")
        mGoodListTitleAdapter = TitleAdapter(R.layout.activity_pay_result_list_title, mGoodListTitleDatas)
        // 商品adapter
        mGoodListAdapter = FindSameGoodsAdapter(mGoodlistDatas, this);//GoodListAdapterNew(R.layout.item_goods_new, mGoodlistDatas)
        rv_result.layoutManager = StaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL)

        concatAdapter = ConcatAdapter(listOf(mHeaderAdapter, mCouponTitleAdapter, mCouponAdapter, mHotAdapter, mGoodListTitleAdapter, mGoodListAdapter))

        rv_result?.adapter = concatAdapter
        rv_result?.layoutManager = WrapLinearLayoutManager(this)

        smartrefresh.setOnLoadMoreListener {
            page++
            getRecommendData()
        }

        mViewModel = ViewModelProvider(this).get(PayResultViewModel::class.java)
        orderNo?.let { mViewModel.getActivityDialog(it, SpUtil.getMerchantid()) }
        mViewModel.queryPWSettingStatus()
        mViewModel.payResultViewModel.observe(this, Observer { t ->
            mHeaderDatas.clear()
            t?.let {
                mHeaderDatas.add(it)
                mHeaderAdapter?.notifyDataSetChanged()
                orderNo = it.payCode?.orderNo
                getRebeatCouponAndBoothData()
            }
        })
        mViewModel.recommendViewModel.observe(this, Observer { data ->
            if (smartrefresh?.isLoading == true) {
                smartrefresh.finishLoadMore()
            }
            data?.let {
                data.rows?.let {
                    mGoodlistDatas.addAll(it)
                }
                if (!isLoadData) {
                    isLoadData = true
                    mFlowData.sId = data.sid
                    mFlowData.spId = data.spId
                    mFlowData.spType = data.spType
                    flowDataPageCommoditySearch(mFlowData)
                    mGoodListAdapter?.flowData = mFlowData
                }
                mGoodListAdapter?.notifyDataSetChanged()
            }
        })
        mViewModel.rebateVoucherViewModel.observe(this, Observer { t ->
            if (t?.list?.size == 0) {
                mCouponTitleAdapter?.let { concatAdapter.removeAdapter(it) }
                mCouponAdapter?.let { concatAdapter.removeAdapter(it) }
                return@Observer
            }
            t?.list?.let {
                mCouponDatas.clear()
                mCouponDatas.addAll(it)
                mCouponAdapter?.notifyDataSetChanged()
            }
        })
        mViewModel.boothDataViewModel.observe(this,
                                              Observer { boothData ->
                                                  boothData?.let {
                                                      mHotDatas.clear()
                                                      mHotDatas.add(boothData)
                                                      mHotAdapter?.notifyDataSetChanged()
                                                  }
                                              })

        mViewModel.submitVoucherLiveData.observe(this) {success->
            if(success){
                uploadVoucherAdapter.dataList = uploadSuccessList.map { UploadVoucherType(picType = it) } as ArrayList<UploadVoucherType>
            }
            LogUtils.d("上传电汇凭证: $success  ")
        }

        mViewModel.getPayResultInfo(getPayResultParams().paramsMap)
        getRecommendData()
        mViewModel.openRedEnvelopeLiveData.observe(this,
                                                   Observer {
                                                       dismissProgress()
                                                   })

        mViewModel.showFingerprintDialogLiveData.observe(this) {
            if (!it) return@observe
            AlertDialogEx(this).setMessage("支付时可通过验证指纹快速完成支付").setCancelButton(
                            "稍后再说",
                            null).setConfirmButton("立即开通") { _, _ ->
                        RoutersUtils.open("ybmpage://paysetting")
                    }.show()
        }
        payway?.let { mViewModel.showFingerprintDialog(it) }

        mViewModel.jDPWSettingLiveData.observe(this) {
            if (it.data != null && it.data.pwSettingStatus != 1) {
                //未设置支付密码
                val dialogEx = AlertDialogEx(this)
                dialogEx.setTitle("")
                    .setMessage("为了您的资金安全，请先设置支付密码")
                    .setCancelButton("稍后设置", "#9494A5",
                        AlertDialogEx.OnClickListener { _, _ -> })
                    .setConfirmButton("去设置",
                        AlertDialogEx.OnClickListener { _, _ ->
                            RoutersUtils.openForResult("ybmpage://setpaypw?settingStatus=$SET_PAY_PASSWORD_SETTING", 100)
                        })
                    .show()
            }
        }
    }

    private fun getRebeatCouponAndBoothData() {
        if (YBMPayUtil.PAY_WXSDK == payway || YBMPayUtil.PAY_WX_MINI == payway
            || YBMPayUtil.PAY_YLSDK == payway
            || YBMPayUtil.PAY_ALISDK == payway || YBMPayUtil.PAY_ALI_MINI == payway
            || YBMPayUtil.PAY_BTSDK == payway
        ) {
            getRebeatCoupon()
            getBoothData()
        } else {
            mCouponTitleAdapter?.let { concatAdapter.removeAdapter(it) }
            mCouponAdapter?.let { concatAdapter.removeAdapter(it) }
            mHotAdapter?.let { concatAdapter.removeAdapter(it) }
        }
    }

    private fun getRebeatCoupon() {
        orderNo?.let {
            mViewModel.getRebateVoucherList(RequestParams().apply {
                put("orderNo", it)
                put("merchantId", SpUtil.getMerchantid())
            }.paramsMap)
        }
    }

    /**
     * 获取个人中心展位数据
     */
    private fun getBoothData() {
        val requestParams = RequestParams()
        requestParams.put("sceneType", "6") //CMS场景类型。1首页，3个人中心，4排行榜，6支付结果页
        mViewModel.getBoothData(requestParams.paramsMap)
    }

    private fun getPayResultParams(): RequestParams = RequestParams
        .newBuilder()
        .url(AppNetConfig.ORDERPAYRESULT)
        .addParam("paycode", payway)
        .addParam("orderId", orderId)
        .addParam("showdialog", "1")
        .addParam("merchantId", merchant_id)
        .addParam("orderNo", orderNo)
        .build()


    private var page = 1
    private val limit = 10
    private var isLoadData = false  //是否加载过数据（是否上传过埋点数据）

    /**
     * 获取推荐数据
     */
    private fun getRecommendData() {
        val params = getRecommendParams()
        if (isLoadData) addAnalysisRequestParams(params, mFlowData)

        mViewModel.getRecommendGoodlist(params.paramsMap)
    }

    private fun getRecommendParams(): RequestParams {
        val params = RequestParams()
        params.put("offset", page.toString() + "")
        params.put("limit", limit.toString() + "")
        params.put("pageType", "6") //2 发现页为你推荐 3 首页为你推荐 4 商品详情页为你推荐5 购物车页推荐 6 付款结果页推荐 7 我的-个人中心页内推荐
        params.put("csuId", SpUtil.getMerchantid())
        params.put("merchantId", SpUtil.getMerchantid())
        params.put("timestamp", System.currentTimeMillis().toString() + "")
        return params
    }

    private fun showSaveDialog(listener: AlertDialogEx.OnClickListener) {
        val dialogEx = AlertDialogEx(this)
        dialogEx.setMessage("请您在汇款时备注药帮忙订单编号，这将会很大程度上缩短我们的核款时间并能尽快为您安排发货。").setCancelButton("关闭",
            AlertDialogEx.OnClickListener { dialog, button -> dialog.dismiss() }).setCancelable(false).setConfirmButton("查看操作示例", listener)
            .setCanceledOnTouchOutside(false).show()
    }

    private fun showHelpDialog(listener: AlertDialogEx.OnClickListener) {
        val dialogEx = AlertDialogEx(this)
        dialogEx.setTitle("提示")
            .setMessage("商品签收时如发现商品少发、错发、包装破损、商品与APP商品图片不符等问题，请第一时间与我们取得联系")
            .setCancelable(false).setConfirmButton("确定", listener).setCanceledOnTouchOutside(false).show()
    }


    /**
     * @param voucherTemplateId
     * @return
     */
    private fun getVoucherParams(voucherTemplateId: String): RequestParams? {
        val merchantid = SpUtil.getMerchantid()
        val mVoucherParams = RequestParams()
        mVoucherParams.url = AppNetConfig.RECEIVE_USABLE_VOUCHER
        mVoucherParams.put("merchantId", merchantid)
        mVoucherParams.put("voucherTemplateId", voucherTemplateId)
        return mVoucherParams
    }

    /**
     * 点击立即领取，改变当前item的状态为2
     *
     * @param voucherTemplateId
     * @param position
     */
    private fun getVoucher(voucherTemplateId: String, position: Int) {
        HttpManager.getInstance().post(getVoucherParams(voucherTemplateId), object : BaseResponse<BaseBean<*>?>() {
            override fun onSuccess(content: String?, obj: BaseBean<BaseBean<*>?>?, t: BaseBean<*>?) {
                if (obj != null && obj.isSuccess) {
                    mCouponDatas?.get(position)?.activityState = 3
                    mCouponAdapter?.notifyItemChanged(position)
                }
            }

            override fun onFailure(error: NetError) {
                super.onFailure(error)
            }
        })
    }

    override fun getPageName(): String = if (YBMPayUtil.PAY_WXSDK == payway || YBMPayUtil.PAY_WX_MINI == payway || YBMPayUtil.PAY_YLSDK == payway || YBMPayUtil.PAY_ALISDK == payway || YBMPayUtil.PAY_ALI_MINI == payway || YBMPayUtil.PAY_BTSDK == payway) {
        XyyIoUtil.PAGE_SUCCESS_ONLINE
    } else {
        XyyIoUtil.PAGE_SUCCESS_UNDERLINE
    }

    /**
     * 从相册打开
     *
     * @param selectFileBean
     */
    private fun selectFromAlbum(selectFileBean: SelectFileBean?) {
        if (selectFileBean == null) return
        var mimeType = selectFileBean.fileType
        if (mimeType == FILE_TYPE_MIX) {
            mimeType = PictureMimeType.ofAll()
        } else if (mimeType == FILE_TYPE_VIDEO) {
            mimeType = PictureMimeType.ofVideo()
        } else if (mimeType == FILE_TYPE_IMAGE) {
            mimeType = PictureMimeType.ofImage()
        }
        PictureSelector.create(mySelf).openGallery(mimeType).maxSelectNum(selectFileBean.count)
                .minSelectNum(1).imageSpanCount(4).setSelectVideoMaxSize(20).compress(true)
                .isCamera(true).setVideoType("video/mp4").canSelectedAudio(false)
                .selectionMode(PictureConfig.MULTIPLE).forResult(PictureConfig.CHOOSE_REQUEST)
    }

    @SuppressLint("CheckResult")
    private fun requestPermission(selectFileBean: SelectFileBean) {
        val rxPermissions = RxPermissions(this) // where this is an Activity instance
        rxPermissions.request(
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE,
                Manifest.permission.CAMERA).subscribe({ granted: Boolean ->
                                                          if (granted) { // 在android 6.0之前会默认返回true
                                                              selectFromAlbum(selectFileBean)
                                                          } else { // 未获取权限
                                                              Toast.makeText(
                                                                      mySelf,
                                                                      "您没有授权该权限，请在设置中打开授权",
                                                                      Toast.LENGTH_LONG).show()
                                                          }
                                                      }) { throwable: Throwable? -> }
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(
                requestCode,
                resultCode,
                data)
        if (resultCode == RESULT_OK && requestCode == PictureConfig.CHOOSE_REQUEST) { // 选择图片或视频
            val images = PictureSelector.obtainMultipleResult(data)
            if (images.isNullOrEmpty()) {
                ToastUtils.showShort("未找到图片")
                return
            }

            for (localMedia in images) {
                if (localMedia.compressPath.isNullOrEmpty()) {
                    uploadUrlList.add(localMedia.path ?: "")
                } else {
                    uploadUrlList.add(localMedia.compressPath ?: "")
                }
            }
            showProgress()
            uploadCircle(uploadUrlList)
        }
    }

    /**
     * 上传图片
     *
     * @param pathList
     */
    fun uploadCircle(pathList: ArrayList<String>) {
        if (pathList.isEmpty()) {
            uploadSuccessList.clear()
            uploadUrlList.clear()
            return
        }
        val path: String = pathList.removeFirst()
        val file = File(path)
        if (!file.exists()) {
            ToastUtils.showShort("上传文件不存在")
            return
        }
        val params = RequestParams()
        if (!TextUtils.isEmpty(SpUtil.getMerchantid())) {
            params.put(
                    "merchantId",
                    SpUtil.getMerchantid())
        }
        params.put(
                "file",
                file)
        HttpManager.getInstance().post(AppNetConfig.TASK_CENTER_UPLOAD_FILE,
                                       params,
                                       object : BaseResponse<List<String>>() {
                                           override fun onSuccess(
                                                   content: String?, obj: BaseBean<List<String>?>?,
                                                   bean: List<String>?) {
                                               if (bean != null && bean.size > 0) {
                                                   uploadSuccessList.add(UploadVoucherPicType(bean[0]))
                                                   if (pathList.size > 0) {
                                                       uploadCircle(pathList)
                                                   } else {
                                                       dismissProgress()
                                                       //上传本地图片完成 上传电汇凭证
                                                       mViewModel.submitVoucher(orderId?:"",SpUtil.getMerchantid()?:"",(uploadSuccessList.map { it.url } as? ArrayList<String>)?: arrayListOf())
                                                   }
                                                   LogUtils.d("单张上传成功  " + path + " - " + bean[0]) //需要保持上传成功服务器返回的地址
                                               } else {
                                                   dismissProgress()
                                               }
                                           }

                                           override fun onFailure(error: NetError) {
                                               dismissProgress()
                                               LogUtils.d("单张上传失败  " + path) //需要保持上传成功服务器返回的地址
                                           }
                                       })
    }

    inner class ResultHeaderAdapter : YBMBaseAdapter<PayResultBean> {

        var btnHome: RoundTextView? = null
        var btnDetail: TextView? = null
        var llBankInfo: LinearLayout? = null
        var tvAction: TextView? = null
        var tvBankInfo: TextView? = null
        var tv_bank_info01: TextView? = null
        var tv_bank_info02: TextView? = null
        var tv_bank_info03: TextView? = null
        var tvBankTips1: TextView? = null
        var tvBankTips2: TextView? = null
        var tvBankTips3: TextView? = null
        var tvBankTipsHead: TextView? = null

        constructor(layoutResId: Int, data: MutableList<PayResultBean>?) : super(layoutResId, data)

        override fun bindItemView(baseViewHolder: YBMBaseHolder, baseBean: PayResultBean?) {

            val tvTotal = baseViewHolder.getView<TextView>(R.id.tv_total)
            val tvPayStatus = baseViewHolder.getView<TextView>(R.id.tv_pay_status)
            val tvPayway = baseViewHolder.getView<TextView>(R.id.tv_payway)
            val ll_kefu = baseViewHolder.getView<ConstraintLayout>(R.id.ll_kefu)
            val tv_pay_result_check = baseViewHolder.getView<TextView>(R.id.tv_pay_result_check)
            val ll_electronic_type = baseViewHolder.getView<LinearLayout>(R.id.ll_electronic_type)
            val ll_pager_type = baseViewHolder.getView<LinearLayout>(R.id.ll_pager_type)

            val clThirdCompanyKefu = baseViewHolder.getView<ConstraintLayout>(R.id.cl_third_company_kefu) //商家客服
            val clPopKefuAll = baseViewHolder.getView<ConstraintLayout>(R.id.cl_pop_kefu_all) //商家客服
            val clPopKefu = baseViewHolder.getView<ConstraintLayout>(R.id.cl_pop_kefu)
            val clCallPhone = baseViewHolder.getView<ConstraintLayout>(R.id.cl_call_phone) //平台拨打电话
            val tvViewExamples = baseViewHolder.getView<TextView>(R.id.tv_view_examples) //查看示例
            val rvUploadVoucher = baseViewHolder.getView<RecyclerView>(R.id.rv_upload_voucher) //查看示例

            //线下支付的头部设置, 需要主要的是为了减少改动和出问题的风险 原本的设值还是保留（不过会被整体隐藏掉换成线下支付的样式需注意）
            val clNormalPayHeader = baseViewHolder.getView<ConstraintLayout>(R.id.cl_normal_pay_header)
            val clOfflinePayHeader = baseViewHolder.getView<ConstraintLayout>(R.id.cl_offline_pay_header)
            val tvOfflinePayContent = baseViewHolder.getView<TextView>(R.id.tv_offline_pay_content)
            val tvOfflinePayAmountContent = baseViewHolder.getView<TextView>(R.id.tv_offline_pay_amount_content)
            val tvOfflinePayViewOrder = baseViewHolder.getView<TextView>(R.id.tv_offline_pay_view_order)
            val tvOfflinePayBackHome = baseViewHolder.getView<TextView>(R.id.tv_offline_pay_back_home)

            btnHome = baseViewHolder.getView(R.id.btn_home)
            btnDetail = baseViewHolder.getView(R.id.btn_detail)
            llBankInfo = baseViewHolder.getView(R.id.ll_bank_info)
            tvAction = baseViewHolder.getView(R.id.tv_action)
            tvBankInfo = baseViewHolder.getView(R.id.tv_bank_info)
            tv_bank_info01 = baseViewHolder.getView(R.id.tv_bank_info_01)
            tv_bank_info02 = baseViewHolder.getView(R.id.tv_bank_info_02)
            tv_bank_info03 = baseViewHolder.getView(R.id.tv_bank_info_03)
            tvBankTips1 = baseViewHolder.getView(R.id.tv_bank_tips1)
            tvBankTips2 = baseViewHolder.getView(R.id.tv_bank_tips2)
            tvBankTips3 = baseViewHolder.getView(R.id.tv_bank_tips3)
            tvBankTipsHead = baseViewHolder.getView(R.id.tv_bank_tips_head)
            val tvDiscount = baseViewHolder.getView<TextView>(R.id.tvDiscount)
            val tvPayStatusDescribe = baseViewHolder.getView<TextView>(R.id.tv_pay_status_describe)
            val llCopy = baseViewHolder.getView<LinearLayout>(R.id.ll_copy)


//            val amount = intent.getStringExtra("amount")
//            tvTotal.text = "¥$amount"
            tvTotal.text = "¥${baseBean?.payCode?.orderAmount?:""}"
            setTitle(baseBean?.title)
            tvPayStatus.text = "${baseBean?.stateName} ¥${baseBean?.payCode?.orderAmount?:""}"
            tvPayway.text = baseBean?.payTypeName

            tvDiscount.visibility = if (baseBean?.payDiscountTips.isNullOrEmpty()) View.GONE else View.VISIBLE
            tvDiscount.text = baseBean?.payDiscountTips

            tvPayStatusDescribe.visibility = if (baseBean?.tips.isNullOrEmpty()) View.GONE else View.VISIBLE
            tvPayStatusDescribe.text = baseBean?.tips

//
//            setTitle("订单支付成功")
//            tvPayStatus.text = "支付成功~"
//            if (YBMPayUtil.PAY_WXSDK == payway || YBMPayUtil.PAY_WX_MINI == payway) {
//                tvPayway.text = "微信支付"
//            } else if (YBMPayUtil.PAY_YLSDK == payway) {
//                tvPayway.text = "银联支付"
//            } else if (YBMPayUtil.PAY_ALISDK == payway || YBMPayUtil.PAY_ALI_MINI == payway) {
//                tvPayway.text = "支付宝"
//            } else if (YBMPayUtil.PAY_BTSDK == payway) {
//                tvPayway.text = "广发白条"
//            } else if (YBMPayUtil.PAY_PING_AN == payway) {
//                tvPayway.text = "平安贷"
            //            } else {
            //                setTitle("订单提交成功")
            //                tvPayStatus.text = "提交成功~"
            //                if (YBMPayUtil.PAY_PAD == payway) {
            //                    tvPayway.text = resources.getString(R.string.payment_tv04)
            //                } else {
            //                    tvPayway.text = resources.getString(R.string.payment_bol)
            //                }
            //            }

            btnDetail?.setOnClickListener { finish() }
            btnHome?.setOnClickListener {
                RoutersUtils.open("ybmpage://main/0")
                finish()
            }

            ll_kefu?.setOnClickListener { RoutersUtils.telKefu(true) }
            clCallPhone?.setOnClickListener { RoutersUtils.telKefu(true) }
            clPopKefu?.setOnClickListener {
                sendOnLineService("0",baseBean)
            }
            clThirdCompanyKefu?.setOnClickListener {
                sendOnLineService("1",baseBean)
            }

            tv_pay_result_check?.setOnClickListener { PayResultPopWindow().show(tv_pay_result_check) }
            ll_electronic_type?.setOnClickListener { orgId?.let { RoutersUtils.open("ybmpage://shopactivity/$orgId/showaccount") } }
            ll_pager_type?.setOnClickListener { orgId?.let { RoutersUtils.open("ybmpage://shopactivity/$orgId/showaccount") } }


            baseBean?.payCode?.message?.let {
                tv_tips.text = baseBean.payCode.message
                tv_tips.visibility = View.VISIBLE
            }

            if (YBMPayUtil.PAY_TRAN == payway) {
                clOfflinePayHeader.isVisible = true
                clNormalPayHeader.isVisible = false
                ll_kefu.isVisible = false
                tvOfflinePayContent.text = baseBean?.title ?: ""
                tvOfflinePayAmountContent.text = baseBean?.payCode?.orderAmount?.let { "应付金额 ¥${it}" }
                        ?: ""
                offlinePayBtnClick(
                        baseBean,
                        tvOfflinePayViewOrder,
                        tvOfflinePayBackHome)
                if (baseBean?.isTelegraphicTransferBusiness == true) { //电汇商家
                    clThirdCompanyKefu.isVisible = true
                    clPopKefuAll.isVisible = false
                } else { //电汇平台
                    clThirdCompanyKefu.isVisible = false
                    clPopKefuAll.isVisible = true
                }

                //上传电汇凭证模块 start=========
                tvViewExamples.setOnClickListener {
                    YbmPhotoViewActivity.launch(
                            this@PayResultActivity,
                            R.drawable.icon_example_voucher,
                            it)
                }

                rvUploadVoucher.layoutManager = GridLayoutManager(
                        this@PayResultActivity,
                        3)
                rvUploadVoucher.adapter = uploadVoucherAdapter
                //上传电汇凭证模块 end=========

                showTran(
                        baseBean,
                        llCopy)
            } else {
                ll_kefu.isVisible = true
                clThirdCompanyKefu.isVisible = false
                clPopKefuAll.isVisible = false
                clOfflinePayHeader.isVisible = false
                clNormalPayHeader.isVisible = true
                llBankInfo?.visibility = View.GONE
            }
            showPad(baseBean)
            baseBean?.let {
                showDialDialog(it)
            }
        }


        /**
         *  设置 查看订单 和 回到首页 的点击事件
         */
        fun showPad(baseBean: PayResultBean?) {
            if (baseBean == null) return
            if (baseBean?.payCode != null) {
                btnHome?.setOnClickListener {
                    if (baseBean?.payCode?.actionLeft?.isEmpty() == true) {
                        RoutersUtils.open(baseBean.payCode.actionLeft)
                    } else {
                        RoutersUtils.open("ybmpage://main/0")
                    }
                    finish()
                }

                btnDetail?.setOnClickListener {
                    baseBean?.payCode?.actionRigth?.let {
                        RoutersUtils.open(it)
                    }
                    finish()
                }
            }
        }

        /**
         * 线下支付
         * 设置 查看订单 和 回到首页 的点击事件
         * @param baseBean PayResultBean?
         */
        fun offlinePayBtnClick(baseBean: PayResultBean?, btnViewOrder: View?, btnBackHome: View?) {
            baseBean?.payCode?.let {
                btnBackHome?.setOnClickListener {
                    if (baseBean?.payCode?.actionLeft?.isEmpty() == true) {
                        RoutersUtils.open(baseBean.payCode.actionLeft)
                    } else {
                        RoutersUtils.open("ybmpage://main/0")
                    }
                    finish()
                }

                btnViewOrder?.setOnClickListener {
                    baseBean?.payCode?.actionRigth?.let {
                        RoutersUtils.open(it)
                    }
                    finish()
                }
            }
        }

        /**
         * 线下转账
         */
        fun showTran(baseBean: PayResultBean?, llCopy: View) {
            if (baseBean == null) {
                return
            }
            if (baseBean.transferInfo != null && !TextUtils.isEmpty(baseBean.transferInfo.msg) && !TextUtils.isEmpty(baseBean.transferInfo.action)) {
                llBankInfo?.setVisibility(View.GONE)
                tvAction?.visibility = View.VISIBLE
                tvAction?.setText(Html.fromHtml(baseBean.transferInfo.msg))
                tvAction?.setOnClickListener(View.OnClickListener { RoutersUtils.open(baseBean.transferInfo.action) })
            } else {
                if (baseBean.transferInfo != null) {
                    val mContent = """
                    公司名称：${baseBean.transferInfo.companyInfo}
                    开户银行：${baseBean.transferInfo.bankInfo}
                    银行账户：${baseBean.transferInfo.bankAccount}
                    """.trimIndent()
                    llBankInfo?.visibility = View.VISIBLE
                    tvBankInfo?.text = mContent
                    tv_bank_info01?.text = baseBean.transferInfo.companyInfo
                    tv_bank_info02?.text = baseBean.transferInfo.bankInfo
                    tv_bank_info03?.text = baseBean.transferInfo.bankAccount
                    tvBankTips1?.text = baseBean.transferInfo.explainOne
                    tvBankTips2?.text = baseBean.transferInfo.explainTwo
                    tvBankTips3?.text = baseBean.transferInfo.explainThree
                    val str = "请您在汇款时备注药帮忙订单编号，这将会很大程度上缩短我们的核款时间并能尽快为您安排发货。"
                    tvBankTipsHead?.text = if (!TextUtils.isEmpty(baseBean.transferInfo.explainZero)) baseBean.transferInfo.explainZero else str

                    llCopy.setOnClickListener {
                        val clipboardManager = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                        val clipData = ClipData.newPlainText(
                                "text",
                                mContent)
                        clipboardManager.setPrimaryClip(clipData)
                        ToastUtils.showShort("复制成功")
                    }
                }
            }
        }

        /*
     * 在线客服
     * */
        private fun sendOnLineService(isThirdCompany: String, payResultBean: PayResultBean?) {
            val params = RequestParams()
            params.put(
                    "isThirdCompany",
                    isThirdCompany)
            HttpManager.getInstance()
                    .post(AppNetConfig.GET_IM_PACKURL,
                                           params,
                                           object : BaseResponse<ImPackUrlBean?>() {
                                               override fun onSuccess(
                                                       content: String,
                                                       obj: BaseBean<ImPackUrlBean?>?,
                                                       baseBean: ImPackUrlBean?) {
                                                   if (obj != null && obj.isSuccess) {
                                                       if (baseBean != null) {
                                                           if (isThirdCompany == "1") { //商家客服
                                                               RoutersUtils.open(
                                                                       RoutersUtils.getRouterPopCustomerServiceUrl(
                                                                               baseBean.IM_PACK_URL,
                                                                               payResultBean?.orgId?:"",
                                                                               payResultBean?.orderNo?:"",
                                                                               payResultBean?.companyName?:""))
                                                           } else { //平台客服
                                                               RoutersUtils.open(
                                                                       RoutersUtils.getRouterYbmOrderCustomerServiceUrl(
                                                                               baseBean.IM_PACK_URL,
                                                                               payResultBean?.orderNo?:""))
                                                           }
                                                       }
                                                   }
                                               }
                                           })
        }

        /**
         *  大转盘数据配置
         */
        private fun showDialDialog(baseBean: PayResultBean) {
            if (TextUtils.isEmpty(baseBean.bigWheelText) || TextUtils.isEmpty(baseBean.bigWheelUrl)) {
                return
            }

            AdDialog4.showDialog(
                    baseBean.bigWheelText,
                    baseBean.bigWheelUrl)
        }
    }


    inner class TitleAdapter : YBMBaseAdapter<String> {
        constructor(layoutResId: Int, data: MutableList<String>?) : super(layoutResId, data)

        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: String?) {
            baseViewHolder?.getView<TextView>(R.id.tv_title)?.text = t?.let { it }
        }
    }

    // 该券状态和券列表的状态不通用，要注意。。。。  优惠券数据结果存在N套了....
    inner class CouponAdapter(data: MutableList<CouponInfoBean>?) : BaseMultiItemQuickAdapter<CouponInfoBean, YBMBaseHolder>(data) {

        init {
            addItemType(1, R.layout.item_show_bottom_car_coupon_dialog_list)
            addItemType(2, R.layout.item_pay_result_red_envelope)
        }

        // 优惠券曝光缓存
        private val traceCouponData = SparseArray<String>()

        private fun bindBaseItem(ybmBaseHolder: YBMBaseHolder, voucherListBean: CouponInfoBean) {
            val tvPriceUnit = ybmBaseHolder.getView<TextView>(R.id.tv_PriceUnit)
            val tvDiscountUnit = ybmBaseHolder.getView<TextView>(R.id.tv_discount_unit)
            val tvCouponAmount = ybmBaseHolder.getView<TextView>(R.id.tv_coupon_amount)
            val tvCouponTitle = ybmBaseHolder.getView<TextView>(R.id.tv_coupon_title)
            val tvCouponSubTitle = ybmBaseHolder.getView<TextView>(R.id.tv_coupon_subtitle)
            val tvCouponLimt = ybmBaseHolder.getView<TextView>(R.id.tv_coupon_limit)
            val tvCouponFullReduceMax = ybmBaseHolder.getView<TextView>(R.id.tv_coupon_full_reduce_max)
            val tvAmountTips = ybmBaseHolder.getView<TextView>(R.id.tv_amount_tips)
            val tvCouponDate = ybmBaseHolder.getView<TextView>(R.id.tv_coupon_date)
            setTitleAndTag(
                mContext,
                voucherListBean.voucherType,
                voucherListBean.templateName,
                voucherListBean.voucherTypeDesc,
                tvCouponTitle
            )
            tvCouponSubTitle.text = voucherListBean.voucherTitle
            //tvCouponLimt.text = voucherListBean.voucherInstructions

            if (voucherListBean.voucherState == 1) {
                tvPriceUnit.visibility = View.GONE
                tvDiscountUnit.visibility = View.VISIBLE
                val discount = UiUtils.transform2Int(voucherListBean.discount)
                tvCouponAmount.text = discount
            } else {
                tvPriceUnit.visibility = View.VISIBLE
                tvDiscountUnit.visibility = View.GONE
                tvCouponAmount.text = UiUtils.transformInt(voucherListBean.moneyInVoucher)
            }


            // 最高减 显示文案
            if (!voucherListBean.maxMoneyInVoucherDesc.isNullOrEmpty()) {
                tvCouponFullReduceMax.visibility = View.VISIBLE
                tvCouponFullReduceMax.text = voucherListBean.maxMoneyInVoucherDesc
            } else {
                tvCouponFullReduceMax.visibility = View.GONE
            }

            if ((voucherListBean.validDateToString?.isNullOrEmpty() == false) && (voucherListBean.expireDateToString?.isNullOrEmpty() == false)) {
                tvCouponDate.text = voucherListBean.validDateToString + "-" + voucherListBean.expireDateToString
            }

            //优惠券 适用金额限制条件
            ybmBaseHolder.setText(R.id.tv_coupon_full_reduce, voucherListBean.minMoneyToEnableDesc)

            tvAmountTips.visibility = View.GONE

            val rtv = ybmBaseHolder.getView<RoundTextView>(R.id.tv_coupon_immediate_use)
            val tvGetItNow = ybmBaseHolder.getView<TextView>(R.id.tv_get_it_now)
            rtv.setOnClickListener { v: View? ->
                //去凑单
                if (TextUtils.isEmpty(voucherListBean.appUrl)) {
                    voucherListBean.appUrl = "ybmpage://couponavailableactivity/" + voucherListBean.templateId + "/" + 1
                }
                RoutersUtils.open(voucherListBean.appUrl)
                LogUtils.e("snow ACTION_PAY_RESULT_COUPON_CLICK")
                XyyIoUtil.track(ACTION_PAY_RESULT_COUPON_CLICK, JSONObject().apply {
                    put("id", voucherListBean.templateId)
                    put("text", "立即使用")
                    put("link", voucherListBean.appUrl)
                })
            }
            tvGetItNow.setOnClickListener { v: View? ->
                //立即领取
                voucherListBean.templateId?.let {
                    getVoucher(it, ybmBaseHolder.bindingAdapterPosition)
                }
                LogUtils.e("snow ACTION_PAY_RESULT_COUPON_CLICK")
                XyyIoUtil.track(ACTION_PAY_RESULT_COUPON_CLICK, JSONObject().apply {
                    put("id", voucherListBean.templateId)
                    put("text", "立即领取")
                })
            }
            tvGetItNow.visibility = if (voucherListBean.activityState == 2) View.VISIBLE else View.GONE
            rtv.visibility = if (voucherListBean.activityState == 3) View.VISIBLE else View.GONE

            if (traceCouponData[ybmBaseHolder.bindingAdapterPosition] == null) {
                traceCouponData?.put(ybmBaseHolder.bindingAdapterPosition, voucherListBean.templateId)
                // 优惠券曝光
                LogUtils.e("snow PAY_COUPON_EXPOSURE")
                XyyIoUtil.track(PAY_COUPON_EXPOSURE, JSONObject().apply {
                    put("id", voucherListBean.templateId)
                    orderNo?.let {
                        put("orderNo", orderNo)
                    }
                })
            }
        }

        override fun convert(baseHolder: YBMBaseHolder, voucherListBean: CouponInfoBean?) {
            if (voucherListBean == null) return
            when(voucherListBean.itemType) {
                1 -> handleCoupon(baseHolder, voucherListBean)
                2 -> handleRedEnvelope(baseHolder, voucherListBean)
            }
        }
        private fun handleCoupon(baseHolder: YBMBaseHolder, voucherListBean: CouponInfoBean) {
            baseHolder.setGone(R.id.rv_cart_bottom_coupon_goods, false)
            bindBaseItem(baseHolder, voucherListBean)
        }

        private fun handleRedEnvelope(baseHolder: YBMBaseHolder, item: CouponInfoBean) {
            baseHolder.setText(R.id.tv_red_envelope_title, item.templateName)
            if (!TextUtils.isEmpty(item.validDateToString) && !TextUtils.isEmpty(item.expireDateToString)) {
                baseHolder.setText(R.id.tv_red_envelope_time, "${item.validDateToString}-${item.expireDateToString}")
            } else {
                baseHolder.setText(R.id.tv_red_envelope_time, item.validDayStr)
            }

            if (item.activityState == 5) {
                //已抢光
                baseHolder.setText(R.id.tv_red_envelope_gold, "已抢光")
                baseHolder.setVisible(R.id.tv_red_envelope_gold, true)
                baseHolder.setVisible(R.id.tv_red_envelope_btn, false)
            } else if (item.activityState == 2) {
                //立即领取
                baseHolder.setVisible(R.id.tv_red_envelope_gold, false)
                baseHolder.setVisible(R.id.tv_red_envelope_btn, true)
                baseHolder.getView<TextView>(R.id.tv_red_envelope_btn).setOnClickListener {
                    XyyIoUtil.track("action_payPopupFloor_Click", hashMapOf(
                            "activity_id" to (item.marketingId?: ""),
                            "redpacket_id" to (item.templateId?: "")
                    ))
                    openRedEnvelope(mapOf(
                            "redPacketTemplateId" to (item.templateId?: ""),
                            "marketingId" to (item.marketingId?: ""),
                            "orderNo" to (orderNo?: "")
                    ), baseHolder.bindingAdapterPosition)
                }
            } else if (item.activityState == 3) {
                //立即使用
                val builder = SpannableStringBuilder("￥").run {
                    setSpan(AbsoluteSizeSpan(12, true), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                    append(StringUtil.removeZeroAfterDot(UiUtils.transform(item.redPacketOriginMoney)))
                }
                baseHolder.setText(R.id.tv_red_envelope_gold, builder)
                baseHolder.setVisible(R.id.tv_red_envelope_gold, true)
                baseHolder.setVisible(R.id.tv_red_envelope_btn, false)
            }
        }

        /**
         * 领取红包
         */
        private fun openRedEnvelope(map: Map<String, String>, position: Int) {
            showProgress()
            mViewModel.openRedEnvelope(map, position)
        }

    }

    inner class HotAdapter : YBMBaseAdapter<BoothData> {

        // 优惠券曝光缓存
        private val traceHotData = SparseArray<String>()

        constructor(layoutResId: Int, data: MutableList<BoothData>?) : super(layoutResId, data)

        override fun bindItemView(baseViewHolder: YBMBaseHolder, boothData: BoothData?) {

            var mineBanner = baseViewHolder.getView<HomeSteadyBannerView>(R.id.hsbv_minebanner)
            var mineStreamer = baseViewHolder.getView<HomeSteadyStreamerView>(R.id.hssv_minestreamer)

            val boothDetail: BoothDetail? = boothData?.detail
            if (boothDetail?.bannerDto == null && boothDetail?.steamerDto == null) {
                mineBanner?.visibility = View.GONE
                mineStreamer?.visibility = View.GONE
                return
            }
            if (boothDetail?.type == 1) {
                //轮播类型
                mineStreamer?.visibility = View.GONE
                mineBanner?.visibility = View.VISIBLE
                if (boothDetail.bannerDto == null) return
                mineBanner?.setData(boothDetail.bannerDto?.imageDtos)
            } else if (boothDetail?.type == 2) {
                //胶囊类型
                boothDetail.steamerDto.isHotZone = true
                mineBanner?.visibility = View.GONE
                mineStreamer?.visibility = View.VISIBLE
                if (boothDetail.steamerDto == null) return
                mineStreamer?.setStreamer(boothDetail.steamerDto)
                mineStreamer?.setAnalysisCallback(::streamerAnalysisCallback)
            }

            // 横幅广告曝光
            if (traceHotData[baseViewHolder.bindingAdapterPosition] == null) {
                LogUtils.e("snow PAY_IMAGE_EXPOSURE")
                traceHotData.put(baseViewHolder.bindingAdapterPosition, PAY_IMAGE_EXPOSURE)
                XyyIoUtil.track(PAY_IMAGE_EXPOSURE)
            }

        }


        /**
         * 胶囊位入口点击埋点回调
         */
        fun streamerAnalysisCallback(action: String, text: String) {
            LogUtils.e("snow ACTION_PAY_RESULT_STREAMER_CLICK")
            XyyIoUtil.track(ACTION_PAY_RESULT_STREAMER_CLICK, JSONObject().apply {
                put("text", text)
                put("link", action)
            })
        }


    }


}