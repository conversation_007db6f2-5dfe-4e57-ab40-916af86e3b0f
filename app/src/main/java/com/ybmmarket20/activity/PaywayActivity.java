package com.ybmmarket20.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Color;
import android.os.CountDownTimer;
import android.os.Handler;
import android.text.Html;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.RelativeSizeSpan;
import android.view.KeyEvent;
import android.view.View;
import android.widget.CheckedTextView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.ybm.app.bean.NetError;
import com.ybm.app.common.SmartExecutorManager;
import com.ybm.app.utils.SpUtil;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.PayConfig;
import com.ybmmarket20.bean.PayConfigBean;
import com.ybmmarket20.bean.PayDialogBean;
import com.ybmmarket20.bean.PayDialogBtnList;
import com.ybmmarket20.bean.ShowPopBean;
import com.ybmmarket20.bean.YBMPayEntity;
import com.ybmmarket20.bean.YBMPayParam;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.ViewOnClickListener;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.ImageUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.StringUtil;
import com.ybmmarket20.utils.YBMPayUtil;
import com.ybmmarket20.view.VerificationCodeDialog;
import com.ybmmarket20.viewmodel.PayDialogViewModel;

import org.greenrobot.eventbus.EventBus;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;

/**
 * 收银台，支付方式选择并支付
 */
//@Router({"paywayactivity/:orderId/:orderInfo/:amount/:payway", "paywayactivity/:orderId/:amount/:payway", "paywayactivity/:orderId/:amount/:payRoute", "paywayactivity/:orderId/:amount/:orderNo/:payRoute", "paywayactivity"})
@Deprecated
public class PaywayActivity extends BaseActivity implements View.OnClickListener {

    //支付类型-支付宝
    public static final String PAY_TYPE_ALIPAY = "alipay";
    //支付类型- 微信
    public static final String PAY_TYPE_WECHAT_PAY = "weixin";
    //支付类型-银联
    public static final String PAY_TYPE_UNION_PAY = "unionpay";
    //支付类型-白条
    public static final String PAY_TYPE_BT_PAY = "gfbaitiao";

    // 展示支付类型
    public static final String ICON_TYPE_ALIPAY = "alipay";
    public static final String ICON_TYPE_ALIPAY_CREDIT = "pcredit";
    public static final String ICON_TYPE_WECHAT_PAY = "weixin";
    public static final String ICON_TYPE_UNION_PAY = "unionpay";
    public static final String ICON_TYPE_BT_PAY = "gfbaitiao";
    public static final String ICON_TYPE_PINGANCREDIT_PAY = "pingancredit";

    @Bind(R.id.tv_total)
    TextView tvTotal;
    @Bind(R.id.btn_pay)
    TextView btnPay;
    @Bind(R.id.tv_timer)
    TextView tvTimer;
    @Bind(R.id.tv_pay_way_tips)
    TextView tvTips;
    @Bind(R.id.ll_pay_item)
    LinearLayout llPayGroup;
    @Bind(R.id.tv_fold_pay_items)
    TextView tvFoldPayItems;

    RadioButton rbAlipay;
    RadioButton rbWx;
    RadioButton rbBank;
    RadioButton rbBt;
    LinearLayout llBtAgree;
    CheckedTextView ctvBtAgree;
    TextView tv_protocal_link;

    private TimeCount time;
    public static String payWay = "";
    private YBMPayUtil.PaySDKCallBack paySDKCallBack;
    private static final String KEY_ENDTIME = "endTime";

    private String orderId;
    private String orderNo;
    private String orderInfo;
    private String amount;
    private String endTime;
    private String endTime_def = "3天";
    private String payRoute;
    private String isChannelOrder = "0";


    // 是否需要在轮训结果后做 提示以及后续操作
    private boolean needNotification = true;

    private static final String GF_STATUS_USABLE = "0";//可用
    private static final String GF_STATUS_NEW_USER = "1";//新用户
    private static final String GF_STATUS_UNUSABLE = "2";//置灰
    private String gfStatus;
    private String showBtnPayStr;
    private String showGFBtnPayStr;
    private List<PayConfig> extendPaymentList;
    private int radiobuttonId = 0;
    private String btStatus;
    private String btTips;
    private List<PayConfig> mPaymentList;
    private String mPayId;
    private AlertDialogEx dialogEx;
    private PayDialogViewModel mViewModel;
    private String payCode;

    /**
     * 加载支付选项并填充数据
     */
    private LinearLayout inflatePayItemAndAddData(PayConfig iconType) {
        SpannableStringBuilder text;
        int drawableResource;
        LinearLayout itemView = (LinearLayout) View.inflate(this, R.layout.item_pay_radio, null);
        RadioButton radio = itemView.findViewById(R.id.rb_pay_item);
        ImageView ivPayItem = itemView.findViewById(R.id.ivPayItem);
        TextView tvPayItemTitle = itemView.findViewById(R.id.tvPayItemTitle);
        ImageView ivPayItemTag = itemView.findViewById(R.id.ivPayItemTag);
        TextView tvPayItemDes = itemView.findViewById(R.id.tvPayItemDes);
        text = new SpannableStringBuilder(iconType.payname);
        switch (iconType.paycode) {
            case ICON_TYPE_ALIPAY:
//                text = new SpannableStringBuilder(getResources().getString(R.string.text_pay_type_alipay));
                if (TextUtils.equals(iconType.iconCode, "pcredit")) {
                    drawableResource = R.drawable.pay_way_alipay_pcredit;
                } else {
                    drawableResource = R.drawable.pay_way_alipay;
                }
                rbAlipay = radio;
                break;
            case ICON_TYPE_ALIPAY_CREDIT:
//                text = new SpannableStringBuilder(getResources().getString(R.string.text_pay_type_pcredit));
                drawableResource = R.drawable.pay_way_alipay_pcredit;
                rbAlipay = radio;
                break;
            case ICON_TYPE_WECHAT_PAY:
//                text = new SpannableStringBuilder(getResources().getString(R.string.text_pay_type_wechat_pay));
                drawableResource = R.drawable.pay_way_wx;
                rbWx = radio;
                break;
            case ICON_TYPE_UNION_PAY:
//                text = new SpannableStringBuilder(getResources().getString(R.string.text_pay_type_union_pay));
                drawableResource = R.drawable.pay_way_bank;
                rbBank = radio;
                break;
            case ICON_TYPE_BT_PAY:
//                text = new SpannableStringBuilder(getResources().getString(R.string.text_pay_type_bt_pay));
                drawableResource = R.drawable.pay_way_gfbt;
                rbBt = radio;
                break;
            case ICON_TYPE_PINGANCREDIT_PAY:
                if (TextUtils.isEmpty(iconType.pingAnCredBalance)) {
                    iconType.pingAnCredBalance = "0.00";
                }
//                text = new SpannableStringBuilder(getResources().getString(R.string.text_pay_type_pingan_pay));
                SpannableStringBuilder des = new SpannableStringBuilder("当前可用额度: " + StringUtil.DecimalFormat2Double(iconType.pingAnCredBalance));
                des.setSpan(new ForegroundColorSpan(ContextCompat.getColor(this, R.color.color_676773)), 0, des.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                des.setSpan(new AbsoluteSizeSpan(10, true), 0, des.length(), Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                text.append("\n").append(des);
                drawableResource = R.drawable.icon_pay_way_pingan;
                rbBt = radio;
                break;
            default:
                return null;
        }
        tvPayItemTitle.setText(text);
        ivPayItem.setImageResource(drawableResource);
        if (!TextUtils.isEmpty(iconType.mktTip)) {
            tvPayItemDes.setText(iconType.mktTip);
        }
        if (!TextUtils.isEmpty(iconType.mktTipColor)) {
            try {
                tvPayItemDes.setTextColor(Color.parseColor(iconType.mktTipColor));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (!TextUtils.isEmpty(iconType.mktIcon)) {
            ImageUtil.loadNoPlace(this, iconType.mktIcon, ivPayItemTag);
        }
        if (TextUtils.equals(iconType.paycode, ICON_TYPE_PINGANCREDIT_PAY)) {
            radio.setEnabled(iconType.isCanUse());
        }
        radio.setOnClickListener(this);
        itemView.setOnClickListener(this);
        radio.setTag(iconType.iconCode);
        itemView.setTag(iconType.iconCode);
//        radio.setId(radiobuttonId++);
        return itemView;
    }

    /**
     * 加载协议view
     */
    private View inflateBtPayProtocol() {
        llBtAgree = (LinearLayout) View.inflate(this, R.layout.item_pay_bt_protocol, null);
        ctvBtAgree = llBtAgree.findViewById(R.id.ctv_bt_agree);
        tv_protocal_link = llBtAgree.findViewById(R.id.tv_bt_agree_doc);
        ctvBtAgree.setOnClickListener(this);
        tv_protocal_link.setOnClickListener(this);
        llBtAgree.setVisibility(View.GONE);
        return llBtAgree;
    }

    @Override
    protected void initData() {
        mViewModel = new ViewModelProvider(this).get(PayDialogViewModel.class);
        setTitle("收银台");
        if (getIntent() == null || getIntent().getExtras() == null) {
            ToastUtils.showShort("参数异常，不能进行支付");
            finish();
        }
        orderId = getIntent().getStringExtra("orderId");
        orderInfo = getIntent().getStringExtra("orderInfo");
        amount = getIntent().getStringExtra("amount");
        payWay = getIntent().getStringExtra("payway");
        orderNo = getIntent().getStringExtra("orderNo");
        payRoute = getIntent().getStringExtra("payRoute");

        if (TextUtils.isEmpty(orderId) || TextUtils.isEmpty(amount) || TextUtils.isEmpty(payRoute)) {
            ToastUtils.showShort("支付参数异常，不能进行支付");
            finish();
        }
//        handleAmount(amount);
        initPayConfig();
        endTime = SpUtil.readString(KEY_ENDTIME, endTime_def);
        btnPay.setOnClickListener(this);
        tvFoldPayItems.setOnClickListener(this);
        mViewModel.getPayDialogLiveData().observe(this, new Observer<BaseBean<PayDialogBean>>() {
            @Override
            public void onChanged(BaseBean<PayDialogBean> payDialogBeanBaseBean) {
                dismissProgress();
                if (payDialogBeanBaseBean != null
                        && payDialogBeanBaseBean.isSuccess()
                        && payDialogBeanBaseBean.data != null
                        && payDialogBeanBaseBean.data.getBtnList() != null) {
                    processNotifyDialog(payDialogBeanBaseBean.data);
                }
            }
        });
        YBMPayUtil.getInstance().setPayCodeCallback(payCode -> {
            this.payCode = payCode;
        });
    }

    private void handleAmount(String amount) {//处理小数点后两位和小数点后数字缩小
        NumberFormat nf = new DecimalFormat("¥0.00");
        String formatAmount = nf.format(Double.parseDouble(amount));
        showBtnPayStr = "确认支付 " + formatAmount;
        showGFBtnPayStr = "确认申请并支付 " + formatAmount;
        btnPay.setText(showBtnPayStr);

        SpannableString spannableString = new SpannableString(formatAmount);
        if (formatAmount.contains("¥")) {
            spannableString.setSpan(new RelativeSizeSpan(0.75f), formatAmount.indexOf("¥"), 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }

        if (formatAmount.contains(".")) {
            spannableString.setSpan(new RelativeSizeSpan(0.75f), formatAmount.indexOf("."), formatAmount.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        tvTotal.setText(spannableString);
    }

    private void initPayConfig() {
        RequestParams params = RequestParams
                .newBuilder()
                .url(AppNetConfig.PAYCONFIG)
//                .url("http://192.168.112.106:8015/app/paymentlist") //
                .addParam("merchantId", merchant_id).addParam("orderId", orderId).addParam("payRoute", payRoute).build();
        HttpManager.getInstance().post(params, new BaseResponse<PayConfigBean>() {
            @Override
            public void onSuccess(String content, BaseBean<PayConfigBean> data, PayConfigBean bean) {
                if (data != null && data.isSuccess()) {
                    handleAmount(bean.money);
                    if (!TextUtils.isEmpty(data.getData().payEndTime)) {
                        time = new TimeCount(data.getData().countDownTime * 1000, 1000);//构造CountDownTimer对象
                        time.start();
                    }
                    if (!TextUtils.isEmpty(bean.isChannelOrder)) {
                        isChannelOrder = bean.isChannelOrder;
                    }
                    if (bean.paymentlist == null || bean.paymentlist.size() < 0) {
                        return;
                    }
                    mPayId = bean.paymentlist.get(0).payid + "";
                    btStatus = bean.guangfaStatus;
                    btTips = bean.guangfaTips;
                    handlePayItemData(bean.paymentlist);
                    mPaymentList = bean.paymentlist;
                    extendPaymentList = bean.extendPaymentList;
                    if (extendPaymentList != null && !extendPaymentList.isEmpty())
                        tvFoldPayItems.setVisibility(View.VISIBLE);
                    for (int i = 0; i < bean.paymentlist.size(); i++) {
                        if (!TextUtils.isEmpty(bean.paymentlist.get(i).ptime)) {
                            endTime = bean.paymentlist.get(i).ptime;
                            break;
                        }
                    }
                    if (TextUtils.isEmpty(endTime)) {
                        if (extendPaymentList != null && !extendPaymentList.isEmpty()) {
                            for (int i = 0; i < extendPaymentList.size(); i++) {
                                if (!TextUtils.isEmpty(extendPaymentList.get(i).ptime)) {
                                    endTime = extendPaymentList.get(i).ptime;
                                    break;
                                }
                            }
                        }
                        if (TextUtils.isEmpty(endTime)) endTime = endTime_def;
                    }
                    SpUtil.writeString(KEY_ENDTIME, endTime);
                    // 2019/10/24 收银台温馨提示
                    if (!TextUtils.isEmpty(bean.tips)) {
                        tvTips.setText("温馨提示：" + bean.tips);
                    }
                    handleIconType(bean.paymentlist.get(0).iconCode);
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
            }
        });
    }

    /**
     * 设置支付项
     *
     * @param paymentlist
     */
    private void handlePayItemData(List<PayConfig> paymentlist) {
        List<PayConfig> types = new ArrayList<>();
        for (int a = 0; a < paymentlist.size(); a++) {
            if (paymentlist.get(a) != null) {
                int state = paymentlist.get(a).state;
                if (state == 1) types.add(paymentlist.get(a));
            }
        }
        appendPayItem(types);
        //小药白条
        if (rbBt == null) return;
        gfStatus = btStatus;
        if (GF_STATUS_USABLE.equals(gfStatus)) {//可用
            if (!TextUtils.isEmpty(btTips)) {
                rbBt.setText(Html.fromHtml(String.format(getResources().getString(R.string.str_pay_way_gf_bt), "(" + btTips + ")")));
            }
        } else if (GF_STATUS_NEW_USER.equals(gfStatus) && rbBt.isChecked()) {//新用户
            llBtAgree.setVisibility(View.VISIBLE);
            btnPay.setText(showGFBtnPayStr);
        } else if (GF_STATUS_UNUSABLE.equals(gfStatus)) {//置灰
            rbBt.setEnabled(false);
            if (!TextUtils.isEmpty(btTips)) {
                rbBt.setText(Html.fromHtml(String.format(getResources().getString(R.string.str_pay_way_gf_bt), "(" + btTips + ")")));
            }
        }
    }

    /**
     * 设置显示支付项
     * @param types 显示的支付项类型
     */
    private void appendPayItem(List<PayConfig> types) {
        if (types == null || types.isEmpty()) return;
        //记录上次选中的位置
        int selectedRadioId = -1;
        for (int i = 0; i < types.size(); i++) {
            LinearLayout radio = inflatePayItemAndAddData(types.get(i));
            if (radio == null) continue;
            llPayGroup.addView(radio);
            if (PAY_TYPE_BT_PAY.equals(types.get(i))) llPayGroup.addView(inflateBtPayProtocol());
        }
        // 根据上次选中的位置设置选中状态
        if (llPayGroup.getChildCount() != 0){
            try {
                LinearLayout itemView = (LinearLayout) llPayGroup.getChildAt(0);
                RadioButton rb = itemView.findViewById(R.id.rb_pay_item);
                rb.setChecked(true);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            ((RadioButton) llPayGroup.findViewById(selectedRadioId)).setChecked(true);
        }
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_payway;
    }

    private void clickTab(View view) {

        switch (view.getId()) {
            case R.id.title_left:
                hideSoftInput();
                if (!TextUtils.isEmpty(orderId)) {
                    RoutersUtils.open("ybmpage://orderdetail/" + orderId);
                }
                finish();
                break;
            case R.id.btn_pay:
                //广发白条新用户需要先判断是否勾选同意授权委托书
                if (YBMPayUtil.PAY_BTSDK.equals(payWay) && GF_STATUS_NEW_USER.equals(gfStatus) && !ctvBtAgree.isChecked()) {
                    ToastUtils.showShort("请勾选并同意《授权委托与承诺书》");
                    return;
                }
                //先判断是否需要弹框
                getIsNeedPop(null);
                break;
            case R.id.tv_bt_agree_doc://授权委托与承诺书
                RoutersUtils.open("ybmpage://commonh5activity?url=" + AppNetConfig.MY_BANKING_XYBT_EMPOWER);
                break;
            case R.id.ctv_bt_agree://同意授权委托与承诺书
                ctvBtAgree.toggle();
                break;
            case R.id.tv_fold_pay_items:
                handlePayItemData(extendPaymentList);
                tvFoldPayItems.setVisibility(View.GONE);
                break;
            default:
                break;
        }

        Object iconType = view.getTag();
        if (iconType == null) return;
        mPayId = getPayId(iconType.toString());
        for (int i = 0; i < llPayGroup.getChildCount(); i++) {
            View childView = llPayGroup.getChildAt(i);
            RadioButton rb = childView.findViewById(R.id.rb_pay_item);
            rb.setChecked(TextUtils.equals(iconType.toString(), rb.getTag().toString()));
        }
        handleIconType(iconType.toString());
    }

    private void handleIconType(String iconType) {
        switch (iconType) {
            case ICON_TYPE_ALIPAY:
                if (llBtAgree != null && llBtAgree.getVisibility() == View.VISIBLE) {
                    btnPay.setText(showBtnPayStr);
                    llBtAgree.setVisibility(View.GONE);
                }
                payWay = YBMPayUtil.PAY_ALISDK;
                break;
            case ICON_TYPE_ALIPAY_CREDIT:
                if (llBtAgree != null && llBtAgree.getVisibility() == View.VISIBLE) {
                    btnPay.setText(showBtnPayStr);
                    llBtAgree.setVisibility(View.GONE);
                }
                payWay = YBMPayUtil.PAY_ALISDK;
                break;
            case ICON_TYPE_WECHAT_PAY:
                if (llBtAgree != null && llBtAgree.getVisibility() == View.VISIBLE) {
                    btnPay.setText(showBtnPayStr);
                    llBtAgree.setVisibility(View.GONE);
                }
                payWay = YBMPayUtil.PAY_WXSDK;
                break;
            case ICON_TYPE_UNION_PAY:
                if (llBtAgree != null && llBtAgree.getVisibility() == View.VISIBLE) {
                    btnPay.setText(showBtnPayStr);
                    llBtAgree.setVisibility(View.GONE);
                }
                payWay = YBMPayUtil.PAY_YLSDK;
                break;
            case ICON_TYPE_BT_PAY:
                if (llBtAgree != null && GF_STATUS_NEW_USER.equals(gfStatus)) {
                    llBtAgree.setVisibility(View.VISIBLE);
                    btnPay.setText(showGFBtnPayStr);
                }
                payWay = YBMPayUtil.PAY_BTSDK;
                break;
            case ICON_TYPE_PINGANCREDIT_PAY:
                if (llBtAgree != null && GF_STATUS_NEW_USER.equals(gfStatus)) {
                    llBtAgree.setVisibility(View.VISIBLE);
                    btnPay.setText(showGFBtnPayStr);
                }
                payWay = YBMPayUtil.PAY_PING_AN;
                break;
            default:
                break;
        }
    }

    /**
     * 根据iconType获取pId
     * @param iconType
     * @return
     */
    private String getPayId(String iconType) {
        if (mPaymentList == null || iconType == null) return "";
        for (PayConfig payConfig : mPaymentList) {
            if (TextUtils.equals(iconType, payConfig.iconCode)) return payConfig.payid + "";
        }
        for (PayConfig payConfig : extendPaymentList) {
            if (TextUtils.equals(iconType, payConfig.iconCode)) return payConfig.payid + "";
        }
        return "";
    }

    /**
     * 支付是否弹窗
     * @param payChannel 20：降级
     */
    private void getIsNeedPop(String payChannel) {
        RequestParams requestParams = RequestParams.newBuilder()
                .url(AppNetConfig.IS_NEED_POP)
                .addParam("merchantId", merchant_id)
                .addParam("orderId", orderId)
                .addParam("uuid", com.ybmmarket20.utils.SpUtil.getDeviceId())
                .build();
        HttpManager.getInstance().post(requestParams, new BaseResponse<ShowPopBean>() {
            @Override
            public void onSuccess(String content, BaseBean<ShowPopBean> obj, ShowPopBean showPopBean) {
                if (showPopBean != null) {
                    int code = showPopBean.code;
                    if (code == 40000) {//需要弹框
                        VerificationCodeDialog.Companion.show(getSupportFragmentManager(), showPopBean.msg, merchant_id, orderId);
                        VerificationCodeDialog.Companion.setVerifyListener(aBoolean -> {
                            if (aBoolean != null && aBoolean == true) {
                                pay(payChannel);
                            }
                            return null;
                        });
                    } else if (code == 10000) {//不弹
                        pay(payChannel);
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
            }
        });
    }

    private void pay(String payChannel) {

        if (TextUtils.isEmpty(payWay)) {
            payWay = YBMPayUtil.PAY_ALISDK;
        }
        toPay(payChannel);
    }

    private void toPay(String payChannel) {
        YBMPayEntity entity = new YBMPayEntity();
        if (YBMPayUtil.PAY_ALI_MINI.equals(payWay)) {
            payWay = YBMPayUtil.PAY_ALISDK;
        }
        if (YBMPayUtil.PAY_WX_MINI.equals(payWay)) {
            payWay = YBMPayUtil.PAY_WXSDK;
        }
        entity.payTypeForFrontKey = payWay;
        entity.order_id = orderId;
        entity.payRoute = payRoute;
        entity.payId = mPayId;
        if (!TextUtils.isEmpty(payChannel)) {
            entity.payChannel = payChannel;
        }
        YBMPayUtil.getInstance().ybmPay(entity, new payCallBackListener());
    }

    @Override
    public void onClick(View v) {
        clickTab(v);
    }

    @Deprecated
    class payCallBackListener implements YBMPayUtil.PayCallBackListener {
        @Override
        public void payCallBack(int resultCode, String msg) {
            SmartExecutorManager.getInstance().executeUI(() -> {
                if (paySDKCallBack != null) {
                    paySDKCallBack = null;
                }
                if (needNotification) {
                    ToastUtils.showShort(msg);
                } else {
                    return;
                }
                if (resultCode == YBMPayUtil.RET_CODE_PROCESS || resultCode == YBMPayUtil.RET_CODE_SUCCESS) {
                    RoutersUtils.open("ybmpage://payresultactivity/" + orderId + "/" + payWay + "/" + amount + "/" + orderNo);
                    //支付成功刷新首页
                    handler.postDelayed(() -> {
                        LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(new Intent(IntentCanst.ACTION_SWITCH_HOME_INFO));
                        LocalBroadcastManager.getInstance(getApplicationContext()).sendBroadcast(new Intent(IntentCanst.ACTION_ORDER_LIST_REFRESH));
                    }, 2000);
                    finish();
                }
            });
        }
    }

    static Handler handler = new Handler();

    @Override
    protected void onResume() {
        super.onResume();
        setLeft(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showBackDialog(endTime, isChannelOrder);
            }
        });
        if (dialogEx == null || !dialogEx.isShowingNoOwnerActivity()) {
            processNotifyCallBack();
        }
    }

    /**
     * 轮训查询是否支付成功
     */
    private void processNotifyCallBack() {
        if (YBMPayUtil.PAY_BTSDK.equalsIgnoreCase(payWay)
                || YBMPayUtil.PAY_ALI_MINI.equalsIgnoreCase(payWay)
                || YBMPayUtil.PAY_WX_MINI.equalsIgnoreCase(payWay)
                || YBMPayUtil.PAY_PING_AN.equalsIgnoreCase(payWay)) {
            String payWayTemp = "";
            if (TextUtils.equals(YBMPayUtil.PAY_ALI_MINI, payWay)) {
                payWayTemp = YBMPayUtil.PAY_ALISDK;
            } else if(TextUtils.equals(YBMPayUtil.PAY_WX_MINI, payWay)) {
                payWayTemp = YBMPayUtil.PAY_WXSDK;
            } else {
                payWayTemp = payWay;
            }
            mViewModel.getPayDialog(orderNo, payWayTemp);
        }
    }

    /**
     * 支付后提示Dialog
     */
    private void processNotifyDialog(PayDialogBean payDialogBeanBaseBean) {
        dialogEx = new AlertDialogEx(this, payDialogBeanBaseBean.getStyleTemplate() == 1? LinearLayout.HORIZONTAL: LinearLayout.VERTICAL);
        dialogEx.setMessage(payDialogBeanBaseBean.getTitle());
        dialogEx.setCanceledOnTouchOutside(false);
        for (PayDialogBtnList payDialogBtnItem : payDialogBeanBaseBean.getBtnList()) {
            //根据服务端返回添加按钮
            if (TextUtils.isEmpty(payDialogBtnItem.getColor())) {
                payDialogBtnItem.setColor("#292933");
            }
            dialogEx.addButton(payDialogBtnItem.getBtnType(), payDialogBtnItem.getBtnText(), new PayDialogViewOnClickListener(payDialogBtnItem.getBtnType()), payDialogBtnItem.getColor());
        }
        dialogEx.setTitle(null);
        dialogEx.show();
    }


    /**
     * 支付结果轮训
     */
    private void doPayResultQuery() {
        YBMPayEntity entity = new YBMPayEntity();
        entity.payTypeForFrontKey = payWay;
        entity.order_id = orderId;
        entity.payRoute = payRoute;
        YBMPayParam params = new YBMPayParam();
        params.entity = entity;
        YBMPayUtil.getInstance().queryPayResult(params, new payCallBackListener(), needNotification);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            showBackDialog(endTime, isChannelOrder);
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    private void showBackDialog(String endTime, final String isChannelOrder) {
        boolean isShow = "0".equals(isChannelOrder);
        AlertDialogEx dialogEx = new AlertDialogEx(this);
        dialogEx.setTitle("")
                .setMessage("下单后" + endTime + "内未支付，订单\n将会被取消，请尽快支付")
                .setCancelButton("放弃支付", (AlertDialogEx.OnClickListener) (dialog, button) -> {
                    if (!isShow) {
                        RoutersUtils.open("ybmpage://myorderlist/0");
                    }
                    finish();
                }).setConfirmButton("继续支付", null)
                .show();

    }

    @SuppressLint("MissingSuperCall")
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (data == null || !YBMPayUtil.PAY_YLSDK.equals(payWay)) {
            if (paySDKCallBack != null) {
                paySDKCallBack = null;
            }
            return;
        }

        /*
         * 支付控件返回字符串:success、fail、cancel 分别代表支付成功，支付失败，支付取消
         */
        String str = data.getExtras().getString("pay_result");
        if (TextUtils.isEmpty(str)) {
            return;
        }
        String result = data.getExtras().getString("result_data");
        if (str.equalsIgnoreCase("success")) {
            // 支付成功后，extra中如果存在result_data，取出校验
            // result_data结构见c）result_data参数说明
            //"支付成功！";
            if (paySDKCallBack != null) {
                paySDKCallBack.sdkPayCallBack(YBMPayUtil.RET_CODE_SUCCESS, getStr(R.string.payway_result_succ), result);
            }
        } else if (str.equalsIgnoreCase("fail")) {
            //"支付失败！";
            if (paySDKCallBack != null) {
                paySDKCallBack.sdkPayCallBack(YBMPayUtil.RET_CODE_FAIL_SDK, getStr(R.string.payway_result_error_sdk), result);
            }
        } else if (str.equalsIgnoreCase("cancel")) {
            //"用户取消了支付";
            if (paySDKCallBack != null) {
                paySDKCallBack.sdkPayCallBack(YBMPayUtil.RET_CODE_USER_CANCEL, getStr(R.string.payway_result_cancel), result);
            }
        }

    }

    public void setPayListener(YBMPayUtil.PaySDKCallBack payCall) {
        this.paySDKCallBack = payCall;
    }

    private String getStr(int id) {
        return YBMAppLike.getAppContext().getString(id);
    }

    class TimeCount extends CountDownTimer {
        public TimeCount(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);//参数依次为总时长,和计时的时间间隔
        }

        @Override
        public void onFinish() {//计时完毕时触发
            if (tvTimer != null) {
                tvTimer.setText(Html.fromHtml("请在<font color='#FF2121'>00:00:00</font>内完成支付"));
            }
            finish();//返回订单详情
        }

        @Override
        public void onTick(long millisUntilFinished) {//计时过程显示
            if (tvTimer == null) {
                return;
            }
            long h = millisUntilFinished / 1000 / 3600;
            long fen = millisUntilFinished / 1000 % 3600 / 60;
            long s = millisUntilFinished / 1000 % 60;
            String hh = "";
            String ff = "";
            String ss = "";
            if (h < 10) {
                hh = "0" + h;
            } else {
                hh = "" + h;
            }
            if (fen < 10) {
                ff = "0" + fen;
            } else {
                ff = "" + fen;
            }
            if (s < 10) {
                ss = "0" + s;
            } else {
                ss = "" + s;
            }
            if (tvTimer != null) {
                tvTimer.setText(Html.fromHtml("请在<font color='#FF2121'>" + hh + ":" + ff + ":" + ss + "</font>内完成支付"));
            }
        }
    }

    @Override
    protected void onDestroy() {
        if (time != null) {
            time.cancel();
        }
        super.onDestroy();
        if(YBMPayUtil.getInstance().pingAnPay != null) {
            EventBus.getDefault().unregister(YBMPayUtil.getInstance().pingAnPay);
        }
    }

    class PayDialogViewOnClickListener implements ViewOnClickListener {

        //已完成付款
        public static final int PAY_DIALOG_FINISH = 1;
        //付款失败
        public static final int PAY_DIALOG_FAILURE = 2;
        //切换支付渠道
        public static final int PAY_DIALOG_SWITCH_OTHER = 3;
        //遇到提示支付存在风险，点此重新支付
        public static final int PAY_DIALOG_REPAY = 4;

        private int btnType;

        PayDialogViewOnClickListener(int btnType) {
            this.btnType = btnType;
        }

        @Override
        public void onClick(AlertDialogEx dialog, int button) {
            switch (btnType) {
                case PAY_DIALOG_FINISH:
                    //已完成付款
                    needNotification = true;
                    doPayResultQuery();
                    break;
                case PAY_DIALOG_FAILURE:
                    //付款失败
                    needNotification = false;
                    break;
                case PAY_DIALOG_SWITCH_OTHER:
                    //切换支付渠道
                    //不处理，默认dismiss
                    break;
                case PAY_DIALOG_REPAY:
                    //遇到提示支付存在风险，点此重新支付,降级
                    //广发白条新用户需要先判断是否勾选同意授权委托书
                    if (YBMPayUtil.PAY_BTSDK.equals(payWay) && GF_STATUS_NEW_USER.equals(gfStatus) && !ctvBtAgree.isChecked()) {
                        ToastUtils.showShort("请勾选并同意《授权委托与承诺书》");
                        return;
                    }
                    //先判断是否需要弹框
                    getIsNeedPop("1");
                    break;
            }
        }
    }
}
