package com.ybmmarket20.activity.jdpay

import android.content.Intent
import android.graphics.Color
import android.os.Handler
import android.text.Html
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.text.style.RelativeSizeSpan
import android.view.KeyEvent
import android.view.View
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.mzule.activityrouter.annotation.Router
import com.google.gson.Gson
import com.ybmmarket20.R
import com.ybmmarket20.activity.PaywayActivity
import com.ybmmarket20.activity.ShoppingGoldRechargeResultActivity
import com.ybmmarket20.activity.jdpay.adapter.PayWayV2Adapter
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.PayNongCheckData
import com.ybmmarket20.bean.ReqUrlJsonBean
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.utils.AlertDialogHtml.Companion.showAlertDialogAuthorization
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.YBMPayUtil
import com.ybmmarket20.utils.showPayPop
import com.ybmmarket20.view.BankCardPopWindow
import com.ybmmarket20.viewmodel.BaseViewModel
import com.ybmmarket20.viewmodel.PAY_LAYOUT_TYPE_NONG
import com.ybmmarket20.viewmodel.PAY_TYPE_NONG
import com.ybmmarket20.viewmodel.PayWayV2ViewModel
import kotlinx.android.synthetic.main.activity_pay_way_v2.btnPay
import kotlinx.android.synthetic.main.activity_pay_way_v2.rvPayWay
import kotlinx.android.synthetic.main.activity_pay_way_v2.tvPayDiscountTips
import kotlinx.android.synthetic.main.activity_pay_way_v2.tvPayWayTips
import kotlinx.android.synthetic.main.activity_pay_way_v2.tvTimer
import kotlinx.android.synthetic.main.activity_pay_way_v2.tvTotal
import kotlinx.android.synthetic.main.activity_pay_way_v2.tv_shopping_gold_tips
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Runnable
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import java.text.DecimalFormat
import java.text.NumberFormat

/**
 * 收银台V2
 */
@Router(
    "paywayactivity/:orderId/:orderInfo/:amount/:payway",
    "paywayactivity/:orderId/:amount/:payway",
    "paywayactivity/:orderId/:amount/:payRoute",
    "paywayactivity/:orderId/:amount/:orderNo/:payRoute",
    "paywayactivity"
)
class PayWayV2Activity : PayWayBaseV2Activity(), View.OnClickListener {

    private val payWayV2ViewModel: PayWayV2ViewModel by viewModels()
    private val payWayHandler: Handler = Handler()
    private var payWayRunnable: Runnable? = null
    private var isFirst = true
    private var rechargeType: String = ""

    override fun jumpSetPw() {
        super.jumpSetPw()
        RoutersUtils.open("ybmpage://setpaypw?settingStatus=$SET_PAY_PASSWORD_SETTING_PAY")
    }

    override fun onResume() {
        super.onResume()
        if (isFirst) {
            isFirst = false
            return
        }
        payWayRunnable?.let { payWayHandler.removeCallbacks(it) }
        payWayRunnable = Runnable {
            if (!getViewModel().isBindCardStatus) {
                getViewModel().getPayDialog(mOrderNo,isFromShoppingRecharge)
            } else getViewModel().isBindCardStatus = false
        }
        payWayHandler.postDelayed(payWayRunnable!!, 1000)
    }

    override fun onPause() {
        super.onPause()
        payWayRunnable?.let { payWayHandler.removeCallbacks(it) }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        showProgress()
        getViewModel().getPaymentList(
                mOrderId ?: "",
                mPayRoute ?: "",
                mOrderNo ?: "",
                isPassCardId = false,
                amount = mAmount ?: "",
                rechargeType = rechargeType
        )
    }

    override fun getContentViewId(): Int = R.layout.activity_pay_way_v2

    override fun initData() {
        setTitle("收银台")
        setLeft { showBackDialog(endTime,tv_shopping_gold_tips.isVisible) }
        if (intent == null || intent.extras == null) {
            ToastUtils.showShort("参数异常，不能进行支付")
            finish()
        }
        mOrderId = intent.getStringExtra("orderId")
        mOrderInfo = intent.getStringExtra("orderInfo")
        mAmount = intent.getStringExtra("amount") ?: ""
//        mPayWay = intent.getStringExtra("payway")
        mOrderNo = intent.getStringExtra("orderNo")
        mPayRoute = intent.getStringExtra("payRoute")
        rechargeType = intent.getStringExtra("rechargeType") ?: ""
        if (rechargeType == "2") {
            isFromShoppingRecharge = true
        }
        if (!isFromShoppingRecharge && (TextUtils.isEmpty(mOrderId) || TextUtils.isEmpty(mPayRoute))) {
            ToastUtils.showShort("支付参数异常，不能进行支付")
            finish()
            return
        }
        initView()
        setObserver()
        showProgress()
        payWayV2ViewModel.getPaymentList(mOrderId ?: "", mPayRoute ?: "", mOrderNo
                ?: "", amount = mAmount ?: "",
                rechargeType = rechargeType)
    }

    fun initView() {
        rvPayWay.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
    }

    private fun setObserver() {
        btnPay.setOnClickListener(this)
        //拉取支付相关数据
        payWayV2ViewModel.paymentListBeanLiveData.observe(this) { payTypeConfigV2Bean ->
            dismissProgress()
            if (mPayWayV2Adapter == null) {
                countDown((payTypeConfigV2Bean.countDownTime ?: 0) * 1000)
            }
            payTypeConfigV2Bean.money?.let { money ->
                if (mAmount.isNullOrEmpty()) {
                    mAmount = money;
                }
                handleAmount(money)
            }
            if (payTypeConfigV2Bean.payDiscountTips.isNullOrEmpty()) {
                tvPayDiscountTips.visibility = View.GONE
            } else {
                tvPayDiscountTips.visibility = View.VISIBLE
                tvPayDiscountTips.text = payTypeConfigV2Bean.payDiscountTips
            }
            tvPayWayTips.text = "【温馨提示】${payTypeConfigV2Bean.tips}"
            tvPayWayTips.visibility =
                    if (TextUtils.isEmpty(payTypeConfigV2Bean.tips)) View.GONE else View.VISIBLE
            if (payTypeConfigV2Bean.payTypeList == null || payTypeConfigV2Bean.payTypeList!!.isEmpty()) return@observe

            payTypeConfigV2Bean.rechargeTextList?.let {
                if (it.isNotEmpty()) {
                    tv_shopping_gold_tips.visibility = View.VISIBLE
                    val spannableStringBuilder = SpannableStringBuilder()

                    for (bean in it) {
                        if (bean.text != null && bean.text.isNotEmpty()) {
                            val mText = bean.text
                            var mColor = ContextCompat.getColor(this, R.color.color_ff2121)
                            try {
                                if (bean.color != null && !bean.color.isEmpty()) {
                                    mColor = Color.parseColor(bean.color)
                                }
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                            spannableStringBuilder.append(mText)
                            spannableStringBuilder.setSpan(
                                    ForegroundColorSpan(mColor),
                                    0,
                                    mText.length,
                                    0
                            )
                        }
                    }
                    tv_shopping_gold_tips.text = spannableStringBuilder
                } else {
                    tv_shopping_gold_tips.visibility = View.GONE
                }
            } ?: kotlin.run {
                tv_shopping_gold_tips.visibility = View.GONE
            }


            mPayWayV2Adapter =
                    payTypeConfigV2Bean.payTypeList?.let { PayWayV2Adapter(it, payWayV2ViewModel) }
            rvPayWay.adapter = mPayWayV2Adapter
            mPayWayV2Adapter?.selectItemCallback = { payCode, payId, itemType ->
                showProgress()
                if (payCode == PAY_TYPE_NONG) { //农行贷
                    payWayV2ViewModel.checkAbChinaLoan(mOrderNo ?: "")
                } else {
                    payWayV2ViewModel.getPaymentList(
                            mOrderId ?: "",
                            mPayRoute ?: "",
                            mOrderNo ?: "",
                            payCode,
                            payId = payId,
                            virtualCardId = payId,
                            itemType = itemType,
                            amount = mAmount ?: "",
                            rechargeType = rechargeType
                    )
                }
            }
            mPayWayV2Adapter?.changeCardCallback = {
                val cardList = payWayV2ViewModel.getBankCards()
                val bankCardPopWindow = BankCardPopWindow(
                    this,
                    cardList,
                    payWayV2ViewModel
                )
                bankCardPopWindow.popWindowSelectCardCallback = { s: String? ->
                    payWayV2ViewModel.switchBankCard(s!!)
                }
                bankCardPopWindow.show(rvPayWay)
            }
        }
        payWayV2ViewModel.nongPayItemLiveData.observe(this,
            Observer<BaseBean<PayNongCheckData>> { bean: BaseBean<PayNongCheckData> ->
                dismissProgress()
                if (bean.isSuccess) {
                    if (bean.data != null) {
                        if (bean.data!!.checkResult) {
                            showProgress()
                            payWayV2ViewModel.getPaymentList(
                                    mOrderId ?: "",
                                    mPayRoute ?: "",
                                    mOrderNo ?: "",
                                    PAY_TYPE_NONG,
                                    payId = PAY_LAYOUT_TYPE_NONG.toString(),
                                    virtualCardId = PAY_LAYOUT_TYPE_NONG.toString(),
                                    itemType = PAY_LAYOUT_TYPE_NONG,
                                    amount = mAmount ?: "",
                                    rechargeType = rechargeType
                            )
                        } else {
                            when (bean.data!!.checkStatus) {
                                "0" -> {
                                    //无需处理 不会有这种情况
                                }

                                "1" -> {
                                    val title = "支付失败"
                                    val contentStr = "农行链e贷已被以下订单锁定待支付，暂不可用。" +
                                            "请额度申请人前往【中国农业银行】App完成支付后再次尝试，或选用其他支付渠道进行支付"
                                    val cancelStr = "使用其他支付渠道"
                                    val confirmStr = "打开农行APP"
                                    val orderNum = bean.data!!.orderNo
                                    val amount = bean.data!!.amount
                                    showPayPop(this, title, contentStr,
                                        orderNum, amount, cancelStr, confirmStr, null,
                                        View.OnClickListener {
                                            val reqUrl =
                                                Gson().fromJson(
                                                    bean.data.abchinaLoanAppDirectUrl,
                                                    ReqUrlJsonBean::class.java
                                                )
                                            val intent = packageManager.getLaunchIntentForPackage(
                                                reqUrl.androidScheme ?: ""
                                            )
                                            if (null != intent) {
                                                // 如果找到了对应的Intent，则启动该应用
                                                startActivity(intent)
                                            } else {
                                                RoutersUtils.open(reqUrl.abchinaDirectUrl)
                                            }
                                        })
                                }

                                "2" -> {
                                    val title = "前往【中国农业银行】App支付"
                                    val contentStr =
                                        "请额度申请人前往【中国农业银行】App，在消息中找到待支付订单，尽快完成支付"
                                    val cancelStr = "稍后支付"
                                    val confirmStr = "打开农行APP"
                                    val orderNum = ""
                                    val amount = ""
                                    showPayPop(this, title, contentStr,
                                        orderNum, amount, cancelStr, confirmStr, null,
                                        View.OnClickListener {
                                            val reqUrl =
                                                Gson().fromJson(
                                                    bean.data.abchinaLoanAppDirectUrl,
                                                    ReqUrlJsonBean::class.java
                                                )
                                            val intent = packageManager.getLaunchIntentForPackage(
                                                reqUrl.androidScheme ?: ""
                                            )
                                            if (null != intent) {
                                                // 如果找到了对应的Intent，则启动该应用
                                                startActivity(intent)
                                            } else {
                                                RoutersUtils.open(reqUrl.abchinaDirectUrl)
                                            }
                                        })
                                }

                                else -> {
                                    null
                                }
                            }
                        }
                    }
                }
            }
        )
        setBaseObserver()
    }

    /**
     * 设置页面金额
     */
    private fun handleAmount(amount: String) { //处理小数点后两位和小数点后数字缩小
        val nf: NumberFormat = DecimalFormat("¥0.00")
        val formatAmount = nf.format(amount.toDouble())
        val showBtnPayStr = "确认支付 $formatAmount"
        btnPay.text = showBtnPayStr
        val spannableString = SpannableString(formatAmount)
        if (formatAmount.contains("¥")) {
            spannableString.setSpan(
                RelativeSizeSpan(0.75f),
                formatAmount.indexOf("¥"),
                1,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        if (formatAmount.contains(".")) {
            spannableString.setSpan(
                RelativeSizeSpan(0.75f),
                formatAmount.indexOf("."),
                formatAmount.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        tvTotal.text = spannableString
    }

    /**
     * 倒计时
     */
    private fun countDown(time: Long) {
        flow {
            for (i in time downTo 0 step 1000) {
                delay(1000)
                emit(i)
            }
        }.flowOn(Dispatchers.Default)
            .onEach {
                val h: Long = it / 1000 / 3600
                val fen: Long = it / 1000 % 3600 / 60
                val s: Long = it / 1000 % 60
                val hh: String = if (h < 10) "0$h" else "$h"
                val ff: String = if (fen < 10) "0$fen" else "$fen"
                val ss = if (s < 10) "0$s" else "$s"
                tvTimer.text =
                    Html.fromHtml("请在<font color='#FF2121'>$hh:$ff:$ss</font>内完成支付")
                val endH = if (h == 0L) "" else "${h}小时"
                val endm = if (h == 0L) "" else "${fen}分"
                val endS = if (h == 0L) "" else "${s}秒"
                endTime = "$endH$endm$endS"
            }
            .onCompletion {
                tvTimer.text = Html.fromHtml("请在<font color='#FF2121'>00:00:00</font>内完成支付")
                finish()
            }
            .flowOn(Dispatchers.Main)
            .launchIn(lifecycleScope)
    }

    override fun checkSubmitOrder() {
        super.checkSubmitOrder()
        val pairParams = payWayV2ViewModel.getSelectedPayRouter(bindFromKey())
        if (pairParams != null) {
            if (TextUtils.isEmpty(pairParams.first)) {
                if (!TextUtils.isEmpty(pairParams.second)) {
                    RoutersUtils.open(pairParams.second)
                }
            } else {
                showAlertDialogAuthorization(
                    this,
                    (if (pairParams.first == null) "" else pairParams.first)!!,
                    if (pairParams.second == null) "" else pairParams.second
                ) { s: String? ->
                    RoutersUtils.open(s)
                    null
                }
            }
        } else {
            showProgress()
            payWayV2ViewModel.getPayType()
        }
    }

    override fun onClick(v: View?) {
        RoutersUtils.open("ybmpage://payresultactivity/" + mOrderId + "/" + PaywayActivity.payWay + "/" + mAmount + "/" + mOrderNo)
//        when (v?.id) {
//            R.id.btnPay -> {
//                if (payWayV2ViewModel.isSelectedBankCard()) {
//                    //选中银行卡
//                    showProgress()
//                    payWayV2ViewModel.queryPayPWSettingStatus()
////                    payWayV2ViewModel.queryPWSettingStatus();
//                } else {
//                    checkSubmitOrder()
//                }
//            }
//        }
    }

    override fun switchBankCard() {
        super.switchBankCard()
        switchBankCard()
    }

    override fun jump2ShoppingGoldResult() {
        super.jump2ShoppingGoldResult()
        payWayV2ViewModel.paymentListBeanLiveData.value?.let {
            ShoppingGoldRechargeResultActivity.launchActivity(
                    this, mAmount?:"",it.resultType ?: "",
                    it.resultTextList ?: arrayListOf()
            )
        }


    }

    override fun bindFromKey(): String = BIND_RESULT_FROM_PAY_WAY

    override fun onDestroy() {
        super.onDestroy()
        YBMPayUtil.destroyUnionPayResult()
    }

    override fun getViewModel(): PayWayV2ViewModel = payWayV2ViewModel
    override fun getReqScene(): String = "cashier"

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            showBackDialog(endTime,tv_shopping_gold_tips.isVisible)
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun getBaseViewModel(): BaseViewModel = payWayV2ViewModel
}