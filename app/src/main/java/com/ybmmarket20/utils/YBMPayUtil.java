package com.ybmmarket20.utils;

import static com.ybmmarket20.utils.PopShowUtilKt.showPayPop;

import android.content.Intent;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;

import com.alipay.sdk.app.PayTask;
import com.alipay.share.sdk.openapi.APAPIFactory;
import com.alipay.share.sdk.openapi.IAPApi;
import com.apkfuns.logutils.LogUtils;
import com.google.gson.Gson;
import com.pingan.bank.kyb_sdk.KybSdk;
import com.pingan.bank.kyb_sdk.bean.KybCallStatusInfo;
import com.pingan.bank.kyb_sdk.bean.KybStatusInfo;
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;
import com.tencent.mm.opensdk.modelpay.PayReq;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.unionpay.UPPayAssistEx;
import com.xyyio.analysis.util.DeviceInfoUtils;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.common.SmartExecutorManager;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.utils.NetUtil;
import com.ybmmarket20.BuildConfig;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.CommonH5Activity;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.PayResultBean;
import com.ybmmarket20.bean.PingAnPayResult;
import com.ybmmarket20.bean.ReqUrlJsonBean;
import com.ybmmarket20.bean.YBMPayEntity;
import com.ybmmarket20.bean.YBMPayParam;
import com.ybmmarket20.bean.YMTPayEntity;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.wxapi.WXEntryActivity;
import com.ybmmarket20.wxapi.WXPayEntryActivity;
import com.ybmmarketkotlin.utils.ChCrypto;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Locale;
import java.util.Map;

import go.error;

/**
 * <AUTHOR>
 * @version V1.0 支付工具类
 * @date 2015-11-3
 */
public class YBMPayUtil {

    private YBMPayUtil() {
    }

    public final static String PAY_ALISDK = "alipay";
    public final static String PAY_ALI_MINI = "alipay_applets";
    public final static String PAY_WXSDK = "weixin";
    public final static String PAY_WX_MINI = "weixin_applets";
    public final static String PAY_YLSDK = "unionpay";
    public final static String PAY_BTSDK = "gfbaitiao";
    public final static String PAY_PING_AN = "pingancredit";
    public final static String PAY_JD = "jdCardPay";
    public final static String PAY_XYD = "xydLoan";
    //农行e贷
    public final static String PAY_NONG = "abchinaLoan";
    public final static String PAY_JINDIE = "kingDee";

    public final static String PAY_HUABEI = "pcredit";

    public final static String PAY_PAD = "pad";//货到付款
    public final static String PAY_TRAN = "tran";//线下转账
    public String pay_str = "支付太频繁,请5秒后再试！";

    //失败的原因
    public final static int RET_CODE_NET_ERROR = 1;//没有网络
    public final static int RET_CODE_PARAM_ERROR = 2;//参数错误
    public final static int RET_CODE_USER_CANCEL = 3;//用户取消
    public final static int RET_CODE_NET_BUSS = 4;//网络忙
    public final static int RET_CODE_FAIL_SDK = 99;//支付失败 SDK 级别的
    public final static int RET_CODE_FAIL = 199;//支付失败
    public final static int RET_CODE_FAIL_YBM = 299;//YBM返回收款失败

    //支付成功
    public static final int RET_CODE_SUCCESS = 1000;//  交易成功
    public static final int RET_CODE_PROCESS = 9999;//  支付处理中

    // 订单支付结果
    public static final int ORDER_STATUS_SUCCESS = 1;//  支付成功
    public static final int ORDER_STATUS_PROCESS = 2;//  支付处理中
    public static final int ORDER_STATUS_ERROR = 3;//  错误

    private int retryResultCount = 0;           //支付结果查询次数 最大三次 3；间隔1s,2s
    private int retrySDKContentCount = 1;       //获取sdk报文次数 最大三次 3； 每次间隔都是1.5s
    private final int MAXRETRYRESULTCOUNT = 4;  //支付结果查询次数 最大三次 3；
    private static YBMPayUtil mInstance;
    private PayCodeCallback payCodeCallback;
    public PaySDKHandler pingAnPay;
    //银联支付结果回调
    private static PaySDKCallBack unionPayCall;


    //定义接口
    public interface PaySDKCallBack {
        void sdkPayCallBack(int code, String res_msg, String RawStr);
    }

    private interface PaySDKHandler {
        void pay(String paymentkey, final PaySDKCallBack payCall);
    }

    public interface PayCallBackListener {
        void payCallBack(int resultCode, String msg);

        default void setOrderNoAndPayReqNo(String orderNo,String mPayReqNo){};

        //小雨点回调
        default void xydCallBack(String paymentKey){}
        default void jinDieCallBack(String paymentKey){}
    }

    private Handler mHandler = new Handler(Looper.getMainLooper());

    public static YBMPayUtil getInstance() {
        if (mInstance == null) {
            synchronized (YBMPayUtil.class) {
                if (mInstance == null) {
                    mInstance = new YBMPayUtil();
                }
            }
        }
        return mInstance;
    }

    public void showProgressDialog() {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            if (YBMAppLike.getApp().getCurrActivity() != null && YBMAppLike.getApp().getCurrActivity() instanceof BaseActivity) {
                ((BaseActivity) YBMAppLike.getApp().getCurrActivity()).showProgress();
            }
        } else {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    if (YBMAppLike.getApp().getCurrActivity() != null && YBMAppLike.getApp().getCurrActivity() instanceof BaseActivity) {
                        ((BaseActivity) YBMAppLike.getApp().getCurrActivity()).showProgress();
                    }
                }
            });
        }
    }

    public void dismissDialog() {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            if (YBMAppLike.getApp().getCurrActivity() != null && YBMAppLike.getApp().getCurrActivity() instanceof BaseActivity) {
                ((BaseActivity) YBMAppLike.getApp().getCurrActivity()).dismissProgress();
            }
        } else {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    if (YBMAppLike.getApp().getCurrActivity() != null && YBMAppLike.getApp().getCurrActivity() instanceof BaseActivity) {
                        ((BaseActivity) YBMAppLike.getApp().getCurrActivity()).dismissProgress();
                    }
                }
            });
        }
    }

    // 启动支付,流程说明 根据订单号获取sdk数据，toSDK， 通知支付服务，查询支付结果
    public void ybmPay(final YBMPayEntity entity, final PayCallBackListener listener) {
        retryResultCount = 0;
        retrySDKContentCount = 1;
        if (entity == null) {
            if (listener != null) {
                listener.payCallBack(RET_CODE_PARAM_ERROR, "参数错误");
            }
            return;
        }
        if (NetUtil.getNetworkState(YBMAppLike.getApp().getCurrActivity()) == NetUtil.NETWORN_NONE) {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    if (listener != null) {
                        listener.payCallBack(RET_CODE_NET_ERROR, "请检查网络状态");
                    }
                }
            });
            return;
        }
        if (PAY_WXSDK.equals(entity.payTypeForFrontKey) && !isWXAppInstalled()) {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    if (listener != null) {
                        listener.payCallBack(RET_CODE_PARAM_ERROR, "使用微信支付，请先安装微信");
                    }
                }
            });
            return;
        }
        showProgressDialog();
        if (entity.payTypeForFrontKey.equals(PAY_ALISDK)
                || entity.payTypeForFrontKey.equals(PAY_WXSDK)
                || entity.payTypeForFrontKey.equals(PAY_YLSDK)
                || entity.payTypeForFrontKey.equals(PAY_BTSDK)
                || entity.payTypeForFrontKey.equals(PAY_PING_AN)
                || entity.payTypeForFrontKey.equals(PAY_JD)
                || entity.payTypeForFrontKey.equals(PAY_NONG)
                || entity.payTypeForFrontKey.equals(PAY_HUABEI)
                || entity.payTypeForFrontKey.equals(PAY_JINDIE)
                ||entity.payTypeForFrontKey.equals(PAY_XYD)) {
            YBMPayParam params = new YBMPayParam();
            params.entity = entity;
            getPayContent(params, listener);
        } else {
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    dismissDialog();
                    if (listener != null) {
                        listener.payCallBack(RET_CODE_PARAM_ERROR, "请选择正确的支付方式");
                    }
                }
            });
            return;
        }
    }

    //获取sdk的报文
    private void getPayContent(final YBMPayParam params, final PayCallBackListener listener) {

        String charset = "UTF-8";
        String formatType = "JSON";
        String version = "1.0";
        String signType = "SHA-256";
        String timestamp = System.currentTimeMillis() + "";
        JSONObject dataJson = new JSONObject();
        try {
            dataJson.put("deviceStatus", SpUtil.getPayFingerprintStatus() ? "1" : "0");
            dataJson.put("deviceId", DeviceInfoUtils.getDeviceId(YBMAppLike.getAppContext()));
            dataJson.put("paycode", params.entity.payTypeForFrontKey);
            dataJson.put("orderId", params.entity.order_id);
            dataJson.put("orderNo", params.entity.orderNo);
            dataJson.put("payRoute", params.entity.payRoute);
            dataJson.put("merchantId", SpUtil.getMerchantid());

            if (!TextUtils.isEmpty(params.entity.payChannel)) {
                dataJson.put("directPay", params.entity.payChannel);
            }
            if (!TextUtils.isEmpty(params.entity.token)) {
                dataJson.put("token", params.entity.token);
            }
            if (!TextUtils.isEmpty(params.entity.tranNo)) {
                dataJson.put("tranNo", params.entity.tranNo);
            }
            if (!TextUtils.isEmpty(params.entity.cardId)) {
                dataJson.put("cardId", params.entity.cardId);
            }
            if ((!TextUtils.isEmpty(params.entity.bankShowName))) {
                dataJson.put("bankCardInfo", params.entity.bankShowName);
            }
            if (!TextUtils.isEmpty(params.entity.reqScene)) {
                dataJson.put("reqScene", params.entity.reqScene);
            }

            if (params.entity.rechargeType != null && params.entity.rechargeType.equals("2")){
                dataJson.put("amount", params.entity.amount);
                dataJson.put("rechargeType", params.entity.rechargeType);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        String dataStr = dataJson.toString();
        String dataEncrypted = ChCrypto.jdPayAesFingerprintEncryptWithKey(dataStr);
        String dataEncrypted1 = dataEncrypted.replace("+", "%2B");
        String sign = "charset=" +
                charset +
                "&data=" +
                dataEncrypted +
                "&formatType=" +
                formatType +
                "&signType=" +
                signType +
                "&timestamp=" +
                timestamp +
                "&version=" +
                version +
                "&key=" +
                "quztwFr2HIl5a1GG";
        String signEncrypted = ChCrypto.shaEncrypt256(sign.toUpperCase(Locale.getDefault()));
        RequestParams request = RequestParams
                .newBuilder()
                .url(AppNetConfig.SDK_PAYMENTV3)
                .addParam("data", dataEncrypted1)
                .addParam("sign", signEncrypted)
                .addParam("charset", charset)
                .addParam("formatType", formatType)
                .addParam("version", version)
                .addParam("signType", signType)
                .addHeader("timestamp", timestamp)
                .build();

        HttpManager.getInstance().post(request, new BaseResponse<YMTPayEntity>() {
            @Override
            public void onSuccess(String content, BaseBean<YMTPayEntity> data, YMTPayEntity baseBean) {
                if (data != null && data.isSuccess()) {
                    dismissDialog();
                    if (baseBean.isQueryPay == 0) {
                        return;
                    }
                    params.paymentKey = baseBean.paymentKey;
                    params.pinganLoginCheckUrl = baseBean.pinganLoginCheckUrl;
                    params.entity.payTypeForFrontKey = baseBean.payTypeForFrontKey;
                    params.entity.orderNo = baseBean.orderNo;
                    if (listener != null){
                        listener.setOrderNoAndPayReqNo(baseBean.orderNo,baseBean.payReqNo);
                    }
                    if (!TextUtils.isEmpty(baseBean.bizCode)) {
                        params.entity.bizCode = baseBean.bizCode;
                    }
                    if (payCodeCallback != null) {
                        payCodeCallback.onCallback(baseBean.payTypeForFrontKey);
                    }
                    if (!TextUtils.isEmpty(params.paymentKey)) {
                        // 这里获取支付的信息
                        if (!TextUtils.isEmpty(baseBean.pToken)) {
                            params.pToken = baseBean.pToken;
                        }

                        //小雨点跳链接
                        if (YBMPayUtil.PAY_XYD.equalsIgnoreCase(params.entity.payTypeForFrontKey)){
                            if (listener != null){
                                listener.xydCallBack(params.paymentKey);
                            }
                            return;
                        } else if (YBMPayUtil.PAY_JINDIE.equalsIgnoreCase(params.entity.payTypeForFrontKey)) {
                            //金蝶跳链接
                                if (listener != null) {
                                    listener.jinDieCallBack(params.paymentKey);
                                }
                                return;
                        }else {
                            toSDK(params, listener);
                        }
                    } else {
                        if (!TextUtils.isEmpty(data.msg)) {
                            pay_str = data.msg;
                        }
                        showError(pay_str, listener, params);
                    }
                } else {
                    if (data != null && !TextUtils.isEmpty(data.msg)) {
                        pay_str = data.msg;
                    }
                    showError(pay_str, listener, params);
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                if (error != null && !TextUtils.isEmpty(error.message)) {
                    pay_str = error.message;
                }
//                retry(pay_str, params, listener);
                showError(pay_str, listener, params);
            }

        });

    }

    private void showError(String error, PayCallBackListener listener, YBMPayParam params) {
        dismissDialog();
        if (!TextUtils.isEmpty(params.entity.reqScene)
                && TextUtils.equals(params.entity.reqScene, "settle")) {
//            AlertDialogEx dialogEx = new AlertDialogEx(YBMAppLike.getApp().getCurrActivity());
//            dialogEx.setMessage("网络异常，请在订单列表重新支付。").setCancelButton("确定", (AlertDialogEx.OnClickListener) (dialog, button) -> {
//                RoutersUtils.open("ybmpage://myorderlist/0");
//                dialog.dismiss();
//                YBMAppLike.getApp().getCurrActivity().finish();
//            }).setCancelable(false).setCanceledOnTouchOutside(false).setTitle("温馨提示").show();
//            return;
            showProgressDialog();
            queryPayResult(params, listener, false);
        }
//        if (listener != null) {//返回
//            listener.payCallBack(RET_CODE_PARAM_ERROR, error);
//        }
    }

    //重试生成sdk的报文
    private void retry(String payStr, final YBMPayParam params, final PayCallBackListener listener) {
        retrySDKContentCount++;
        if (retrySDKContentCount > MAXRETRYRESULTCOUNT || params == null) {
            showError(payStr, listener, params);
        } else {//
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    getPayContent(params, listener);
                }
            }, 1500);

        }
    }

    private void toSDK(final YBMPayParam params, final PayCallBackListener listener) {
        // 调用SDK支付
        PaySDKHandler pay = null;
        if (YBMPayUtil.PAY_ALISDK.equalsIgnoreCase(params.entity.payTypeForFrontKey)) {
            pay = new AliSDKPay();
        } else if (YBMPayUtil.PAY_ALI_MINI.equalsIgnoreCase(params.entity.payTypeForFrontKey)) {
            pay = new AliMiniSdkPay();
        } else if (YBMPayUtil.PAY_WXSDK.equalsIgnoreCase(params.entity.payTypeForFrontKey)) {
            pay = new WXSDKPay();
        } else if (YBMPayUtil.PAY_WX_MINI.equalsIgnoreCase(params.entity.payTypeForFrontKey)) {
            pay = new WxMiniSdkPay();
        } else if (YBMPayUtil.PAY_YLSDK.equalsIgnoreCase(params.entity.payTypeForFrontKey)) {
            pay = new YLSDKPay();
        } else if (YBMPayUtil.PAY_BTSDK.equalsIgnoreCase(params.entity.payTypeForFrontKey)) {
            pay = new BTSDKPay();
        } else if (YBMPayUtil.PAY_PING_AN.equalsIgnoreCase(params.entity.payTypeForFrontKey)) {
            if (pingAnPay != null) {
                EventBus.getDefault().unregister(pingAnPay);
                pingAnPay = null;
            }
            pay = new PingAnPay(params.pToken);
            pingAnPay = pay;
        } else if (YBMPayUtil.PAY_JD.equalsIgnoreCase(params.entity.payTypeForFrontKey)) {
            //京东支付
            pay = new JDSDKPay(params, listener);
        } else if (YBMPayUtil.PAY_NONG.equalsIgnoreCase(params.entity.payTypeForFrontKey)) {
            //农行e贷
            pay = new NongSDKPay(params, listener);
        }else {
            if (listener != null) {
                listener.payCallBack(RET_CODE_PARAM_ERROR, "支付参数异常，不能进行支付");
            }
            return;
        }
        pay.pay(params.paymentKey, new PaySDKCallBack() {
            @Override
            public void sdkPayCallBack(final int resultCode, final String result_msg, final String raw) {
                params.ret_msg = handlerUserMsg(params.entity.payTypeForFrontKey, result_msg, raw);
                params.ret_raw = raw;
                if (resultCode == RET_CODE_SUCCESS || resultCode == RET_CODE_PROCESS || resultCode == RET_CODE_FAIL_SDK || resultCode == RET_CODE_USER_CANCEL) {//支付完成 或者处理中
                    params.sdk_ret = resultCode;
                    notityYBM(params, listener);
                } else {
                    mHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            if (listener != null) {
                                listener.payCallBack(RET_CODE_PARAM_ERROR, "支付SDK异常，支付无法进行");
                            }
                        }
                    });
                }
            }
        });
    }

    //用于处理返回文案
    private String handlerUserMsg(String payType, String result_msg, String raw) {
        return result_msg;
    }

    //通知支付服务 成功要通知网关告诉支付情况 ；用户取消或者失败不请求支付结果，只有成功或者处理中才去请求支付结果
    private void notityYBM(final YBMPayParam params, final PayCallBackListener listener) {
        if (params.sdk_ret == RET_CODE_USER_CANCEL || params.sdk_ret == RET_CODE_FAIL_SDK) {
            if (listener != null) {
                listener.payCallBack(params.sdk_ret, params.ret_msg);
            }
        } else {//支付成功或者处理中到订单系统去查询支付结果
            showProgressDialog();
            queryPayResult(params, listener);
        }
    }

    //从订单系统查询支付结果
    public void queryPayResult(final YBMPayParam params, final PayCallBackListener listener) {
        queryPayResult(params, listener, true);
    }

    // 查询支付结果
    public void queryPayResult(YBMPayParam params, PayCallBackListener listener, boolean needNotification) {
        if (needNotification && retryResultCount == 0) {
            showProgressDialog();
        }
        RequestParams requestParams = RequestParams
                .newBuilder()
                // 药学院支付结果查询
                .url("3".equalsIgnoreCase(params.entity.payRoute) ? AppNetConfig.ORDERPAYRESULT_FOR_DRUGSCHOOL : AppNetConfig.ORDERPAYRESULT)
//                .url("3".equalsIgnoreCase(params.entity.payRoute) ? "http://192.168.160.70:8015/app/virtual/goods/queryPay" : AppNetConfig.ORDERPAYRESULT)
                .addParam("paycode", params.entity.payTypeForFrontKey)
                .addParam("orderId", params.entity.order_id)
                .addParam("orderNo", params.entity.orderNo)
                .addParam("reqScene", params.entity.reqScene)
                .addParam("reqCount", retryResultCount + 1 + "")
                .addParam("showdialog", "0")
                .addParam("merchantId", HttpManager.getInstance().getMerchant_id())
                .build();
        HttpManager.getInstance().post(requestParams, new BaseResponse<PayResultBean>() {
            @Override
            public void onSuccess(String content, BaseBean<PayResultBean> data, PayResultBean baseBean) {
                if (data != null && data.isSuccess()) {
                    if (baseBean.isFinish == 1) {
                        retryPayResult(params, listener, needNotification);
                        return;
                    } else if (baseBean.isFinish == 3) {
                        dismissDialog();
                        return;
                    }
                    if (baseBean.payCode != null && baseBean.payCode.tn == ORDER_STATUS_SUCCESS) {//成功
                        dismissDialog();
                        listener.payCallBack(RET_CODE_SUCCESS, getStr(R.string.payway_result_succ));
                        return;
                    } else {
                        // 白条无sdk，是直接查询
                        if (params.entity.payTypeForFrontKey.equals(PAY_BTSDK)) {
                            params.ybm_ret = RET_CODE_FAIL;
                            params.ret_msg = getStr(R.string.payway_result_error_sdk);
                            retryPayResult(params, listener, needNotification);
                            return;
                        }

                        // 微信，支付宝和银联本地支付成功后才查询
                        // 所以查询结果如果不成功的话，订单状态为没有支付，实际已经扣款的
                        params.ybm_ret = RET_CODE_FAIL_YBM;
                        params.ret_msg = getStr(R.string.payway_result_error_result_query);
                        retryPayResult(params, listener, needNotification);
                        return;
                    }
                }
                params.ret_msg = getStr(R.string.payway_result_error_result_query);
                params.ybm_ret = YBMPayUtil.RET_CODE_FAIL_YBM;
                retryPayResult(params, listener, needNotification);
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                params.ret_msg = getStr(R.string.payway_result_error_result_query);
                params.ybm_ret = YBMPayUtil.RET_CODE_FAIL_YBM;
                retryPayResult(params, listener, needNotification);
            }
        });

    }


    // 支付结果重试3次
    private void retryPayResult(final YBMPayParam params, final PayCallBackListener listener, boolean needNotification) {
        retryResultCount++;
        if (retryResultCount >= MAXRETRYRESULTCOUNT) {//重试后回返结果,ybm支付结果查询失败了
            dismissDialog();
            if (retryResultCount > MAXRETRYRESULTCOUNT) {
                if (!TextUtils.isEmpty(params.entity.reqScene)
                        && TextUtils.equals(params.entity.reqScene, "settle")) {
                    AlertDialogEx dialogEx = new AlertDialogEx(YBMAppLike.getApp().getCurrActivity());
                    dialogEx.setMessage("网络异常，请在订单列表重新支付。").setCancelButton("确定", (AlertDialogEx.OnClickListener) (dialog, button) -> {
                        RoutersUtils.open("ybmpage://myorderlist/0");
                        dialog.dismiss();
                        YBMAppLike.getApp().getCurrActivity().finish();
                    }).setCancelable(false).setCanceledOnTouchOutside(false).setTitle("温馨提示").show();
                    return;
                }
            }
            if (listener != null) {
                mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        listener.payCallBack(params.ybm_ret, params.ret_msg);
                    }
                });
            }
        } else {
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    queryPayResult(params, listener, needNotification);
                }
            }, (retryResultCount + 1) * 1000);

        }
    }

    //京东支付
    private class JDSDKPay implements PaySDKHandler {

        private YBMPayParam params;
        private PayCallBackListener listener;

        public JDSDKPay(YBMPayParam params, PayCallBackListener listener) {
            this.params = params;
            this.listener = listener;
        }

        @Override
        public void pay(String paymentkey, PaySDKCallBack payCall) {
//            payCall.sdkPayCallBack(RET_CODE_SUCCESS, getStr(R.string.payway_result_succ), "");
            showProgressDialog();
            queryPayResult(params, listener, false);
        }
    }

    //跳转收银台
    private void skipToPayWay(String orderId, String amount, String orderNo) {
        RoutersUtils.open("ybmpage://paywayactivity?orderId=" + orderId + "&amount=" + amount + "&payRoute=1" + "&orderNo=" + orderNo);
        YBMAppLike.getApp().getCurrActivity().finish();
    }

    //跳转农行 app
    private void skipNongPay(String json) {
        ReqUrlJsonBean reqUrlJsonBean = new Gson().fromJson(json, ReqUrlJsonBean.class);
        String androidScheme = reqUrlJsonBean.getAndroidScheme() != null ? reqUrlJsonBean.getAndroidScheme() : "";
        Intent intent = YBMAppLike.getAppContext().getPackageManager().getLaunchIntentForPackage(androidScheme);
        if (intent != null) {
            // 如果找到了对应的 Intent，则启动该应用
            YBMAppLike.getAppContext().startActivity(intent);
        } else {
            RoutersUtils.open(reqUrlJsonBean.getAbchinaDirectUrl());
        }
    }

    //跳转订单列表  老版本 合并 master时需要修改成订单外放需求的方式 todo
    private void skipToOrderList() {
        RoutersUtils.open("ybmpage://myorderlist/0");
        YBMAppLike.getApp().getCurrActivity().finish();
    }

    private class NongSDKPay implements PaySDKHandler {
        private static final String STATE_ERROR = "9111";//  客户合约状态非正常或不存在贷款
        private static final String BALANCE_ERROR = "9112";//  贷款可用余额不足
        private static final String NUMBER_ERROR = "9113";//  受托支付账号有误
        private static final String EXIST_ERROR = "9114";//  存在未支付订单
        private static final String SUCCESS = "0";//  成功 核验通过
        private YBMPayParam params;
        private PayCallBackListener listener;

        public NongSDKPay(YBMPayParam params, PayCallBackListener listener) {
            this.params = params;
            this.listener = listener;
        }

        @Override
        public void pay(String paymentkey, PaySDKCallBack payCall) {
//            payCall.sdkPayCallBack(RET_CODE_SUCCESS, getStr(R.string.payway_result_succ), "");
//            showProgressDialog();
//            queryPayResult(params, listener, false);
            if (params.entity.reqScene.equals("settle")) {
                switch (params.entity.bizCode) {
                    case STATE_ERROR: {
                        String title = "支付失败";
                        String contentStr = "农行链e贷额度过期或冻结，暂不可用。请选用其他支付渠道进行支付";
                        String cancelStr = "稍后支付";
                        String confirmStr = "选用其他支付渠道";
                        String orderNum = "";
                        String amount = "";
                        showPayPop(
                                YBMAppLike.getApp().getCurrActivity(), title, contentStr,
                                orderNum, amount, cancelStr, confirmStr, new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        skipToOrderList();
                                    }
                                }, null);
                        break;
                    }
                    case BALANCE_ERROR: {
                        String title = "支付失败";
                        String contentStr = "农行链e贷可用余额不足，暂不可用。请选择其他支付渠道进行支付。\n" +
                                "如需查询可用额度，额度申请人可前往【中国农业银行】app查询";
                        String cancelStr = "稍后支付";
                        String confirmStr = "选用其他支付渠道";
                        String orderNum = "";
                        String amount = "";
                        showPayPop(
                                YBMAppLike.getApp().getCurrActivity(), title, contentStr,
                                orderNum, amount, cancelStr, confirmStr, new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        skipToOrderList();
                                    }
                                }, new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        skipToPayWay(params.entity.order_id, "", params.entity.orderNo);
                                    }
                                });
                        break;
                    }
                    case NUMBER_ERROR: {
                        String title = "支付失败";
                        String contentStr = "农行链e贷受托支付异常，暂不可用。请选用其他支付渠道进行支付";
                        String cancelStr = "稍后支付";
                        String confirmStr = "选用其他支付渠道";
                        String orderNum = "";
                        String amount = "";
                        showPayPop(
                                YBMAppLike.getApp().getCurrActivity(), title, contentStr,
                                orderNum, amount, cancelStr, confirmStr, new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        skipToOrderList();
                                    }
                                }, new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        skipToPayWay(params.entity.order_id, "", params.entity.orderNo);

                                    }
                                });
                        break;
                    }
                    case EXIST_ERROR: {
                        String title = "支付失败";
                        String contentStr = "农行链e贷已被订单锁定待支付或付款中，暂不可用。请选用其他支付渠道进行支付";
                        String cancelStr = "稍后支付";
                        String confirmStr = "选用其他支付渠道";
                        String orderNum = "";
                        String amount = "";
                        showPayPop(
                                YBMAppLike.getApp().getCurrActivity(), title, contentStr,
                                orderNum, amount, cancelStr, confirmStr, new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        skipToOrderList();
                                    }
                                },
                                new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        skipToPayWay(params.entity.order_id, "", params.entity.orderNo);
                                    }
                                });
                        break;
                    }
                    case SUCCESS: {
                        String title = "已通过申请";
                        String contentStr = "提单成功，请额度申请人前往【中国农业银行】App，在消息中找到待支付订单，尽快完成支付";
                        String cancelStr = "稍后支付";
                        String confirmStr = "打开农行APP";
                        String orderNum = "";
                        String amount = "";
                        showPayPop(
                                YBMAppLike.getApp().getCurrActivity(), title, contentStr,
                                orderNum, amount, cancelStr, confirmStr, new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        skipToOrderList();
                                    }
                                },
                                new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        skipNongPay(paymentkey);
                                        showProgressDialog();
                                        queryPayResult(params, listener, false);
                                    }
                                }
                        );
                        break;
                    }
                }
            } else if (params.entity.reqScene.equals("cashier")) {
                switch (params.entity.bizCode) {
                    case STATE_ERROR: {
                        String title = "支付失败";
                        String contentStr = "农行链e贷额度过期或冻结，暂不可用。请选用其他支付渠道进行支付";
                        String cancelStr = "";
                        String confirmStr = "选用其他支付渠道";
                        String orderNum = "";
                        String amount = "";
                        showPayPop(
                                YBMAppLike.getApp().getCurrActivity(), title, contentStr,
                                orderNum, amount, cancelStr, confirmStr, null, null);
                        break;
                    }
                    case BALANCE_ERROR: {
                        String title = "支付失败";
                        String contentStr = "农行链e贷可用余额不足，暂不可用。请选择其他支付渠道进行支付。\n" +
                                "如需查询可用额度，额度申请人可前往【中国农业银行】app查询";
                        String cancelStr = "去农行app查额度";
                        String confirmStr = "选用其他支付渠道";
                        String orderNum = "";
                        String amount = "";
                        showPayPop(
                                YBMAppLike.getApp().getCurrActivity(), title, contentStr,
                                orderNum, amount, cancelStr, confirmStr, new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        skipNongPay(paymentkey);
                                    }
                                }, null);
                        break;
                    }
                    case NUMBER_ERROR: {
                        String title = "支付失败";
                        String contentStr = "农行链e贷受托支付异常，暂不可用。请选用其他支付渠道进行支付";
                        String cancelStr = "";
                        String confirmStr = "选用其他支付渠道";
                        String orderNum = "";
                        String amount = "";
                        showPayPop(
                                YBMAppLike.getApp().getCurrActivity(), title, contentStr,
                                orderNum, amount, cancelStr, confirmStr, null, null);
                        break;
                    }
                    case EXIST_ERROR: {
                        String title = "支付失败";
                        String contentStr = "农行链e贷已被订单锁定待支付或付款中，暂不可用。请选用其他支付渠道进行支付";
                        String cancelStr = "";
                        String confirmStr = "选用其他支付渠道";
                        String orderNum = "";
                        String amount = "";
                        showPayPop(
                                YBMAppLike.getApp().getCurrActivity(), title, contentStr,
                                orderNum, amount, cancelStr, confirmStr, null,
                                null);
                        break;
                    }
                    case SUCCESS: {
                        String title = "前往【中国农业银行】App支付";
                        String contentStr = "请额度申请人前往【中国农业银行】App，在消息中找到待支付订单，尽快完成支付";
                        String cancelStr = "稍后支付";
                        String confirmStr = "打开农行APP";
                        String orderNum = "";
                        String amount = "";
                        showPayPop(
                                YBMAppLike.getApp().getCurrActivity(), title, contentStr,
                                orderNum, amount, cancelStr, confirmStr, null,
                                new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        skipNongPay(paymentkey);
                                        showProgressDialog();
                                        queryPayResult(params, listener, false);
                                    }
                                }
                        );
                        break;
                    }
                }
            }
        }
    }

    // 支付宝支付
    private class AliSDKPay extends PayTask implements PaySDKHandler {
        private static final String SUCCESS = "9000";//  交易成功
        private static final String PROCESS = "8000";//  支付处理中
        private static final String CANCEL = "6001";//  用户取消
        private static final String NET_ERROR = "6002";// 网络错误

        public AliSDKPay() {
            super(YBMAppLike.getApp().getCurrActivity());
        }

        @Override
        public void pay(final String paymentkey, final PaySDKCallBack payCall) {
            SmartExecutorManager.getInstance().execute(new Runnable() {
                @Override
                public void run() {
                    // 调用支付接口，获取支付结果
                    Map<String, String> strRet = payV2(paymentkey, true);
                    if (payCall == null) {
                        LogUtils.d("回调null");
                        return;
                    }
                    if (strRet == null || strRet.size() <= 0) {
                        payCall.sdkPayCallBack(RET_CODE_PARAM_ERROR, "支付SDK返回参数异常", "");
                        return;
                    }
                    String resultStatus = "";
                    String result = "";
                    String memo = "";
                    for (String key : strRet.keySet()) {
                        if (TextUtils.equals(key, "resultStatus")) {
                            resultStatus = strRet.get(key);
                        } else if (TextUtils.equals(key, "result")) {
                            result = strRet.get(key);
                        } else if (TextUtils.equals(key, "memo")) {
                            memo = strRet.get(key);
                        }
                    }
                    StringBuffer rawStr = new StringBuffer();
                    rawStr.append(resultStatus).append(";").append(result).append(";").append(memo);
                    if (SUCCESS.equals(resultStatus)) {
                        payCall.sdkPayCallBack(RET_CODE_SUCCESS, getStr(R.string.payway_result_succ), rawStr.toString());
                    } else if (PROCESS.equals(resultStatus)) {
                        payCall.sdkPayCallBack(RET_CODE_PROCESS, getStr(R.string.payway_result_doing), rawStr.toString());
                    } else if (CANCEL.equals(resultStatus)) {
                        payCall.sdkPayCallBack(RET_CODE_USER_CANCEL, getStr(R.string.payway_result_cancel), rawStr.toString());
                    } else {
                        if (NET_ERROR.equals(resultStatus)) {
                            payCall.sdkPayCallBack(RET_CODE_FAIL_SDK, getStr(R.string.payway_result_error_net), rawStr.toString());
                        } else {
                            payCall.sdkPayCallBack(RET_CODE_FAIL_SDK, getStr(R.string.payway_result_error_sdk), rawStr.toString());
                        }

                    }
                }
            });
        }

        private String gatValue(String content, String key) {
            String prefix = key + "={";
            return content.substring(content.indexOf(prefix) + prefix.length(),
                    content.lastIndexOf("}"));
        }
    }

    // alipay 小程序支付
    private class AliMiniSdkPay implements PaySDKHandler {

        @Override
        public void pay(String paymentkey, PaySDKCallBack payCall) {
            IAPApi api = APAPIFactory.createZFBApi(YBMAppLike.getAppContext(), ShareUtil.SHARE_ALI_APPID, false);
            boolean isZFBInstalled = api.isZFBAppInstalled();
            if (!isZFBInstalled) {
                ToastUtils.showShort("请安装支付宝");
                return;
            }
            Intent intent = new Intent();
            Uri alipayMiniUri = Uri.parse(paymentkey);
            intent.setData(alipayMiniUri);
            YBMAppLike.getApp().getCurrActivity().startActivity(intent);
        }
    }

    //微信支付
    private class WXSDKPay implements PaySDKHandler {

        @Override
        public void pay(String paymentkey, PaySDKCallBack payCall) {
            try {
                JSONObject json = new JSONObject(paymentkey);
                if (json != null && json.has("appid")) {
                    PayReq req = new PayReq();
                    req.appId = json.getString("appid");
                    req.partnerId = json.getString("partnerid");
                    req.prepayId = json.getString("prepayid");
                    req.nonceStr = json.getString("noncestr");
                    req.timeStamp = json.getString("timestamp");
                    req.packageValue = json.getString("package");
                    req.sign = json.getString("sign");
                    IWXAPI api = WXAPIFactory.createWXAPI(YBMAppLike.getAppContext(), null);
                    api.registerApp(req.appId);
                    api.sendReq(req);
                    //设置支付回调
                    WXPayEntryActivity.setPaySDKCallBack(payCall, req.appId);
                } else {
                    payCall.sdkPayCallBack(RET_CODE_PARAM_ERROR, "支付服务返回数据错误", null);
                }
            } catch (JSONException e) {
                e.printStackTrace();
                payCall.sdkPayCallBack(RET_CODE_PARAM_ERROR, "支付服务返回数据错误", null);
            }
        }
    }

    // 微信小程序支付
    private class WxMiniSdkPay implements PaySDKHandler {

        @Override
        public void pay(String paymentkey, PaySDKCallBack payCall) {
            // 返回一个json串
            /*{
                    "Appid":"wx5766ec723a326dff",
                    "WXMiniUserName":"gh_c5d2d636d254",
                    "WXMiniPath ":"pages/index/index?businessNo=YBM20200817110711100002&payNo=XXX&needPaymoney=xxx "
            }*/

            try {
                LogUtils.tag("payment").e(paymentkey);
                JSONObject wxminipayJson = new JSONObject(paymentkey);

                String appId = wxminipayJson.optString("Appid"); // 填应用AppId
                IWXAPI api = WXAPIFactory.createWXAPI(YBMAppLike.getApp().getCurrActivity(), appId);

                WXLaunchMiniProgram.Req req = new WXLaunchMiniProgram.Req();
                req.userName = wxminipayJson.optString("WXMiniUserName"); // 填小程序原始id
                req.path = wxminipayJson.optString("WXMiniPath");         // 拉起小程序页面的可带参路径，不填默认拉起小程序首页，对于小游戏，可以只传入 query 部分，来实现传参效果，如：传入 "?foo=bar"。
                //todo 上线前换成release
                if ("com.ybmmarket20.debug".equalsIgnoreCase(BuildConfig.APPLICATION_ID)) {
                    req.miniprogramType = WXLaunchMiniProgram.Req.MINIPROGRAM_TYPE_PREVIEW;// 可选打开 开发版，体验版和正式版
                } else {
                    req.miniprogramType = WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE;// 可选打开 开发版，体验版和正式版
                }
                api.sendReq(req);

                //设置支付回调
                WXEntryActivity.setPaySdkCallBack(payCall);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    //银联支付
    private class YLSDKPay implements PaySDKHandler {
        private String serverMode = "00";

        @Override
        public void pay(String paymentkey, PaySDKCallBack payCall) {
            try {
                UPPayAssistEx.startPay(BaseYBMApp.getApp().getCurrActivity(), null, null, paymentkey, serverMode);
                unionPayCall = payCall;
            } catch (Throwable e) {
                LogUtils.d(e);
                BugUtil.sendBug(e);
                payCall.sdkPayCallBack(YBMPayUtil.RET_CODE_PARAM_ERROR, "银联支付错误", null);
            }
        }

    }

    //白条支付
    private class BTSDKPay implements PaySDKHandler {

        @Override
        public void pay(String paymentkey, PaySDKCallBack payCall) {
            try {
                Intent intent = new Intent(YBMAppLike.getApp().getCurrActivity(), CommonH5Activity.class);
                intent.putExtra(CommonH5Activity.ext_url, paymentkey);
                intent.putExtra(CommonH5Activity.needDecode, false);
                intent.putExtra("photo", "1");
                intent.putExtra("head_menu", "0");
                CommonH5Activity.setPaySDKCallBack(payCall);
                YBMAppLike.getApp().getCurrActivity().startActivity(intent);
            } catch (Throwable e) {
                LogUtils.d(e);
                BugUtil.sendBug(e);
                payCall.sdkPayCallBack(YBMPayUtil.RET_CODE_PARAM_ERROR, "白条支付错误", null);
            }
        }

    }

    /**
     * 平安
     */
    private class PingAnPay implements PaySDKHandler {

        PaySDKCallBack payCall = null;
        String pToken = "";

        public PingAnPay(String pToken) {
            this.pToken = pToken;
        }

        @Override
        public void pay(String paymentkey, PaySDKCallBack payCall) {
            this.payCall = payCall;
            EventBus.getDefault().register(this);
            KybSdk.startWithYunReceiveMoney(YBMAppLike.getApp().getCurrActivity(), paymentkey);
        }

        /**
         * 通过EventBus收到KybCallStatusInfo的消息通知。
         * KybCallStatusInfo的消息通知是H5调用callHostNative方法
         * 原生这边会将调用callHostNative方法所传递的params 和method字段传递到EKybCallStatusInfo的data中消息中
         * 原生监听KybCallStatusInfo消息，然后自身做业务和逻辑处理。如果有返回结果
         * 通过KybSdk.hostCallbackH5(callbackInfo); 传递给H5
         */
        @Subscribe(threadMode = ThreadMode.MAIN)
        public void receviedKybStatusInfo(@NonNull KybCallStatusInfo kybCallStatusInfo) {
            String data = kybCallStatusInfo.getJsonData();
            String checkResultJson = kybCallStatusInfo.getJsonData();
            final JSONObject callbackInfo = new JSONObject();
            boolean checkoutResult = false;
            try {
                JSONObject resultObj = new JSONObject(checkResultJson).getJSONObject("params");
                if (TextUtils.equals(resultObj.getString("channelId"), "CCSS-CLOUDPAY")) {
                    //支付验证
                    checkoutResult = TextUtils.equals(resultObj.getString("checkCode"), SpUtil.getMerchantid());
                } else if (TextUtils.equals(resultObj.getString("channelId"), "KYB")) {
                    //免登录校验
                    checkoutResult = TextUtils.equals(resultObj.getString("checkCode"), pToken);
                }
            } catch (JSONException e) {
                e.printStackTrace();
                checkoutResult = false;
            }
            try {
                if (checkoutResult) {
                    callbackInfo.put("status", "ok");
//                    callbackInfo.put("data", "");
//                    callbackInfo.put("data2", true);
                    callbackInfo.put("msg", "");
                } else {
                    callbackInfo.put("status", "error");
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
            KybSdk.hostCallbackH5(callbackInfo);
        }

        /**
         * 通过EventBus收到KybStatusInfo的消息通知。
         * KybStatusInfo的消息通知是H5调用statusPush方法进行状态通知的
         * 原生这边会将调用statusPush方法所传递的data字段传递到 KybStatusInfo 的data中消息中
         * 原生监听KybStatusInfo消息，然后自身做业务和逻辑处理。如果有返回结果
         * 通过KybSdk.hostCallbackH5(callbackInfo); 传递给H5
         */
        @Subscribe(threadMode = ThreadMode.MAIN)
        public void receviedKybStatusInfo(@NonNull KybStatusInfo kybCallStatusInfo) {
            String data = kybCallStatusInfo.getData();
//            final JSONObject callbackInfo = new JSONObject();
//            try {
//                callbackInfo.put("status", "ok");
//                callbackInfo.put("data", data);
//                callbackInfo.put("data2", true);
//                callbackInfo.put("msg", "");
//            } catch (JSONException e) {
//                e.printStackTrace();
//            }
//            KybSdk.hostCallbackH5(callbackInfo);
            try {
                Gson gson = new Gson();
                PingAnPayResult payResult = gson.fromJson(data, PingAnPayResult.class);
                int status = -1;
                if (payResult != null && payResult.valueMap != null) {
                    status = payResult.valueMap.OrderStatus;
                }
                if (payCall != null) {
                    if (status == 1) {
                        payCall.sdkPayCallBack(RET_CODE_SUCCESS, getStr(R.string.payway_result_succ), data);
                    } else if (status == 0 || status == 2 || status == 3) {
                        payCall.sdkPayCallBack(RET_CODE_PROCESS, getStr(R.string.payway_result_doing), data);
                    } else if (status == 4) {
                        payCall.sdkPayCallBack(RET_CODE_USER_CANCEL, getStr(R.string.payway_result_cancel), data);
                    } else {
                        payCall.sdkPayCallBack(RET_CODE_FAIL_SDK, getStr(R.string.payway_result_error_sdk), data);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                EventBus.getDefault().unregister(this);
                pingAnPay = null;
            }
        }
    }

    public boolean isWXAppInstalled() {
        try {
            IWXAPI api = WXAPIFactory.createWXAPI(YBMAppLike.getAppContext(), null);
            return api.isWXAppInstalled();
        } catch (Throwable e) {
            BugUtil.sendBug(e);
        }
        return true;
    }

    private String getStr(int id) {
        return YBMAppLike.getAppContext().getString(id);
    }


    public interface PayCodeCallback {
        void onCallback(String payCode);
    }

    public void setPayCodeCallback(PayCodeCallback callback) {
        this.payCodeCallback = callback;
    }

    public static void callUnionPayResult(int code, String resMsg, String rawStr) {
        if (unionPayCall != null) {
            unionPayCall.sdkPayCallBack(code, resMsg, rawStr);
        }
    }

    public static void destroyUnionPayResult() {
        unionPayCall = null;
    }
}
