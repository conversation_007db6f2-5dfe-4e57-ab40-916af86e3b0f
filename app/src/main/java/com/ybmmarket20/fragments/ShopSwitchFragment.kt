package com.ybmmarket20.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.ybmmarket20.R
import com.ybmmarket20.common.SimpleLazyFragment

/**
 * 切换fragment
 */
class ShopSwitchFragment : SimpleLazyFragment() {

    lateinit var mInitState: SwitchState

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return View.inflate(context, R.layout.fragment_shop_switch_fragment, null)
    }

    /**
     * 初始化fragment状态，首次展示的fragment
     */
    fun initSwitchState(state: SwitchState) {
        mInitState = state
    }

    override fun loadData() {
        switchFragment(mInitState)
    }

    /**
     * 切换fragment
     */
    fun switchFragment(state: SwitchState) {
        val orgId = arguments?.getString("orgId")
        val shopCode = arguments?.getString("shopCode")
        val fragment = when (state) {
            is SwitchState.SHOP_QUALIFACTION_SELF -> ShopQualificationSelfFragment()
            is SwitchState.SHOP_QUALIFACTION_POP -> ShopQualificationPopFragment()
            is SwitchState.SHOP_AFTER_SALE_DISTRIBUTION_SELF -> ShopAfterSaleDistributionSelfFragment()
            is SwitchState.SHOP_AFTER_SALE_DISTRIBUTION_POP -> ShopAfterSaleDistributionPopFragment()
            is SwitchState.SHOP_OPEN_ACCOUNT -> ShopOpenAccountFragment()
        }
        fragment.apply {
            arguments = Bundle().apply {
                putString("params", state.id)
                orgId?.let { putString("orgId", orgId) }
                shopCode?.let { putString("shopCode", shopCode) }
            }
            val transaction = <EMAIL>()
            <EMAIL> {
                transaction.remove(it)
            }
            transaction.replace(R.id.container, this)
            transaction.commit()
        }
    }
}

/**
 * 切换状态
 */
sealed class SwitchState(val id: String) {
    //店铺资质-自营
    class SHOP_QUALIFACTION_SELF(val params: String) : SwitchState(params)

    //店铺资质-pop
    class SHOP_QUALIFACTION_POP(val params: String) : SwitchState(params)

    //配送售后-自营
    class SHOP_AFTER_SALE_DISTRIBUTION_SELF(val params: String) : SwitchState(params)

    //配送售后-pop
    class SHOP_AFTER_SALE_DISTRIBUTION_POP(val params: String) : SwitchState(params)

    //开户流程
    class SHOP_OPEN_ACCOUNT(val params: String) : SwitchState(params)
}