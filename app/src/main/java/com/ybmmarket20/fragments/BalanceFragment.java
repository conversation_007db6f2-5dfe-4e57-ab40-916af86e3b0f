package com.ybmmarket20.fragments;

import android.content.Intent;
import android.os.Bundle;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.OrderDetailActivity;
import com.ybmmarket20.bean.BalanceLogListBean;
import com.ybmmarket20.bean.BalanceLogRowBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.common.BaseFragment;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.SpUtil;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import butterknife.Bind;

import static com.chad.library.adapter.base.BaseQuickAdapter.RequestLoadMoreListener;

/**
 * Created by ybm on 2017/4/25.
 * 余额的页面
 */
public class BalanceFragment extends BaseFragment {

    public static final int TYPE_EXPEND = 2;
    public static final int TYPE_INCOME = 1;
    public static final int TYPE_TOTAL = 4;
    public static final int TYPE_WAIT = 3;
    public static final int TYPE_ALL = 0;
    public static final String BUNDLE_TYPE = "bundle_type";

    @Bind(R.id.rv_balance_list)
    RecyclerView rvBalanceList;
    @Bind(R.id.ll_no_data)
    LinearLayout llNoData;
    @Bind(R.id.tv_no_data_tip)
    TextView tvNoDataTip;

    private List<BalanceLogRowBean> mBalanceData = new ArrayList<>();
    private BalanceAdapter mAdapter;
    private int mCurrentPage = 0;
    private int mPageSize = 10;
    private int mBalanceType;
    private BalanceNumberListener mBalanceListener;

    public interface BalanceNumberListener {
        void getBalanceNumber(String total, String wait, String available,int isShowCash);
    }

    public static BalanceFragment getInstance(Bundle bundle) {
        BalanceFragment fragment = new BalanceFragment();
        if (bundle != null) {
            fragment.setArguments(bundle);
        }
        return fragment;
    }

    public void setBalanceListener(BalanceNumberListener listener) {
        mBalanceListener = listener;
    }

    @Override
    protected void initData(String content) {
        rvBalanceList.setLayoutManager(new WrapLinearLayoutManager(getNotNullActivity()));
//        DividerLine divider = new DividerLine(DividerLine.VERTICAL);
//        divider.setSize(1);
//        divider.setColor(0xffeeeeee);
//        rvBalanceList.addItemDecoration(divider);
        mAdapter = new BalanceAdapter(R.layout.item_fragment_balance_v2, mBalanceData);
        mAdapter.openLoadMore(mPageSize, true);
        mAdapter.setOnLoadMoreListener(mLoadMoreListener);
        rvBalanceList.setAdapter(mAdapter);
        getBalanceList(mCurrentPage);
    }

    @Override
    protected void initTitle() {
        Bundle bundle = getArguments();
        if (bundle == null) {
            return;
        }
        mBalanceType = bundle.getInt(BUNDLE_TYPE, TYPE_TOTAL);
        String noDataTip;
        switch (mBalanceType) {
            case TYPE_EXPEND:
                noDataTip = "还没有支出记录";
                break;
            case TYPE_INCOME:
                noDataTip = "还没有收入记录";
                break;
            case TYPE_TOTAL:
                noDataTip = "还没有累计余额明细";
                break;
            case TYPE_WAIT:
                noDataTip = "还没有待领取余额明细";
                break;
            case TYPE_ALL:
                noDataTip = "还没有余额明细";
                break;
            default:
                noDataTip = "还没有相关记录";
                break;
        }
        tvNoDataTip.setText(noDataTip);
    }

    @Override
    protected RequestParams getParams() {
        return null;
    }

    @Override
    protected String getUrl() {
        return null;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_balance;
    }

    /**
     * 加载更多的接口
     */
    private RequestLoadMoreListener mLoadMoreListener = new RequestLoadMoreListener() {
        @Override
        public void onLoadMoreRequested() {
            mCurrentPage++;
            getBalanceList(mCurrentPage);
        }
    };

    private void showView(boolean isDataNull) {
        if (mCurrentPage != 0) {
            return;
        }
        if (llNoData != null) {
            llNoData.setVisibility(isDataNull ? View.VISIBLE : View.GONE);
        }
        if (rvBalanceList != null) {
            rvBalanceList.setVisibility(isDataNull ? View.GONE : View.VISIBLE);
        }
    }

    /**
     * 获取余额
     */
    private void getBalanceList(int page) {
        String merchant_id = SpUtil.getMerchantid();
        int offset = page > 0 ? page : 0;
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("balanceType", String.valueOf(mBalanceType));
        params.put("limit", String.valueOf(mPageSize));
        params.put("offset", String.valueOf(offset));
        HttpManager.getInstance().post(AppNetConfig.BALANCE_INFO, params, new BaseResponse<BalanceLogListBean>() {

            @Override
            public void onSuccess(String content, BaseBean<BalanceLogListBean> obj,BalanceLogListBean data) {
                if (obj == null || !obj.isSuccess() || data == null) {
                    showView(true);
                    if (mCurrentPage > 0) {
                        mCurrentPage--;
                    }
                    return;
                }
                if (mBalanceListener != null) {
                    mBalanceListener.getBalanceNumber(data.totalBalance, data.unclaimedBalance, data.balance,data.isShowCash);
                }
                if (data.getList() == null || data.getList().isEmpty()) {
                    showView(true);
                    if (mCurrentPage > 0) {
                        mCurrentPage--;
                    }
                    mAdapter.notifyDataChangedAfterLoadMore(false);
                } else {
                    showView(false);
                    //第一页数据
                    if (mCurrentPage < 1) {
                        if(mBalanceData==null){
                            mBalanceData = new ArrayList<>();
                        }
                        mBalanceData.clear();
                        mBalanceData.addAll(data.getList());
                        mAdapter.setNewData(mBalanceData);
                    } else {
                        mBalanceData.addAll(data.getList());
                        mAdapter.notifyDataChangedAfterLoadMore(data.getList().size() >= mPageSize);
                    }

                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                showView(true);
                if (mCurrentPage > 0) {
                    mCurrentPage--;
                }
            }
        });
    }

    private class BalanceAdapter extends YBMBaseAdapter<BalanceLogRowBean> {

        private SimpleDateFormat mFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());

        public BalanceAdapter(int layoutResId, List<BalanceLogRowBean> data) {
            super(layoutResId, data);
        }

        @Override
        protected void bindItemView(YBMBaseHolder baseViewHolder, final BalanceLogRowBean bean) {
            String time = mFormat.format(new Date(bean.createTime));
            baseViewHolder.setText(R.id.tv_balance_type, bean.statusMemo);
            double balance = Double.parseDouble(bean.balanceJournal);
            baseViewHolder.setText(R.id.tv_balance_num, (balance >= 0 ? "+" : "") + bean.balanceJournal);
            baseViewHolder.setText(R.id.tv_balance_time, time);
            baseViewHolder.getView(R.id.iv_right).setVisibility(errorId(bean.orderId)?View.INVISIBLE:View.VISIBLE);
            baseViewHolder.getView(R.id.root_balance_item).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!errorId(bean.orderId)) {
                        Intent intent = new Intent(getActivity(), OrderDetailActivity.class);
                        intent.putExtra(IntentCanst.ORDER_ID, bean.orderId);
                        startActivity(intent);
                    }
                }
            });
        }

        private boolean errorId(String id) {
            return TextUtils.isEmpty(id) || "-1".equals(id);
        }
    }

    public void getData(){
        mCurrentPage = 0;
        getBalanceList(mCurrentPage);
    }

}
