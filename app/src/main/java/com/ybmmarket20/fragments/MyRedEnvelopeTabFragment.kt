package com.ybmmarket20.fragments

import androidx.fragment.app.Fragment

/**
 * 我的红包
 */
class MyRedEnvelopeTabFragment: MyRedEnvelopeClassFragment() {

    var mFragments: Array<Fragment>? = null

    override fun getTitles(): Array<String> = arrayOf("可使用", "已用完", "已失效")

    override fun getFragments(): Array<Fragment> {
        if (mFragments == null) mFragments = arrayOf(
                MyRedEnvelopeSubTabFragment.getInstance(1),
                MyRedEnvelopeSubTabFragment.getInstance(2),
                MyRedEnvelopeSubTabFragment.getInstance(3)
        )
        return mFragments!!
    }
}