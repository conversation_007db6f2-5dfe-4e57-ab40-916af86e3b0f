package com.ybmmarket20.fragments

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import butterknife.Bind
import com.baidu.location.BDAbstractLocationListener
import com.baidu.location.BDLocation
import com.baidu.location.LocationClient
import com.baidu.location.LocationClientOption
import com.tbruyelle.rxpermissions2.RxPermissions
import com.ybm.app.bean.NetError
import com.ybm.app.utils.PermissionDialogUtil
import com.ybmmarket20.R
import com.ybmmarket20.activity.REQUEST_CODE_CHECK_PERMISSION
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.SearchResultBean
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.AdapterUtils.getAfterDiscountPrice
import com.ybmmarket20.utils.PermissionUtil
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.*
import org.json.JSONException
import org.json.JSONObject

/**
 * @description ka首页常购清单
 */
class KaHomeOftenBuyFragment : HomeTabLicenseStatusFragment() {
    private var isInit: Boolean = false

    // 是否已经加载过
    private var isLoaded: Boolean = false
    override var pageNum: Int = 0

    // 是否要更新mFlowData，点击tab按钮(为你推荐)调用接口后更新，该字段判断是否更新
    private var isUpdateAnalysis: Boolean = true


    @Bind(R.id.tv_local)
    lateinit var tv_local: TextView

    @Bind(R.id.empty_view)
    lateinit var empty_view: LinearLayout
    internal var gpsIsOpen = false
    private var latitude = ""
    private var longitude = ""
    private var isLocaled = true

    /**
     * 是否开启一审资质审核状态监听
     */
    override fun onLicenseStatusEnable(): Boolean = true

    override fun initData(content: String?) {
        //super.initData(content)
        // 空实现，基类会调用网络请求，该页面要求点击tab后切换到该页面所以此处不应该调用网络加载
        tv_local.setOnClickListener { v ->
            isLocaled = false
            openAppSettingDetail(context as Activity)
        }
        //requestLocationPermission()
    }

    override fun onResume() {
        super.onResume()
        if (!isLocaled) {
            requestLocationPermissionDialog();
        }
    }

    /**
     * 处理状态变更
     */
    override fun handleLicenseStatusChange(status: Int) {
        if (adapter != null && isLoaded) {
            rowsList.clear()
            adapter?.notifyDataSetChanged()
            getListData()
        }
    }

    /**
     * 获取网络数据
     */
    override fun getListData() {
        val requestParams = params
        if (!isUpdateAnalysis) {
            addAnalysisRequestParams(requestParams, mFlowData)
        }
        HttpManager.getInstance().post(AppNetConfig.KA_HOME_HOME_OFTEN_BUY_LIST, requestParams, object : BaseResponse<SearchResultBean>() {

            override fun onSuccess(content: String?, obj: BaseBean<SearchResultBean>?, t: SearchResultBean?) {
                if (obj != null && t != null && t.rows != null && obj.isSuccess) {
                    isLoaded = true
                    if (isRefresh) {
                        rowsList.clear()
                        isRefresh = false
                    }
                    if (isUpdateAnalysis) {
                        updateFlowData(mFlowData, t.sptype, t.spid, t.sid)
                        flowDataPageCommoditySearch(mFlowData)
                        adapter?.setFlowData(mFlowData)
                        isUpdateAnalysis = false
                    }
                    if (t.rows.size == 10) {
                        rowsList.addAll(t.rows)
                        // 请求并更新折后价
                        adapter?.let { getAfterDiscountPrice(t.rows, it) }
                        adapter?.notifyDataChangedAfterLoadMore(true)
                    } else if (t.rows.size < 10) {
                        rowsList.addAll(t.rows)
                        // 请求并更新折后价
                        adapter?.let { getAfterDiscountPrice(t.rows, it) }
                        adapter?.notifyDataChangedAfterLoadMore(false)
                    }
                }
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
                adapter?.notifyDataChangedAfterLoadMore(false)
            }
        })
    }

    override fun initView() {
        super.initView()
        adapter?.setOnListItemClickListener { rows ->
            // 竖向列表埋点
            val jsonObject = JSONObject()
            try {
                jsonObject.put("id", rows?.id)
            } catch (e: JSONException) {
                e.printStackTrace()
            }
            XyyIoUtil.track(XyyIoUtil.ACTION_HOME_PH_PRODUCT, jsonObject,rows)
            openUrl("ybmpage://productdetail/" + rows?.id)
        }
        val inflater = context?.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val emptyView = inflater.inflate(R.layout.layout_empty_view_home, null)
        emptyView.layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        adapter?.emptyView = emptyView
    }


    /**
     * 组装请求参数
     */
    override fun getParams(): RequestParams? = RequestParams().also {
        it.put("merchantId", SpUtil.getMerchantid())
//        it.put("limit", pageSize.toString())
//        it.put("offset", pageNum.toString())
        it.put("longitude", longitude)
        it.put("latitude", latitude)
        it.put("spFrom", AnalysisConst.FlowDataChain.FLOWDATACHAIN_FROM_HOME_OFTEN_BUY_LIST)
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_home_activity_area
    }

    /**
     * 通过该方法主动获取数据
     */
    override fun refresh() {
        super.refresh()
        pageNum = 0
        rowsList.clear()
        requestLocationPermissionDialog()
    }

    /**
     * 打开应用设置详情页
     */
    private fun openAppSettingDetail(context: Activity) {
        val intent = Intent()
        if (!gpsIsOpen) {
            intent.action = Settings.ACTION_LOCATION_SOURCE_SETTINGS
        } else {
            intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
            intent.addCategory(Intent.CATEGORY_DEFAULT)
            intent.data = Uri.parse("package:" + context.packageName)
        }
        try {
            context.startActivityForResult(intent, REQUEST_CODE_CHECK_PERMISSION)
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    /**
     * 是否需要更新埋点参数
     * @param isUpdateAnalysis true: 需要更新 false: 不需要更新
     */
    fun setUpdateAnalysisStatus(isUpdateAnalysis: Boolean) {
        this.isUpdateAnalysis = isUpdateAnalysis
    }

    internal inner class MyLocationListener : BDAbstractLocationListener() {

        override fun onReceiveLocation(bdLocation: BDLocation) {
            //获取纬度信息
            latitude = "" + bdLocation.latitude
            //获取经度信息
            longitude = "" + bdLocation.longitude
            getListData()
        }
    }

    private fun requestLocationPermissionDialog(){
        val rxPermissions = RxPermissions(notNullActivity)
        if (rxPermissions.isGranted(Manifest.permission.ACCESS_FINE_LOCATION)
                &&rxPermissions.isGranted(Manifest.permission.ACCESS_COARSE_LOCATION)) {
            requestLocationPermission()
        } else {
            PermissionDialogUtil.showPermissionInfoDialog(notNullActivity,
                    "药帮忙App需要申请定位权限，用于获取您当前所在地，为您展示当前所在地的商品数据"
            ) { requestLocationPermission() }
        }
    }

    @SuppressLint("CheckResult")
    private fun requestLocationPermission() {
        gpsIsOpen = PermissionUtil.checkGPSIsOpen(YBMAppLike.getAppContext())
        val rxPermissions = RxPermissions(notNullActivity)
        rxPermissions.request(Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION)
                .subscribe { granted ->
                    if (gpsIsOpen && granted) { //获取权限
                        empty_view.visibility = View.GONE
                        initLocationOption()
                    } else { //未获取
                        empty_view.visibility = View.VISIBLE
                    }
                }
    }

    /**
     * 初始化定位参数配置
     */
    private fun initLocationOption() { //定位服务的客户端。宿主程序在客户端声明此类，并调用，目前只支持在主线程中启动
        val locationClient = LocationClient(context?.applicationContext)
        //声明LocationClient类实例并配置定位参数
        val locationOption = LocationClientOption()
        val myLocationListener = MyLocationListener()
        //注册监听函数
        locationClient.registerLocationListener(myLocationListener)
        //可选，默认高精度，设置定位模式，高精度，低功耗，仅设备
        locationOption.locationMode = LocationClientOption.LocationMode.Hight_Accuracy
        //可选，默认gcj02，设置返回的定位结果坐标系，如果配合百度地图使用，建议设置为bd09ll;
        locationOption.setCoorType("gcj02")
        //可选，默认0，即仅定位一次，设置发起连续定位请求的间隔需要大于等于1000ms才是有效的
        locationOption.setScanSpan(0)
        //可选，设置是否需要地址信息，默认不需要
        locationOption.setIsNeedAddress(true)
        //可选，设置是否需要地址描述
        locationOption.setIsNeedLocationDescribe(true)
        //可选，设置是否需要设备方向结果
        locationOption.setNeedDeviceDirect(false)
        //可选，默认false，设置是否当gps有效时按照1S 1次频率输出GPS结果
        locationOption.isLocationNotify = false
        //可选，默认true，定位SDK内部是一个SERVICE，并放到了独立进程，设置是否在stop的时候杀死这个进程，默认不杀死
        locationOption.setIgnoreKillProcess(true)
        //可选，默认false，设置是否需要位置语义化结果，可以在BDLocation.getLocationDescribe里得到，结果类似于“在北京天安门附近”
        locationOption.setIsNeedLocationDescribe(false)
        //可选，默认false，设置是否需要POI结果，可以在BDLocation.getPoiList里得到
        locationOption.setIsNeedLocationPoiList(true)
        //可选，默认false，设置是否收集CRASH信息，默认收集
        locationOption.SetIgnoreCacheException(false)
        //可选，默认false，设置是否开启Gps定位
        locationOption.isOpenGps = true
        //可选，默认false，设置定位时是否需要海拔信息，默认不需要，除基础定位版本都可用
        locationOption.setIsNeedAltitude(false)
        //设置打开自动回调位置模式，该开关打开后，期间只要定位SDK检测到位置变化就会主动回调给开发者，该模式下开发者无需再关心定位间隔是多少，定位SDK本身发现位置变化就会及时回调给开发者
        // locationOption.setOpenAutoNotifyMode()
        //设置打开自动回调位置模式，该开关打开后，期间只要定位SDK检测到位置变化就会主动回调给开发者
        //locationOption.setOpenAutoNotifyMode(3000, 1, LocationClientOption.LOC_SENSITIVITY_HIGHT)
        //需将配置好的LocationClientOption对象，通过setLocOption方法传递给LocationClient对象使用
        locationClient.locOption = locationOption
        //开始定位
        locationClient.start()
    }
}