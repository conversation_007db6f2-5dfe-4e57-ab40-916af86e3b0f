package com.ybmmarket20.fragments;

import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;
import android.view.View;

import com.flyco.tablayout.SlidingTabLayout;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.FindPagerAdapter;
import com.ybmmarket20.common.BaseFragment;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.NoScrollViewPager;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;

/**
 * 发现界面
 */
public class FindFragment extends BaseFragment {

    @Bind(R.id.tl_discover)
    SlidingTabLayout tl;
    @Bind(R.id.vp_discover)
    NoScrollViewPager mViewPager;
    @Bind(R.id.view_masking)
    View view_masking;
    private ArrayList<String> mList_title;
    private List<Fragment> list_fragment;
    private FindPagerAdapter mPagerAdapter;

    DiscoverTabOftenBuyFragment mFragment1;
    DiscoverTabRecommendedFragment mFragment2;
    DiscoverTabAllianceFragment mFragment3;

    @Override
    public int getLayoutId() {
        return R.layout.fragment_find;
    }

    @Override
    protected void initTitle() {

    }

    @Override
    protected void initData(String content) {
        initFragmentTitle();
        mPagerAdapter = new FindPagerAdapter(getChildFragmentManager(), mList_title, list_fragment);
        mViewPager.setAdapter(mPagerAdapter);
        mViewPager.setOffscreenPageLimit(mList_title.size());
        mViewPager.setScroll(false);
        tl.setViewPager(mViewPager);
        //设置选中第一条
        tl.setCurrentTab(0);
        tl.onPageSelected(0);
    }

    public void setTabMasking(int visibility){
        view_masking.setVisibility(visibility);
    }

    public void setOnMaskingClickListener(View.OnClickListener l){
        view_masking.setOnClickListener(l);
    }

    private void initFragmentTitle() {
        mList_title = new ArrayList<>();
        list_fragment = new ArrayList<>();

        mList_title.add("常购清单");
        mFragment1 = DiscoverTabOftenBuyFragment.getInstance();
        list_fragment.add(mFragment1);

        mList_title.add("为你推荐");
        mFragment2 = DiscoverTabRecommendedFragment.getInstance();
        list_fragment.add(mFragment2);

        mList_title.add("用药指导");
        mFragment3 = DiscoverTabAllianceFragment.getInstance();
        list_fragment.add(mFragment3);
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            }

            @Override
            public void onPageSelected(int position) {
                if (position == 0) {
                    XyyIoUtil.track(XyyIoUtil.ACTION_FIND_PURCHASEHISTORY);
                } else if (position == 1) {
                    XyyIoUtil.track(XyyIoUtil.ACTION_FIND_RECOMMEND);
                }else if (position == 2) {
                    XyyIoUtil.track(XyyIoUtil.ACTION_FIND_COMBINEDMEDICATION);
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });
    }

    @Override
    protected RequestParams getParams() {
        return null;
    }

    @Override
    protected String getUrl() {
        return null;
    }

    private boolean hidden, isFirst;

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        this.hidden = hidden;
        if (hidden) {

        } else {
            mFragment1.onRefresh();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!hidden && !isFirst) {
            mFragment1.onRefresh();
        }
        isFirst = false;
        if(mFragment2!=null){
            mFragment2.resumeWebView();
        }
    }


}
