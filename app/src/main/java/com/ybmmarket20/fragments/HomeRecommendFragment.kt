package com.ybmmarket20.fragments

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import android.view.LayoutInflater
import android.view.ViewGroup
import com.chad.library.adapter.base.BaseQuickAdapter
import com.ybm.app.bean.NetError
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.adapter.PreferredBrandMultiItemAdapter
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RecommendResultBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.WrapRecommendBean
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.AdapterUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.utils.analysis.addAnalysisRequestParams
import com.ybmmarket20.utils.analysis.flowDataPageCommoditySearch
import com.ybmmarket20.utils.analysis.updateFlowData
import kotlinx.android.synthetic.main.fragment_home_activity_area.*
import org.json.JSONException
import org.json.JSONObject

/**
 * <AUTHOR>
 * @date 2020-04-20
 * @description 首页为你推荐
 */
class HomeRecommendFragment : HomeTabLicenseStatusFragment() {

    // 是否要更新mFlowData，点击tab按钮(为你推荐)调用接口后更新，该字段判断是否更新
    private var isUpdateAnalysis: Boolean = true
    private var isExposure: Boolean = true

    // 是否已经加载过
    private var isLoaded: Boolean = false
    lateinit var recommendAdapter: PreferredBrandMultiItemAdapter
    var wrapRowsList: ArrayList<WrapRecommendBean> = arrayListOf()

    /**
     * 是否开启一审资质审核状态监听
     */
    override fun onLicenseStatusEnable(): Boolean = true

    override fun initData(content: String?) {
        //super.initData(content)
        // 空实现，基类会调用网络请求，该页面要求点击tab后切换到该页面所以此处不应该调用网络加载
    }

    /**
     * 处理状态变更
     */
    override fun handleLicenseStatusChange(status: Int) {
        if (isLoaded) {
            wrapRowsList.clear()
            recommendAdapter.notifyDataSetChanged()
            getListData()
        }
    }

    override fun initView() {
        recommendAdapter = PreferredBrandMultiItemAdapter(wrapRowsList)
        rv_home_tab?.layoutManager = WrapLinearLayoutManager(context)
        val inflater = context?.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val emptyView = inflater.inflate(R.layout.layout_empty_view_home, null)
        emptyView.layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        recommendAdapter.emptyView = emptyView
        recommendAdapter.setOnItemClickListener(object : PreferredBrandMultiItemAdapter.OnListViewItemClickListener {
            override fun onItemClick(rows: RowsBean?) {
                // 竖向列表埋点
                val jsonObject = JSONObject()
                try {
                    jsonObject.put("id", rows?.id)
                } catch (e: JSONException) {
                    e.printStackTrace()
                }
                XyyIoUtil.track(XyyIoUtil.ACTION_HOME_R_PRODUCT, jsonObject,rows)
                openUrl("ybmpage://productdetail?${IntentCanst.PRODUCTID}=${rows?.id}")
            }

        })
        recommendAdapter.openLoadMore(pageSize, true)
        recommendAdapter.setOnLoadMoreListener(object : BaseQuickAdapter.RequestLoadMoreListener {
            override fun onLoadMoreRequested() {
                pageNum++
                getListData()
            }
        })
        rv_home_tab?.adapter = recommendAdapter
        LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).registerReceiver(object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                recommendAdapter.notifyDataSetChanged()
            }
        }, IntentFilter(IntentCanst.ACTION_LOGIN))
    }

    // 获取网络数据
    override fun getListData() {
        val requestParams = params
        if (!isUpdateAnalysis) {
            addAnalysisRequestParams(requestParams, mFlowData)
        }
        HttpManager.getInstance().post(if (SpUtil.isKa()) AppNetConfig.KA_RECOMMENDED_SKU_FOR_YOU_FRONT else AppNetConfig.RECOMMENDED_SKU_FOR_YOU_FRONT, requestParams, object : BaseResponse<RecommendResultBean>() {
            override fun onFailure(error: NetError) {
                recommendAdapter.notifyDataChangedAfterLoadMore(false)
            }

            override fun onSuccess(content: String, obj: BaseBean<RecommendResultBean>?, recommendResultBean: RecommendResultBean?) {
                if (obj != null && obj.isSuccess) {
                    isLoaded = true
                    if (recommendResultBean != null && recommendResultBean.rows != null && recommendResultBean.rows.size > 0) {
                        if (isUpdateAnalysis) {
                            updateFlowData(mFlowData, recommendResultBean.sptype, recommendResultBean.spid, recommendResultBean.sid)
                            flowDataPageCommoditySearch(mFlowData)
                            recommendAdapter.setFlowData(mFlowData)
                            isUpdateAnalysis = false
                        } else if (isExposure) {
                            recommendAdapter.setFlowData(mFlowData)
                            isExposure = false
                        }
                        val wrapRecommendBeans = arrayListOf<WrapRecommendBean>()
                        for (recommendListBean in recommendResultBean.rows) {
                            if (recommendListBean == null) break
                            if (recommendListBean.skuDtoList != null && recommendListBean.skuDtoList.size > 0) {
                                for (rowsBean in recommendListBean.skuDtoList) {
                                    if (rowsBean != null) {
                                        rowsBean.zhugeEventName = XyyIoUtil.ACTION_HOME_R_PRODUCT
                                        val wrapRecommendBean = WrapRecommendBean()
                                        wrapRecommendBean.itemType = WrapRecommendBean.CONTENT_NORMAL
                                        wrapRecommendBean.setRowsBean(rowsBean)
                                        wrapRecommendBeans.add(wrapRecommendBean)
                                    }
                                }
                                if (recommendListBean.preferredBrandBuineseDto != null) {
                                    val wrapRecommendBean = WrapRecommendBean()
                                    wrapRecommendBean.itemType = WrapRecommendBean.CONTENT_COMBO
                                    wrapRecommendBean.isBrand = true
                                    var list: MutableList<RowsBean>? = recommendListBean.preferredBrandBuineseDto.skuVOList
                                    if (list == null) {
                                        list = arrayListOf()
                                        recommendListBean.preferredBrandBuineseDto.skuVOList = list
                                    }
                                    for (rowsBean in list) {
                                        // 首页推荐数据来源 埋点
                                        rowsBean.zhugeEventName = XyyIoUtil.ACTION_HOME_R_PRODUCT
                                        rowsBean.itemType = RowsBean.content_31
                                    }

                                    val rb = RowsBean()
                                    rb.appUrl = recommendListBean.preferredBrandBuineseDto.appUrl
                                    rb.itemType = RowsBean.content_32
                                    list.add(rb)

                                    wrapRecommendBean.setBrandBean(recommendListBean.preferredBrandBuineseDto)
                                    wrapRecommendBeans.add(wrapRecommendBean)
                                }
                            }
                        }
                        if (wrapRecommendBeans.size > 0) {
                            if (isRefresh) {
                                wrapRowsList.clear()
                                isRefresh = false
                            }
                            wrapRowsList.addAll(wrapRecommendBeans)
                            // 折后价
                            AdapterUtils.getAfterDiscountPriceForHomeRecommend(wrapRecommendBeans, wrapRowsList, recommendAdapter)
                            recommendAdapter.notifyDataChangedAfterLoadMore(true)
                        }
                    }
                } else {
                    //recommendAdapter.notifyDataChangedAfterLoadMore(false)
                }
            }
        })
    }

    /**
     * 组装请求参数
     */
    override fun getParams(): RequestParams? = RequestParams().also {
        it.put("merchantId", SpUtil.getMerchantid())
        it.put("downFreshCount", "1")
        it.put("upFreshCount", "1")
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_home_activity_area;
    }

    /**
     * 通过该方法主动获取数据
     */
    override fun refresh() {
        super.refresh()
        pageNum = 0
//        isUpdateAnalysis = true
        isExposure = true
        getListData()
    }

    /**
     * 是否需要更新埋点参数
     * @param isUpdateAnalysis true: 需要更新 false: 不需要更新
     */
    fun setUpdateAnalysisStatus(isUpdateAnalysis: Boolean) {
        this.isUpdateAnalysis = isUpdateAnalysis
    }

}