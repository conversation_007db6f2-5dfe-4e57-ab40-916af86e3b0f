package com.ybmmarket20.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentStatePagerAdapter
import androidx.viewpager.widget.ViewPager
import com.flyco.tablayout.SlidingTabLayout
import com.ybmmarket20.R
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.indicator.OnPageChangeListener
import kotlinx.android.synthetic.main.fragment_my_red_envelope_class.*

/**
 * 我的红包以及tab
 */
abstract class MyRedEnvelopeClassFragment: Fragment(){

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val root = View.inflate(context, R.layout.fragment_my_red_envelope_class, null)
        val vpRedEnvelope = root.findViewById<ViewPager>(R.id.vp_red_envelope)
        val stlRedEnvelope = root.findViewById<SlidingTabLayout>(R.id.stl_red_envelope)
        vpRedEnvelope.offscreenPageLimit = 3
        vpRedEnvelope.adapter = fragmentManager?.let { MyRedEnvelopeFragmentAdapter(it) }
        stlRedEnvelope.setViewPager(vpRedEnvelope, getTitles())
        vpRedEnvelope.addOnPageChangeListener(object : ViewPager.SimpleOnPageChangeListener() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                selectTabTrack(position)
            }
        })
        XyyIoUtil.track("action_Me_RedPacket_menu2", hashMapOf(
                "name" to getTitles()[0]
        ))
        return root
    }

    abstract fun getTitles(): Array<String>

    abstract fun getFragments(): Array<Fragment>

    /**
     * 二级tab选中
     */
    fun selectTabTrack(position: Int) {
        XyyIoUtil.track("action_Me_RedPacket_menu2", hashMapOf(
                "name" to getTitles()[position]
        ))
    }

    inner class MyRedEnvelopeFragmentAdapter(fm: FragmentManager): FragmentStatePagerAdapter(fm, BEHAVIOR_SET_USER_VISIBLE_HINT) {
        override fun getCount(): Int = getTitles().size

        override fun getItem(position: Int): Fragment = getFragments()[position]

    }

}