package com.ybmmarket20.fragments;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.GridView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.apkfuns.logutils.LogUtils;
import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.PublishPicsAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.VideoPicPreviewEntity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.YBMBaseFragment;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.BitmapUtil;
import com.ybmmarket20.utils.FileUtil;
import com.ybmmarket20.utils.GalleryUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import static android.app.Activity.RESULT_OK;

/**
 * 添加图片与视频的fragment
 */
public class AddImageFragment extends YBMBaseFragment implements View.OnClickListener {
    public static final int CAMERA = 100;
    public static final int PICTURE = 200;
    public static final String EXTRA_SIZE = "allowedSize";
    public static final String EXTRA_PHOTO = "allow_photo";
    public static final String EXTRA_GALLERY = "allow_gallery";
    public static final String EXTRA_DATA = "net_data";
    public static final String EXTRA_VIDEO = "allowe_video";
    public static final String EXTRA_HINT = "hint";//提示文案
    public static final String EXTRA_DOMAIN = "domain";//图片上传的域
    public static final String EXTRA_IS_ADD = "allowe_add"; //添加按钮是否可点击
    private static final int DEFSIZE = 3;
    private GridView gv_imageviews;
    private PublishPicsAdapter adapter;
    List<VideoPicPreviewEntity> localPathList = new ArrayList<>();
    int photoCount;
    private RelativeLayout mainView;
    private Activity act;
    private File photoFile;
    private int size = 0;
    private boolean allow_photo = true;
    private boolean allow_gallery = true;
    private boolean allowe_video = true;
    private boolean firstItemAdd = true;
    private TextView tv_hint;
    private String hint;
    private String domain = "order";
    private boolean isAddPic = true;
    protected ArrayList<String> fileNameList;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        act = getNotNullActivity();
        Bundle bundle = getArguments();
        if (bundle != null) {
            size = bundle.getInt(EXTRA_SIZE);
            allow_photo = bundle.getBoolean(EXTRA_PHOTO);
            allow_gallery = bundle.getBoolean(EXTRA_GALLERY);
            allowe_video = bundle.getBoolean(EXTRA_VIDEO);
            hint = bundle.getString(EXTRA_HINT);
            domain = bundle.getString(EXTRA_DOMAIN);
            if (TextUtils.isEmpty(domain)) {
                domain = "order";
            }
            firstItemAdd = bundle.getBoolean(EXTRA_IS_ADD, true);
            List<String> list = bundle.getStringArrayList(EXTRA_DATA);
            if (list != null && list.size() > 0) {
                VideoPicPreviewEntity entity = new VideoPicPreviewEntity();
                for (String str : list) {
                    entity = new VideoPicPreviewEntity();
                    if (TextUtils.isEmpty(str)) {
                        continue;
                    }
                    if (str.startsWith("http") || str.startsWith("Http")) {

                    } else {
                        if (str.startsWith("/")) {
                            str = AppNetConfig.getCDNHost() + str;
                        } else {
                            str = AppNetConfig.getCDNHost() + "/" + str;
                        }

                    }
                    entity.setV_url(str);
                    entity.setPre_url(str);
                    entity.setFile_type(VideoPicPreviewEntity.FILE_PIC_TYPE);
                    entity.setUrl_type(VideoPicPreviewEntity.URL_NETWORK_TYPE);
                    localPathList.add(entity);
                }
            }
        }
        mainView = (RelativeLayout) LayoutInflater.from(getNotNullActivity()).inflate(R.layout.fragment_add_image, null);
        if (size <= 0) {
            size = DEFSIZE;
        }
        initView();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (mainView.getParent() != null) {
            ((ViewGroup) mainView.getParent()).removeView(mainView);
        }
        return mainView;
    }

    private void initView() {
        tv_hint = (TextView) mainView.findViewById(R.id.tv_hint);
        if (!TextUtils.isEmpty(hint)) {
            tv_hint.setText(hint);
        }
        adapter = new PublishPicsAdapter(this, localPathList, size, allowe_video, allow_gallery, allow_photo, firstItemAdd, tv_hint);
        adapter.setIsCanClick(isAddPic);
        gv_imageviews = (GridView) mainView.findViewById(R.id.gv_imageviews);
        gv_imageviews.setAdapter(adapter);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_CANCELED) {
            photoFile = null;
            return;
        }
        gv_imageviews.setVisibility(View.VISIBLE);
        String path = act.getExternalCacheDir().getAbsolutePath() + "/ybm_" + System.currentTimeMillis() + ".png";
        if (requestCode == FileUtil.CAMERA) {
            //来自于相机返回的结果
            File file = null;
            if (data == null) {//设置的图片存储的uri
                file = photoFile;
                if (file != null && file.exists()) {
                    if (BitmapUtil.compressFile(file.getAbsolutePath(), path)) {
                        file = new File(path);
                    }
                }
            } else {//没有设置图片的uri
                Bundle bundle = data.getExtras();
                if (bundle != null) {
                    Bitmap bitmap = (Bitmap) bundle.get("data");
                    file = BitmapUtil.bitmapToFile(bitmap, path);
                } else {//android 5.0 系统
                    if (data.getData() != null && !TextUtils.isEmpty(data.getData().getEncodedPath())) {
                        if (BitmapUtil.compressFile(data.getData().getEncodedPath(), path)) {
                            file = new File(path);
                        }
                    }
                }
            }
            if (file == null || !file.exists()) {
                path = null;
                ToastUtils.showShort("没有找到图片");
                return;
            }
            saveUpPic(path);
        } else if (requestCode == FileUtil.PICTURE && resultCode == RESULT_OK && data != null) {
            //来自于图库返回的结果
            Uri selectedImage = data.getData();
            String pathResult = GalleryUtil.getFilePathByUri(act, selectedImage);
            if (TextUtils.isEmpty(pathResult) || !BitmapUtil.compressFile(pathResult, path)) {
                path = null;
                ToastUtils.showShort("没有找到图片");
                return;
            }
            saveUpPic(path);
        }

    }

    public void setPicture(File photoFile) {
        this.photoFile = photoFile;
    }

    private void saveUpPic(List<VideoPicPreviewEntity> file) {
        if (null != file && file.size() > 0) {
            upFile(file);
        }
    }

    private void saveUpPic(String file) {
        VideoPicPreviewEntity entity = new VideoPicPreviewEntity();
        entity.setFile_type(VideoPicPreviewEntity.FILE_PIC_TYPE);
        entity.setUrl_type(VideoPicPreviewEntity.URL_LOCAL_TYPE);
        entity.setPre_url(file);
        List<VideoPicPreviewEntity> list = new ArrayList<>(4);
        list.add(entity);
        saveUpPic(list);
    }

    /**
     * 上传图片
     *
     * @param picFilePath
     */
    public void upFile(final List<VideoPicPreviewEntity> picFilePath) {
        for (VideoPicPreviewEntity videoPicPreviewEntity : picFilePath) {
            if (videoPicPreviewEntity.getFile_type() == VideoPicPreviewEntity.FILE_PIC_TYPE) {
                photoCount++;
                uploadFile(videoPicPreviewEntity.getPre_url());
            } else {
                adapter.notifyDataSetChanged();
            }
        }
    }

    /**
     * @param path 要上传的文件路径
     * @throws Exception
     */
    public void uploadFile(final String path) {
        if (TextUtils.isEmpty(path)) {
            ToastUtils.showShort("请选择图片在上传");
        }
        final String merchantid = HttpManager.getInstance().getMerchant_id();
        File file = new File(path);
        if (file.exists() && file.length() > 0) {
            showProgress();
            RequestParams params = new RequestParams();
            params.put("uploadPath", "ybm/" + domain + "/" + merchantid + "/");
            final String fileName = path.substring(path.lastIndexOf("/") + 1, path.lastIndexOf("."));
            params.put("targetFileName", fileName);
            params.put("file", file);
            HttpManager.getInstance().post(AppNetConfig.PICTURE_CALL, params, new BaseResponse<EmptyBean>() {

                @Override
                public void onSuccess(String content,BaseBean<EmptyBean> data, EmptyBean obj) {
                    dismissProgress();
                    photoCount--;
                    if (data != null && data.isSuccess()) {
                        LogUtils.d("上传完成，开始更新");
                        VideoPicPreviewEntity entity = new VideoPicPreviewEntity();
                        entity.setPre_url(path);
                        entity.setV_url("/ybm/" + domain + "/" + merchantid + "/" + fileName + ".png");
                        entity.setFile_type(VideoPicPreviewEntity.FILE_PIC_TYPE);
                        entity.setUrl_type(VideoPicPreviewEntity.URL_LOCAL_TYPE);
                        localPathList.add(entity);
                        adapter.notifyDataSetChanged();
                    } else {
                        ToastUtils.showShort("上传失败");
                    }
                }

                @Override
                public void onFailure(NetError error) {
                    photoCount--;
                    super.onFailure(error);
                    ToastUtils.showShort("图片添加失败");
                }
            });

        } else {
            ToastUtils.showShort("文件不存在");
        }
    }


    public static Bundle getBundle2Me(int allowedSize, boolean takePhoto, boolean takeVideo, boolean gallery) {
        Bundle bundle = new Bundle();
        bundle.putInt(EXTRA_SIZE, allowedSize);
        bundle.putBoolean(EXTRA_PHOTO, takePhoto);
        bundle.putBoolean(EXTRA_VIDEO, takeVideo);
        bundle.putBoolean(EXTRA_GALLERY, gallery);
        return bundle;
    }

    public static Bundle getBundle2Me(Context context, int allowedSize, boolean takePhoto, boolean takeVideo, boolean gallery, ArrayList<String> list) {
        Bundle bundle = new Bundle();
        bundle.putInt(EXTRA_SIZE, allowedSize);
        bundle.putBoolean(EXTRA_PHOTO, takePhoto);
        bundle.putBoolean(EXTRA_VIDEO, takeVideo);
        bundle.putBoolean(EXTRA_GALLERY, gallery);
        bundle.putStringArrayList(EXTRA_DATA, list);
        return bundle;
    }

    /**
     * @param context
     * @param allowedSize
     * @param takePhoto
     * @param takeVideo
     * @param gallery
     * @param hintText    提示文案
     * @return
     */
    public static Bundle getBundle2Me(Context context, int allowedSize, boolean takePhoto, boolean takeVideo, boolean gallery, String hintText) {
        Bundle bundle = new Bundle();
        bundle.putInt(EXTRA_SIZE, allowedSize);
        bundle.putBoolean(EXTRA_PHOTO, takePhoto);
        bundle.putBoolean(EXTRA_VIDEO, takeVideo);
        bundle.putBoolean(EXTRA_GALLERY, gallery);
        bundle.putString(EXTRA_HINT, hintText);
        return bundle;
    }

    /**
     * @param context
     * @param allowedSize
     * @param takePhoto
     * @param takeVideo
     * @param gallery
     * @param hintText    提示文案
     * @return
     */
    public static Bundle getBundle2Me(Context context, int allowedSize, boolean takePhoto, boolean takeVideo, boolean gallery, String hintText, String domain) {
        Bundle bundle = new Bundle();
        bundle.putInt(EXTRA_SIZE, allowedSize);
        bundle.putBoolean(EXTRA_PHOTO, takePhoto);
        bundle.putBoolean(EXTRA_VIDEO, takeVideo);
        bundle.putBoolean(EXTRA_GALLERY, gallery);
        bundle.putString(EXTRA_HINT, hintText);
        bundle.putString(EXTRA_DOMAIN, domain);
        return bundle;
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }

    /**
     * 点击确认按钮事件
     */
    public boolean confirm() {
        if (photoCount == 0) {
            setUpLoadFile();
            return true;
        } else {
            ToastUtils.showShort("还有图片没有上传完成");
            return false;
        }
    }

    /**
     * 设置上传成功的图片
     */
    private void setUpLoadFile() {
        fileNameList = new ArrayList<String>();
        for (VideoPicPreviewEntity entity : localPathList) {
            if (entity.getFile_type() == VideoPicPreviewEntity.FILE_PIC_TYPE) {
                fileNameList.add(entity.getV_url());
            }
        }
    }

    public List<String> getFileNameList() {
        return this.fileNameList;
    }

    /**
     * 第一个按钮是否为增加按钮
     *
     * @param firstItemAdd
     */
    public void setFirstItemAdd(boolean firstItemAdd) {
        this.firstItemAdd = firstItemAdd;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
    }


    /**
     * 添加按钮是否可以点击
     *
     * @param isAddPic
     */
    public void setIsCanAddPic(boolean isAddPic) {
        if (adapter != null) {
            adapter.setIsCanClick(isAddPic);
            adapter.notifyDataSetInvalidated();
        }
    }

    /**
     * 清除图片重新刷新布局
     *
     * @param isAddPic
     */
    public void clearPicGridView(boolean isAddPic) {
        localPathList.clear();
        adapter.notifyDataSetChanged();
    }

    @Override
    public void onClick(View v) {
        if (adapter.getCount() < size && gv_imageviews != null && gv_imageviews.getChildCount() > 0) {
            View view = gv_imageviews.getChildAt(gv_imageviews.getChildCount() - 1);
            if (view != null) {
                view.performClick();
            }
        }
    }
}
