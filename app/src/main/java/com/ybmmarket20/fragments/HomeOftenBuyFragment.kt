package com.ybmmarket20.fragments

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RowsListBeanWrapper
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.AdapterUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.*
import org.json.JSONException
import org.json.JSONObject

/**
 * <AUTHOR>
 * @date 2020-04-20
 * @description 首页常购清单
 */
class HomeOftenBuyFragment : HomeTabLicenseStatusFragment() {
    private var isInit: Boolean = false

    // 是否已经加载过
    private var isLoaded: Boolean = false
    override var pageNum: Int = 0

    // 是否要更新mFlowData，点击tab按钮(为你推荐)调用接口后更新，该字段判断是否更新
    private var isUpdateAnalysis: Boolean = true

    /**
     * 是否开启一审资质审核状态监听
     */
    override fun onLicenseStatusEnable(): Boolean = true

    override fun initData(content: String?) {
        //super.initData(content)
        // 空实现，基类会调用网络请求，该页面要求点击tab后切换到该页面所以此处不应该调用网络加载
        adapter?.setShowOftenBuyTypeName(true)
    }

    /**
     * 处理状态变更
     */
    override fun handleLicenseStatusChange(status: Int) {
        if (adapter != null && isLoaded) {
            rowsList.clear()
            adapter?.notifyDataSetChanged()
            getListData()
        }
    }

    /**
     * 获取网络数据
     */
    override fun getListData() {
        val requestParams = params
        if (!isUpdateAnalysis) {
            addAnalysisRequestParams(requestParams, mFlowData)
        }
        HttpManager.getInstance().post(AppNetConfig.FIND_MY_ORDER_LIST, requestParams, object : BaseResponse<RowsListBeanWrapper>() {

            override fun onSuccess(content: String?, obj: BaseBean<RowsListBeanWrapper>?, t: RowsListBeanWrapper?) {
                if (obj != null && t != null && t.pageData != null && t.pageData.rows != null && obj.isSuccess && t.pageData.rows != null) {
                    isLoaded = true
                    if (isRefresh) {
                        rowsList.clear()
                        isRefresh = false
                    }
                    if (isUpdateAnalysis) {
                        updateFlowData(mFlowData, t.sptype, t.spid, t.sid)
                        flowDataPageCommoditySearch(mFlowData)
                        adapter?.setFlowData(mFlowData)
                        isUpdateAnalysis = false
                    }
                    if (t.pageData.rows.size == 10) {
                        rowsList.addAll(t.pageData.rows)
                        // 请求并更新折后价
                        adapter?.let { AdapterUtils.getAfterDiscountPrice(t.pageData.rows, it) }
                        adapter?.notifyDataChangedAfterLoadMore(true)
                    } else if (t.pageData.rows.size < 10) {
                        rowsList.addAll(t.pageData.rows)
                        // 请求并更新折后价
                        adapter?.let { AdapterUtils.getAfterDiscountPrice(t.pageData.rows, it) }
                        adapter?.notifyDataChangedAfterLoadMore(false)
                    }
                }
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
                adapter?.notifyDataChangedAfterLoadMore(false)
            }
        })
    }

    override fun initView() {
        super.initView()
        adapter?.setOnListItemClickListener { rows ->
            // 竖向列表埋点
            val jsonObject = JSONObject()
            try {
                jsonObject.put("id", rows?.id)
            } catch (e: JSONException) {
                e.printStackTrace()
            }
            XyyIoUtil.track(XyyIoUtil.ACTION_HOME_PH_PRODUCT, jsonObject, rows)
            openUrl("ybmpage://productdetail/" + rows?.id)
        }
        val inflater = context?.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val emptyView = inflater.inflate(R.layout.layout_empty_view_home, null)
        emptyView.layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        adapter?.emptyView = emptyView
    }


    /**
     * 组装请求参数
     */
    override fun getParams(): RequestParams? = RequestParams().also {
        it.put("merchantId", SpUtil.getMerchantid())
        it.put("limit", pageSize.toString())
        it.put("offset", pageNum.toString())
        it.put("spFrom", AnalysisConst.FlowDataChain.FLOWDATACHAIN_FROM_HOME_OFTEN_BUY_LIST)
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_home_activity_area
    }

    /**
     * 通过该方法主动获取数据
     */
    override fun refresh() {
        super.refresh()
        pageNum = 0
        rowsList.clear()
        getListData()
    }

    /**
     * 是否需要更新埋点参数
     * @param isUpdateAnalysis true: 需要更新 false: 不需要更新
     */
    fun setUpdateAnalysisStatus(isUpdateAnalysis: Boolean) {
        this.isUpdateAnalysis = isUpdateAnalysis
    }
}