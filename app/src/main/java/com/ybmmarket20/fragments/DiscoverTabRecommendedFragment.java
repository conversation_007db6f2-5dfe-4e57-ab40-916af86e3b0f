
package com.ybmmarket20.fragments;


import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.os.Handler;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import android.view.View;
import android.webkit.JavascriptInterface;

import com.tencent.smtt.export.external.interfaces.PermissionRequest;
import com.tencent.smtt.sdk.ValueCallback;
import com.tencent.smtt.sdk.WebChromeClient;
import com.tencent.smtt.sdk.WebSettings;
import com.xyyio.analysis.stat.XyyIoSDK;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.utils.NetUtil;
import com.ybmmarket20.BuildConfig;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.SelectFileBean;
import com.ybmmarket20.common.BaseFragment;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.message.Message;
import com.ybmmarket20.utils.DialogUtil;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.CommonDialogLayout;
import com.ybmmarket20.view.X5WebView;

import butterknife.Bind;

import static android.view.View.GONE;

/**
 * 为你推荐
 */
public class DiscoverTabRecommendedFragment extends BaseFragment {

    @Bind(R.id.ll_root)
    View mRootLayout;
    @Bind(R.id.webView)
    X5WebView wbX5;
    private String url;
    private XyyIoUtil.XyyJS xyyJs = new XyyIoUtil.XyyJS();

    public static DiscoverTabRecommendedFragment getInstance() {
        return new DiscoverTabRecommendedFragment();
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_discover_recommended;
    }

    @Override
    protected void initTitle() {

    }

    private BroadcastReceiver broadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            loadUrl();
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    wbX5.reload();
                }
            }, 1000);

        }
    };

    @Override
    protected void initData(String content) {
        IntentFilter intentFilter = new IntentFilter(IntentCanst.ACTION_REFRESH_RECOMMEND);
        if (broadcastReceiver != null)
            LocalBroadcastManager.getInstance(getNotNullActivity()).registerReceiver(broadcastReceiver, intentFilter);
        initWebSetting();
        loadUrl();
    }

    private void loadUrl() {
        if (wbX5 != null) {
            url = "debug".equals(BuildConfig.BUILD_TYPE) ? AppNetConfig.getStaticHost2Https() + AppNetConfig.URL_RECOMMEND_BETA : AppNetConfig.getStaticHost2Https() + AppNetConfig.URL_RECOMMEND_RELEASE;
            url += "&merchantId=" + HttpManager.getInstance().getMerchant_id();
            wbX5.loadUrl(url);
        }
    }

    public void resumeWebView() {
        if (wbX5 != null)
            wbX5.callJs("window._self_fn.change_page", "");
    }


    @Override
    public void onResume() {
        super.onResume();
        if (xyyJs != null) {
            xyyJs.onResume();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (xyyJs != null) {
            xyyJs.onPause();
        }
    }


    public class Msg {

        @JavascriptInterface
        public void cutDown() {
            wbX5.post(new Runnable() {
                @Override
                public void run() {
                    Message.instance.reduce();
                }
            });
        }

    }

    private void initWebSetting() {
        WebSettings settings = wbX5.getSettings();
        settings.setJavaScriptEnabled(true);
        wbX5.addJavascriptInterface(new Msg(), "hybird");
        wbX5.addJavascriptInterface(xyyJs, "sdkTracker");

        //设置QQ x5监听
        if (wbX5 != null) {
            wbX5.setCacheMode(X5WebView.CACHE_NO);
            wbX5.setWebViewListener(new X5WebView.WebViewListener() {

                @Override
                public void onReceivedTitle(com.tencent.smtt.sdk.WebView webView, String s) {

                }

                @Override
                public void setStatusColor(com.tencent.smtt.sdk.WebView webView, int color) {
                    mRootLayout.setBackgroundColor(color);
                }

                @Override
                public void onReceivedError(com.tencent.smtt.sdk.WebView view, int errorCode, String description, String failingUrl) {
                    showError(errorCode, failingUrl);
                }

                @Override
                public void onScrollChanged(int x, int y, int oldx, int oldy) {

                }

                @Override
                public void setToolBar(String type, String content) {

                }

                @Override
                public boolean setRightMenu(String title, final String action) {
                    return true;
                }

                @Override
                public boolean setTitleStyle(String startColor, String endColor) {
                    return true;
                }

                @Override
                public boolean setControlTitle(String type, String startColor, String endColor) {
                    return true;
                }

                @Override
                public boolean setShowAdPop(boolean isShow) {
                    return true;
                }

                @Override
                public boolean setShareMenu(String title, String action) {
                    return false;
                }

                @Override
                public boolean controlMallShare(String title, String url, String desc, String desc_pyq, String type) {
                    return false;
                }

                @Override
                public void getImageVideo(SelectFileBean selectFileBean) {

                }

                @Override
                public void closeWebView() {

                }

                @Override
                public void goBack() {

                }

                @Override
                public void setNavigationBar(String bgColor, String fontColor) {

                }

                @Override
                public void downLoadFile(String url) {

                }
            });

            wbX5.setFileChooserListener(new X5WebView.FileChooserListener() {

                @Override
                public void onFileChooserApiLow(ValueCallback<Uri> uploadFile, String acceptType, String capture) {

                }

                @Override
                public void onFileChooserApiHei(ValueCallback<Uri[]> uploadFile, WebChromeClient.FileChooserParams fileChooserParams) {

                }

                @Override
                public void onRequestPermission(PermissionRequest permissionRequest) {

                }
            });

//            if (BaseYBMApp.getApp().
//
//                    isDebug()) {//测试版本不缓存
//                wbX5.setCacheMode("0");
//            }

        }
    }

    private void showError(int errorCode, final String errorUrl) {
        mRootLayout.findViewById(R.id.rl_network_tip).setVisibility(View.VISIBLE);
        mRootLayout.findViewById(R.id.tv_fresh).setOnClickListener(v -> {
            if (NetUtil.getNetworkState(getContext()) == NetUtil.NETWORN_NONE) {
                DialogUtil.showCommonStatus(CommonDialogLayout.CommonTip.noNet, "网络无法连接");
                return;
            }
            wbX5.setVisibility(View.VISIBLE);
            mRootLayout.findViewById(R.id.tv_error).setVisibility(GONE);
            if (wbX5 != null) {
                wbX5.reload();
            }
        });
        wbX5.setVisibility(GONE);
    }

    @Override
    protected RequestParams getParams() {
        return null;
    }

    @Override
    protected String getUrl() {
        return null;
    }


    @Override
    public void onHiddenChanged(boolean hidden) {
        if (hidden) {
            this.onPause();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (broadcastReceiver != null)
            LocalBroadcastManager.getInstance(getContext()).unregisterReceiver(broadcastReceiver);
    }
}
