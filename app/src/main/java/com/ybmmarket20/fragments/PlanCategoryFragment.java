package com.ybmmarket20.fragments;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import com.google.android.material.appbar.AppBarLayout;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.RadioGroup;
import android.widget.TextView;

import com.apkfuns.logutils.LogUtils;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.ElectronicPlanDetailActivity;
import com.ybmmarket20.adapter.PlanProduct2Adapter;
import com.ybmmarket20.adapter.PlanProductAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.PlanProductInfoBean;
import com.ybmmarket20.bean.PlanProductInfoData;
import com.ybmmarket20.bean.SearchFilterBean;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.LicenseStatusFragment;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.ViewOnClickListener;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.view.AllProductPopWindow;
import com.ybmmarket20.view.BaseFilterPopWindow;
import com.ybmmarket20.view.DividerLine;
import com.ybmmarket20.view.FiltrateClassifyPop;
import com.ybmmarket20.view.LeftPopWindow;
import com.ybmmarket20.view.ListFilterPopWindow;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * Created by ybm on 2017/6/21.
 * 计划单分类商品
 */

public class PlanCategoryFragment extends LicenseStatusFragment implements PlanProductAdapter.OnSelectAllListener,
        PlanProductAdapter.OnSwipeListener, PlanProductAdapter.OnItemClickListener {

    @Bind(R.id.crv_product)
    CommonRecyclerView crvProduct;
    @Bind(R.id.cb_select)
    CheckBox cbSelect;
    @Bind(R.id.rb_all)
    TextView rbAll;
    @Bind(R.id.tv_product_num)
    TextView tvOrderNum;
    @Bind(R.id.ll_filter)
    LinearLayout llFilter;
    @Bind(R.id.appbar)
    AppBarLayout appbar;
    @Bind(R.id.rg_product_tab)
    RadioGroup radioGroup;
    @Bind(R.id.rb_filter)
    RadioGroup radioFilter;
    @Bind(R.id.rb_all_category)
    TextView mRbAllCategory;
    @Bind(R.id.rb_comprehensive_ranking)
    TextView mRbComprehensiveRanking;
    @Bind(R.id.tv_shop)
    TextView mTvShop;
    private ElectronicPlanDetailActivity mActivity;
    private String mPlanId;
    private PlanProduct2Adapter mAdapter;
    private List<PlanProductInfoBean> mData = new ArrayList<>();
    private List<PlanProductInfoBean> allProductList = new ArrayList<>();
    private ListFilterPopWindow mPopWindowRanking;
    private AllProductPopWindow mPopWindowProduct;
    private FiltrateClassifyPop mClassifyPop;
    private String categoryId;//分类
    private String fob;//排序
    private String drugClassification;//药品类型 甲类otc ...
    private String maxPrice;//最大价格
    private String minPrice;//最小价格
    private String manufacturer = "";//厂家
    private BroadcastReceiver br;
    private String searchKey = null;

    public static PlanCategoryFragment getInstance(Bundle bundle) {
        PlanCategoryFragment fragment = new PlanCategoryFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (context instanceof ElectronicPlanDetailActivity) {
            mActivity = (ElectronicPlanDetailActivity) context;
        }
    }


    @Override
    protected void initData(String content) {
        Bundle bundle = getArguments();
        if (bundle != null) {
            mPlanId = bundle.getString(ElectronicPlanDetailActivity.BUNDLE_PLAN_ID);
        }

        mAdapter = new PlanProduct2Adapter(mData);
        mAdapter.setOnItemClickListener(this);
        mAdapter.setOnSelectAllListener(this);
        mAdapter.setOnSwipeListener(this);
        mAdapter.setEnableLoadMore(false);
        View emptyView = getNotNullActivity().getLayoutInflater().inflate(R.layout.layout_empty_view, (ViewGroup) crvProduct.getParent(), false);
        mAdapter.setEmptyView(emptyView);
        DividerLine divider = new DividerLine(DividerLine.VERTICAL);
        divider.setSize(1);
        divider.setColor(0xfff1f1f1);
        crvProduct.addItemDecoration(divider);
        crvProduct.setShowAutoRefresh(false);
        crvProduct.setRefreshEnable(false);
        crvProduct.setAdapter(mAdapter);
        YBMAppLike.changeThemeBg(R.drawable.base_header_dynamic_bg, radioGroup);
        getAllPlanProduct(null);
        rbAll.setActivated(true);
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(IntentCanst.ACTION_PLAN_EDIT_PRODUCT_NUM);
        br = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                int num = intent.getIntExtra("num",0);
                int from = intent.getIntExtra("from",0);
                String code = intent.getStringExtra("code");
                if(!TextUtils.isEmpty(code) && from ==1){
                    if(num == -1){
                        int index = -1;
                        for(int a=0;a<allProductList.size();a++){
                            if(code.equals(allProductList.get(a).code)){
                                index = a;
                            }
                        }
                        if(index>=0){
                            PlanProductInfoBean bean = allProductList.get(index);
                            allProductList.remove(index);
                            mData.remove(bean);
                            mAdapter.notifyDataSetChanged();
                        }
                        tvOrderNum.setText("已选择"+mAdapter.getSelectProduct().size()+"个");
                        cbSelect.setChecked(false);
                        if(mData == null || mData.isEmpty()){
                            return;
                        }
                        for (PlanProductInfoBean bean : mData) {
                            if (bean.isChecked == 0) {
                                return;
                            }
                        }
                        cbSelect.setChecked(true);
                    }else {
                        for(PlanProductInfoBean bean : allProductList){
                            if(code.equals(bean.code)){
                                bean.purchaseNumber = num;
                                if(mAdapter !=null){
                                    mAdapter.notifyDataSetChanged();
                                }
                            }
                        }
                    }
                }
            }
        };
        LocalBroadcastManager.getInstance(getContext()).registerReceiver(br, intentFilter);
    }

    @Override
    protected void initTitle() {

    }

    @Override
    protected RequestParams getParams() {
        return null;
    }

    @Override
    protected String getUrl() {
        return null;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_plan_category;
    }

    @OnClick({R.id.rl_bottom_add_cart, R.id.tv_add_purchase_order})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.rl_bottom_add_cart:
                if (mAdapter != null) {
                    cbSelect.setChecked(!cbSelect.isChecked());
                    setSelectAll(cbSelect.isChecked());
                }
                break;
            case R.id.tv_add_purchase_order:
                addCart();
                break;
        }
    }

    private void setSelectAll(boolean checked){
        checked2Net(checked,mData.toArray(new PlanProductInfoBean[]{}));
        for(PlanProductInfoBean bean:mData){
            if(bean !=null){
                bean.isChecked = checked?1:0;
         }
        }
        mAdapter.notifyDataSetChanged();
        tvOrderNum.setText("已选择"+mAdapter.getSelectProduct().size()+"个");
    }

    private void addCart() {
        if (mAdapter == null) {
            ToastUtils.showShort("没有可添加的商品");
            return;
        }

        Map<String, String> products = mAdapter.getSelectProduct();
        if (products == null || products.isEmpty()) {
            ToastUtils.showShort("请选择需要添加到购物车的商品");
            return;
        }
        Intent intent = new Intent(IntentCanst.ACTION_ADD_PRODUCT);
        intent.putExtra("isAdd", true);
        LocalBroadcastManager.getInstance(BaseYBMApp.getAppContext()).sendBroadcast(intent);
        String merchant_id = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("productIds", new JSONObject(products).toString());
        HttpManager.getInstance().post(AppNetConfig.PLAN_PRODUCT_TO_CART, params, new BaseResponse<EmptyBean>() {
            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean data) {
                if (obj != null && obj.isSuccess()) {
//                    ToastUtils.showShort("成功加入采购单");
                    //通知页面去更新购物车的数量
                    LocalBroadcastManager.getInstance(BaseYBMApp.getAppContext()).sendBroadcast(new Intent(IntentCanst.ACTION_SHOPNUMBER));
                    //更新图标
                    for(PlanProductInfoBean bean :allProductList){
                        if(products.containsKey(bean.productId+"")){
                            LogUtils.d("key:"+bean.productName);
                            bean.isCart = 1;
                        }
                    }

                    setPlanningSchedule();

                    mAdapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
            }
        });
    }

    /*
    * 加入成功修改计划单状态
    * */
    private void setPlanningSchedule() {
        if(mTvShop==null){
            return;
        }

        String merchant_id = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("planningScheduleId", mPlanId);
        HttpManager.getInstance().post(AppNetConfig.UPDATE_PLAN_SCHEDULE_STATUS, params, new BaseResponse<EmptyBean>() {
            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean data) {
                if (obj != null && obj.isSuccess()) {
                    ToastUtils.showShort("成功加入购物车");
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
            }
        });

    }

    /**
     * 获取所有的商品
     */
    public void getAllPlanProduct() {
        getAllPlanProduct(null);
    }

    /**
     * 获取所有的商品
     */
    public void getAllPlanProduct(String key) {
        searchKey = key;
        String merchant_id = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("planningScheduleId", mPlanId);
        boolean isAll = true;
        if(!TextUtils.isEmpty(categoryId)){
            params.put("categoryId",categoryId);
            isAll = false;
        }
        if (!TextUtils.isEmpty(fob)) {
            params.put("direction", fob);
            params.put("property", "fob");
        }
        if(!TextUtils.isEmpty(minPrice)){
            params.put("minPrice", minPrice);
            isAll = false;
        }
        if(!TextUtils.isEmpty(maxPrice)){
            params.put("maxPrice", maxPrice);
            isAll = false;
        }
        if(!TextUtils.isEmpty(drugClassification)){
            params.put("drugClassificationStr", drugClassification);
            isAll = false;
        }
        if(!TextUtils.isEmpty(manufacturer)){
            params.put("manufacturer", manufacturer);
            isAll = false;
        }
        params.put("status", "3");
        showProgress();
        boolean finalIsAll = isAll;
        HttpManager.getInstance().post(AppNetConfig.PLAN_SCHEDULE_DETAIL, params, new BaseResponse<PlanProductInfoData>(){
            @Override
            public void onSuccess(String content, BaseBean<PlanProductInfoData> obj, PlanProductInfoData data) {
                super.onSuccess(content, obj, data);
                dismissProgress();
                if (obj == null || !obj.isSuccess()) {
                    ToastUtils.showShort("请求失败");
                    return;
                }
                updateLicenseStatus(data.licenseStatus, getCurrentLicenseStatusListener());
                if (data.list == null) {
                    data.list = new ArrayList<>();
                }
                if(finalIsAll){
                    allProductList = data.list;
                }else {//更新全部列表中的数据
                    for(PlanProductInfoBean bean:data.list){
                        int index = allProductList.indexOf(bean);
                        if(index>=0){
                            allProductList.remove(index);
                            allProductList.add(index,bean);
                        }
                    }
                }
                setListData(data.list);
                if(!TextUtils.isEmpty(key)){
                    search(key);
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                dismissProgress();
                ToastUtils.showShort("获取商品列表失败");
            }
        });
    }

    private void setListData( List<PlanProductInfoBean> data){
        if(tvOrderNum==null){
            return;
        }
        if(data == null){
            data = new ArrayList<>();
        }
        mData.clear();
        mData.addAll(data);
        mAdapter.setNewData(mData);
        tvOrderNum.setText("已选择"+mAdapter.getSelectProduct().size()+"个");
        cbSelect.setChecked(false);
        if(mData == null || mData.isEmpty()){
            return;
        }
        for (PlanProductInfoBean bean : mData) {
            if (bean.isChecked != 1) {
                return;
            }
        }
        cbSelect.setChecked(true);
    }


    @Override
    public void onItemClick(int position) {

    }

    @Override
    public void onEditNum(final int position) {

    }

    @Override
    public void onDelete(final int position) {
        final AlertDialogEx alert = new AlertDialogEx(getNotNullActivity());
        alert.setTitle("删除");
        alert.setMessage("您确认删除吗？");
        alert.setCancelButton("取消", null);
        alert.setConfirmButton("确定", new ViewOnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                String productCode = mData.get(position).code;
                String planId = mData.get(position).planningScheduleId + "";
                if (TextUtils.isEmpty(productCode) || TextUtils.isEmpty(planId)) {
                    return;
                }
                deleteProduct(productCode, planId,position);
            }
        });
        alert.show();
    }

    @Override
    public void onSwipe(int position) {

    }


    @Override
    public void isChecked(boolean isChecked,PlanProductInfoBean beanInfo) {
        checked2Net(isChecked,beanInfo);
        tvOrderNum.setText("已选择"+mAdapter.getSelectProduct().size()+"个");
        if(isChecked&&!cbSelect.isChecked()) {//判断是不是选中
            for (PlanProductInfoBean bean : mData) {
                if (bean.isChecked == 0) {
                    return;
                }
            }
            cbSelect.setChecked(true);
        }else if(!isChecked&&cbSelect.isChecked()){
            for (PlanProductInfoBean bean : mData) {
                if (bean.isChecked == 0) {
                    cbSelect.setChecked(false);
                    return;
                }
            }
        }
    }

    private void checked2Net(final boolean isChecked,PlanProductInfoBean... beanInfos){
        String merchant_id = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        StringBuffer sb = new StringBuffer();
        for(int a=0;a<beanInfos.length;a++) {
            if(a==beanInfos.length-1){
                sb.append(beanInfos[a].id);
            }else {
                sb.append(beanInfos[a].id).append(",");
            }
        }
        params.put("ids", sb.toString());
        params.put("isChecked", isChecked?"1":"0");
        params.put("planningScheduleId", mPlanId);
        HttpManager.getInstance().post(AppNetConfig.PLAN_SCHEDULE_CHECKED, params, new BaseResponse<EmptyBean>() {
            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean data) {
                dismissProgress();
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                if(isChecked){
                    beanInfos[0].isChecked = 0;
                }else {
                    beanInfos[0].isChecked = 1;
                }
            }
        });
    }

    /**
     * 删除商品
     *
     * @param code
     * @param planId
     */
    private void deleteProduct(String code, String planId,int position) {
        showProgress();
        String merchant_id = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("code", code);
        params.put("planningScheduleId", planId);
        HttpManager.getInstance().post(AppNetConfig.PLAN_PRODUCT_DELETE, params, new BaseResponse<EmptyBean>() {
            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean data) {
                dismissProgress();
                if (obj != null && obj.isSuccess()) {
                    Intent intent = new Intent(IntentCanst.ACTION_PLAN_EDIT_PRODUCT_NUM);
                    intent.putExtra("code",code);
                    intent.putExtra("num",-1);
                    intent.putExtra("from",2);
                    LocalBroadcastManager.getInstance(getContext()).sendBroadcast(intent);
                    int index = -1;
                    for(int a=0;a<allProductList.size();a++){
                        if(code.equals(allProductList.get(a).code)){
                            index = a;
                        }
                    }
                    if(index>=0){
                        PlanProductInfoBean bean = allProductList.get(index);
                        allProductList.remove(index);
                        mData.remove(bean);
                        mAdapter.notifyItemRemoved(position);
                    }
                    cbSelect.setChecked(false);
                    if(mData == null || mData.isEmpty()){
                        return;
                    }
                    for (PlanProductInfoBean bean : mData) {
                        if (bean.isChecked != 1) {
                            return;
                        }
                    }
                    cbSelect.setChecked(true);
                } else {
                    ToastUtils.showShort("删除失败");
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }
        });
    }


    @OnClick({R.id.fl_all_product, R.id.rb_all, R.id.rb_all_category, R.id.rb_comprehensive_ranking, R.id.tv_shop})
    public void tabClick(View view) {
        switch (view.getId()) {
            case R.id.fl_all_product:
                mActivity.showFragment(1);
                break;
            case R.id.rb_all:
                resetFilter();
                break;
            case R.id.rb_all_category:
                hideSoftInput();
                setActivated(R.id.rb_all_category);
                setIconState(mRbAllCategory, R.drawable.sort_checked);
                initCategoryPOp();
                mPopWindowProduct.show2(radioFilter, mPlanId);
                break;
            case R.id.rb_comprehensive_ranking:
                hideSoftInput();
                setActivated(R.id.rb_comprehensive_ranking);
                setIconState(mRbComprehensiveRanking, R.drawable.sort_checked);
                initPopSort();
                mPopWindowRanking.show(radioFilter);
                break;
            case R.id.tv_shop:
                hideSoftInput();
                mTvShop.setActivated(true);
                rbAll.setActivated(false);
                init1Classify();
                mClassifyPop.show();
                break;
        }
    }

    /**
     * 综合排序
     */
    private void initPopSort() {

        if (mPopWindowRanking == null) {
            mPopWindowRanking = new ListFilterPopWindow();
            mPopWindowRanking.setNewData(mPopWindowRanking.getRanking4Plan());
            mPopWindowRanking.setOnSelectListener(new BaseFilterPopWindow.OnSelectListener() {
                @Override
                public void getValue(SearchFilterBean show) {
                    mRbComprehensiveRanking.setText(show.nickname);
                    fob = show.id;
                    getAllPlanProduct();
                }

                @Override
                public void OnDismiss(String multiSelectStr) {
                    if (TextUtils.isEmpty(fob)) {
                        mRbComprehensiveRanking.setText("综合排序");
                    }
                    //mRbComprehensiveRanking.setActivated(false);
                    setIconState(mRbComprehensiveRanking, R.drawable.sort_def);
                }
            });
        }
    }

    /**
     * 初始化全部药品popWindow
     */
    private void initCategoryPOp() {
        if (mPopWindowProduct == null) {
            mPopWindowProduct = new AllProductPopWindow();
            mPopWindowProduct.setOnSelectListener(new BaseFilterPopWindow.OnSelectListener() {
                @Override
                public void getValue(SearchFilterBean show) {
                    classifyPopReset();
                    if (show != null) {
                        categoryId = show.id;
                        mRbAllCategory.setText(show.realName);
                    } else {
                        categoryId = "";
                    }
                    getAllPlanProduct();
                }

                @Override
                public void OnDismiss(String multiSelectStr) {
                    //mRbAllCategory.setActivated(false);
                    setIconState(mRbAllCategory, R.drawable.sort_def);
                }
            });
        }
    }

    /*
     * 筛选一级分类
     * */
    private void init1Classify() {
        if (mClassifyPop == null) {
            mClassifyPop = new FiltrateClassifyPop(mPlanId);
            mClassifyPop.setListener(new LeftPopWindow.Listener<SearchFilterBean>() {

                @Override
                public void onResult(SearchFilterBean bean) {
                    drugClassification = setDrugsClass(bean);
                    minPrice = !TextUtils.isEmpty(bean.priceRangeFloor) ? bean.priceRangeFloor : "";
                    maxPrice = !TextUtils.isEmpty(bean.priceRangeTop) ? bean.priceRangeTop : "";
                    setManufacturer(bean);
                    getAllPlanProduct();
                }

                @Override
                public void onDismiss() {
                    //
                }
            });
            mClassifyPop.hideYBM();
        }
    }

    /**
     * 设置厂家过来的数据
     *
     * @param bean
     */
    private void setManufacturer(SearchFilterBean bean) {
        StringBuilder sb = new StringBuilder();
        if (bean.lastNames != null && bean.lastNames.size() > 0) {
            for (int i = 0; i < bean.lastNames.size(); i++) {
                sb.append(bean.lastNames.get(i)).append("*");
            }
            if (sb.length() > 0) {
                sb.deleteCharAt(sb.length() - 1);
            }
        }
        manufacturer = sb.toString();
    }

    /**
     * 拼接药品经营类型
     *
     * @param bean
     * @return
     */
    private String setDrugsClass(SearchFilterBean bean) {
        StringBuilder sb = new StringBuilder();
        if (bean.isClassA) {
            sb.append("1").append(",");
        }
        if (bean.isClassB) {
            sb.append("2").append(",");
        }
        if (bean.isClassRx) {
            sb.append("3").append(",");
        }
        if (bean.isClassElse) {
            sb.append("4").append(",");
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    private void classifyPopReset(){
        if (mClassifyPop != null) {
            mClassifyPop.reset(true);
            drugClassification = "";
            maxPrice = "";
            minPrice = "";
            manufacturer = "";
            mTvShop.setActivated(false);
        }
    }

    /**
     * 搜索状态发生变化重置
     */
    private void resetFilter() {
        rbAll.setActivated(true);
        classifyPopReset();
        if (mPopWindowProduct != null) {
            mPopWindowProduct.resetPosition();
            categoryId = "";
            mRbAllCategory.setText("全部分类");
            mRbAllCategory.setActivated(false);
        }
        if(mPopWindowRanking !=null){
            fob = "";
            mRbComprehensiveRanking.setText("综合排序");
            mRbComprehensiveRanking.setActivated(false);
        }
        search("");
    }

    private void setIconState(TextView tv, int id) {
        Drawable nav_up = getResources().getDrawable(id);
        nav_up.setBounds(0, 0, nav_up.getMinimumWidth(), nav_up.getMinimumHeight());
        tv.setCompoundDrawables(null, null, nav_up, null);
    }

    public void setActivated(int id) {
        mRbAllCategory.setActivated(R.id.rb_all_category == id);
        mRbComprehensiveRanking.setActivated(R.id.rb_comprehensive_ranking == id);
        rbAll.setActivated(false);
    }


    public int search(String key){
        List<PlanProductInfoBean> tmp = new ArrayList<>();
        if(TextUtils.isEmpty(key)){
            tmp = allProductList;
        }else {
            for(PlanProductInfoBean bean : allProductList){
                if(bean.productName.contains(key) || bean.manufacturer.contains(key) || (!TextUtils.isEmpty(bean.zjm) && bean.zjm.contains(key.toUpperCase()))){
                    tmp.add(bean);
                }
            }
        }
        setListData(tmp);
        return tmp.size();
    }

    public void startSearch(){
        radioFilter.setVisibility(View.GONE);
        resetFilter();
    }
    public void endSearch(){
        radioFilter.setVisibility(View.VISIBLE);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if(br !=null){
            LocalBroadcastManager.getInstance(getContext()).unregisterReceiver(br);
        }
    }

    @Override
    public boolean onLicenseStatusEnable() {
        return true;
    }

    @Override
    public void handleLicenseStatusChange(int status) {
        getAllPlanProduct(searchKey);
    }
}
