package com.ybmmarket20.fragments

import android.os.Build
import android.os.Bundle
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.ybmmarket20.R
import com.ybmmarket20.view.ShopQualificationItem
import com.ybmmarket20.view.ShopQualificationItemView
import com.ybmmarket20.viewmodel.ShopAfterSaleDistributionViewModel
import kotlinx.android.synthetic.main.fragment_shop_after_sale_distribution.view.*

/**
 * 店铺-自营售后配送
 */
open class ShopAfterSaleDistributionSelfFragment: LoadingFragment() {

    var mRootView: View? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val rootView = View.inflate(context, R.layout.fragment_shop_after_sale_distribution, null)
        mRootView = rootView
        val sqivAfterSale = rootView.findViewById<ShopQualificationItemView>(R.id.sqiv_after_sale)
        sqivAfterSale.setTitle("配送售后")
        val viewModel = ViewModelProvider(this).get(ShopAfterSaleDistributionViewModel::class.java)
        viewModel.initData(arguments)
        viewModel.afterSaleDistributionLiveData.observe(viewLifecycleOwner, Observer {
            dismissProgress()
            if (it.isSuccess && it.data != null) {
                val item = ShopQualificationItem(null, it.data)
                sqivAfterSale.setContent(arrayListOf(item))
            } else {
                showEmpty()
            }
        })
        viewModel.afterSaleDistributionPopLiveData.observe(viewLifecycleOwner, Observer {
            dismissProgress()
            if (it.isSuccess && it.data != null) {
                val item = ShopQualificationItem(null, it.data, isHtml = true)
                sqivAfterSale.setContent(arrayListOf(item))
            } else {
                showEmpty()
            }
        })
        showProgress()
        getContent(viewModel)
        return rootView
    }

    open fun getContent(viewModel: ShopAfterSaleDistributionViewModel) {
        viewModel.getSelfAfterSaleDistributionContent()
    }

    fun showEmpty() {
        mRootView?.findViewById<View>(R.id.ll_empty_data)?.visibility = View.VISIBLE
    }
}