package com.ybmmarket20.fragments

import android.graphics.Rect
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.bean.NetError
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.adapter.CartBottomCouponGoodsAdapter
import com.ybmmarket20.adapter.CartBottomCouponGoodsListener
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CartCouponGoods
import com.ybmmarket20.bean.CartVoucherListBean
import com.ybmmarket20.bean.CartVoucherListResponse
import com.ybmmarket20.bean.VoucherListBean
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.home.CartAnalysis
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.report.coupon.CouponUtil.Companion.wrapperRouterUrlWithParam
import com.ybmmarket20.report.coupon.ICouponEntryType
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.setTitleAndTag
import com.ybmmarket20.view.ShowBottomCartFragmentAnalysis
import com.ybmmarket20.view.WrapContentLinearLayoutManager
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter

/**
 * @class   ShowBottomCartCouponFragment
 * <AUTHOR>
 * @date  2024/11/18
 * @description
 */
class ShowBottomCartCouponFragment: ShowBottomCartFragmentAnalysis() {
    private var mList: RecyclerView? = null
    private var srlList: SmartRefreshLayout? = null
    private lateinit var mAdapter: CarCouponBottomAdapter
    private val mCartAnalysis = CartAnalysis()
    private var mDatas: ArrayList<VoucherListBean> = ArrayList()
    private var skuIds: String = ""
    private var shopCode: String = ""
    private var shopName: String = ""
    private var type: String= "" //查询类型 1店铺券 2平台券 默认不传是店铺券
    private var tabCoupon:Int = 0
    private var preCartVoucherListResponse: CartVoucherListResponse? = null //外部带进来的参数

    var mOnDismissListener: (()->Unit)? = null

    companion object{
        const val CART_BOTTOM_COUPON_HEAD_TYPE_UNRECEIVE = 0 //item类型-未领取头部

        const val CART_BOTTOM_COUPON_HEAD_TYPE_RECEIVED = 1 //item类型-已领取头部

        const val CART_BOTTOM_COUPON_ITEM_TYPE_UNRECEIVE = 2 //item类型-未领取

        const val CART_BOTTOM_COUPON_ITEM_TYPE_RECEIVED = 3 //item类型-已领取

        const val TAB_FIND_COUPON_FIRST = 1 //tab样式 列表数据第一个
        const val TAB_FIND_COUPON_SECOND = 2 //tab样式 列表数据第二个
    }



    override fun initData(content: String?) {
        arguments?.apply {
            skuIds = getString("skuIds","")
            shopCode = getString("shopCode","")
            shopName = getString("shopName","")
            type = getString("type","")
            tabCoupon = getInt("tabCoupon",0)
            (getSerializable("couponEntryType") as? ICouponEntryType?).let {
                mCouponEntryType = it
            }
            (getSerializable("preCartVoucherListResponse") as? CartVoucherListResponse?).let {
                preCartVoucherListResponse = it
            }


        }

        preCartVoucherListResponse?.let {
            val cartVoucherListBean = CartVoucherListBean().apply {
                availableVoucherList = it.availableVoucherList
                unclaimedVoucherList = it.unclaimedVoucherList
            }
            handleCouponData(cartVoucherListBean, mDatas)
        }

        mList = view?.findViewById(R.id.crv_list)
        srlList = view?.findViewById(R.id.srl_list)
        mList?.layoutManager = WrapLinearLayoutManager(requireActivity(), LinearLayoutManager.VERTICAL, false)
        mList?.addItemDecoration(CouponItemDecoration())
        mAdapter = CarCouponBottomAdapter(mDatas)
        mAdapter.setEmptyView(requireActivity(), R.layout.layout_empty_view, R.drawable.icon_empty_coupon, "暂无优惠券")
        mList?.adapter = mAdapter
        srlList?.setOnRefreshListener {
            when(tabCoupon){
                TAB_FIND_COUPON_FIRST,TAB_FIND_COUPON_SECOND->{
                    getTabShopCouponData()
                }
                else ->{
                    getData()
                }
            }
        }

        if (mDatas.isEmpty()){
            if (tabCoupon == TAB_FIND_COUPON_FIRST || tabCoupon == TAB_FIND_COUPON_SECOND){
                getTabShopCouponData()
            }else{
                getData()
            }
        }

    }

    override fun getLayoutId(): Int = R.layout.fragment_show_bottom_cart_coupon

    /**
     * @param list
     */
    fun setNewData(list: ArrayList<VoucherListBean>) {
        this.mDatas = list
        mAdapter.setNewData(list)
    }


    inner class CarCouponBottomAdapter(data: List<VoucherListBean>?) : YBMBaseMultiItemAdapter<VoucherListBean>(data) {
        init {
            addItemType(CART_BOTTOM_COUPON_HEAD_TYPE_RECEIVED, R.layout.item_cart_bottom_coupon_head)
            addItemType(CART_BOTTOM_COUPON_HEAD_TYPE_UNRECEIVE, R.layout.item_cart_bottom_coupon_head)
            addItemType(CART_BOTTOM_COUPON_ITEM_TYPE_RECEIVED, R.layout.item_show_bottom_car_coupon_dialog_list)
            addItemType(CART_BOTTOM_COUPON_ITEM_TYPE_UNRECEIVE, R.layout.item_show_bottom_car_coupon_dialog_list)
        }

        private fun bindUnreceiveHead(baseHolder: YBMBaseHolder, voucherListBean: VoucherListBean) {
            baseHolder.setText(R.id.tv_cart_bottom_coupon_head_title, mContext.resources.getString(R.string.wait_receive_coupon))
            baseHolder.setText(R.id.tv_cart_bottom_coupon_head_des, mContext.resources.getString(R.string.wait_receive_coupon_des))
        }

        private fun bindReceivedHead(baseHolder: YBMBaseHolder, voucherListBean: VoucherListBean) {
            baseHolder.setText(R.id.tv_cart_bottom_coupon_head_title, mContext.resources.getString(R.string.received_coupon))
            baseHolder.setText(R.id.tv_cart_bottom_coupon_head_des, mContext.resources.getString(R.string.received_coupon_des))
        }

        private fun bindUnreceiveItem(baseHolder: YBMBaseHolder, voucherListBean: VoucherListBean) {
            bindBaseItem(baseHolder, voucherListBean)
            if (voucherListBean.state == VoucherListBean.COUPON_STATUS_RECEIVED) {
                baseHolder.setText(R.id.tv_coupon_immediate_use, mContext.resources.getString(R.string.received))
            }
            baseHolder.setGone(R.id.rv_cart_bottom_coupon_goods, false)
        }

        private fun bindReceivedItem(baseHolder: YBMBaseHolder, voucherListBean: VoucherListBean) {
            bindBaseItem(baseHolder, voucherListBean)
            baseHolder.setText(R.id.tv_coupon_immediate_use, mContext.resources.getString(R.string.go_order))
            val rv = baseHolder.getView<RecyclerView>(R.id.rv_cart_bottom_coupon_goods)
            rv.layoutManager = WrapContentLinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false)
            rv.visibility = View.VISIBLE
            var adapter = rv.adapter as CartBottomCouponGoodsAdapter?
            if (adapter == null) {
                rv.addItemDecoration(CouponGoodsItemDecoration())
                adapter = CartBottomCouponGoodsAdapter(R.layout.item_cart_bottom_coupon_goods, voucherListBean.cartProductList,object : CartBottomCouponGoodsListener {
                    override fun callback(t: CartCouponGoods?) {
                        t?:return
                        val selectActionType = if (t.status == 1) 0 else 1
                        selectGoods(t.skuId, selectActionType, t.packageId)
                    }
                })
                rv.adapter = adapter
            } else {
                adapter.setNewData(voucherListBean.cartProductList)
            }
        }

        private fun bindBaseItem(ybmBaseHolder: YBMBaseHolder, voucherListBean: VoucherListBean) {
            val tvPriceUnit = ybmBaseHolder.getView<TextView>(R.id.tv_PriceUnit)
            val tvDiscountUnit = ybmBaseHolder.getView<TextView>(R.id.tv_discount_unit)
            val tvCouponAmount = ybmBaseHolder.getView<TextView>(R.id.tv_coupon_amount)
            val tvCouponTitle = ybmBaseHolder.getView<TextView>(R.id.tv_coupon_title)
            val tvCouponSubTitle = ybmBaseHolder.getView<TextView>(R.id.tv_coupon_subtitle)
            val tvCouponLimt = ybmBaseHolder.getView<TextView>(R.id.tv_coupon_limit)
            val tvAmountTips = ybmBaseHolder.getView<TextView>(R.id.tv_amount_tips)
            val tvCouponFullReduceMax = ybmBaseHolder.getView<TextView>(R.id.tv_coupon_full_reduce_max)
            setTitleAndTag(mContext, voucherListBean.getVoucherType(), voucherListBean.getShopName(), voucherListBean.getVoucherTypeDesc(), tvCouponTitle)
            tvCouponSubTitle.text = voucherListBean.voucherTitle
            tvCouponLimt.text = voucherListBean.voucherScope
            if (voucherListBean.voucherState == 1) {
                // 折扣
                tvPriceUnit.visibility = View.GONE
                tvDiscountUnit.visibility = View.VISIBLE
                tvCouponAmount.text = voucherListBean.discount
            } else {
                tvPriceUnit.visibility = View.VISIBLE
                tvDiscountUnit.visibility = View.GONE
                tvCouponAmount.text = UiUtils.transformInt(voucherListBean.moneyInVoucher)
            }

            //根据是否是叠加券显示按钮
            val isVoucherDemo = !TextUtils.isEmpty(voucherListBean.voucherDemo)
            // 最高减信息 显示文案
            if (!TextUtils.isEmpty(voucherListBean.maxMoneyInVoucherDesc)) {
                tvCouponFullReduceMax.visibility = View.VISIBLE
                tvCouponFullReduceMax.text = voucherListBean.maxMoneyInVoucherDesc
            } else {
                tvCouponFullReduceMax.visibility = View.GONE
            }

            //有效时间-叠加券文案
            if (isVoucherDemo) {
                ybmBaseHolder.setText(R.id.tv_coupon_date, voucherListBean.voucherDemo)
            } else {
//                ybmBaseHolder.setText(R.id.tv_coupon_date, DateTimeUtil.getCouponDateTime(voucherListBean.validDate)
//                        + "-" + DateTimeUtil.getCouponDateTime(voucherListBean.expireDate));
                ybmBaseHolder.setText(R.id.tv_coupon_date, voucherListBean.validDateToString + "-" + voucherListBean.expireDateToString)
            }
            //优惠券 适用金额限制条件
            ybmBaseHolder.setText(R.id.tv_coupon_full_reduce, voucherListBean.minMoneyToEnableDesc)

            //小计 提示说明
//            String desc = "";
//            if (voucherListBean.noEnoughMoney <= 0) {
//                desc = mContext.getResources().getString(R.string.cart_coupon_item_desc);
//                desc = String.format(desc, voucherListBean.isSelectSkuNum + "", StringUtil.DecimalFormat2Double(voucherListBean.selectSkuAmount));
//            } else {
//                desc = mContext.getResources().getString(R.string.cart_coupon_item_desc_2);
//                desc = String.format(desc, voucherListBean.isSelectSkuNum + "", StringUtil.DecimalFormat2Double(voucherListBean.selectSkuAmount), StringUtil.DecimalFormat2Double(voucherListBean.noEnoughMoney));
//            }
//            tvAmountTips.setText(Html.fromHtml(desc));
            if (voucherListBean.itemType == CART_BOTTOM_COUPON_ITEM_TYPE_RECEIVED) {
                tvAmountTips.visibility = View.VISIBLE
            } else {
                tvAmountTips.visibility = View.GONE
            }
            tvAmountTips.text = voucherListBean.title
            val rtv = ybmBaseHolder.getView<RoundTextView>(R.id.tv_coupon_immediate_use)
            val tvGetItNow = ybmBaseHolder.getView<TextView>(R.id.tv_get_it_now)
            rtv.setOnClickListener { v: View? ->
                var routerUrl = ""
                if (voucherListBean.itemType == CART_BOTTOM_COUPON_ITEM_TYPE_RECEIVED) {
                    //去凑单
                    if (shopCode == null) shopCode = ""
                    if (shopName == null) shopName = ""
                    if (TextUtils.isEmpty(voucherListBean.appUrl)) {
                        routerUrl = wrapperRouterUrlWithParam("ybmpage://couponavailableactivity/" + voucherListBean.voucherTemplateId + "/" + 1, mCouponEntryType?.getCouponEntryType()?:"", shopCode, shopName)
                        RoutersUtils.open(routerUrl)
                        mCartAnalysis.cartCrossShopCouponActionClick("ybmpage://couponavailableactivity/" + voucherListBean.voucherTemplateId + "/" + 1)
                    } else {
                        routerUrl = wrapperRouterUrlWithParam(voucherListBean.appUrl, mCouponEntryType?.getCouponEntryType()?:"", shopCode, shopName)
                        RoutersUtils.open(routerUrl)
                        mCartAnalysis.cartCrossShopCouponActionClick(voucherListBean.appUrl)
                    }

                    mOnDismissListener?.invoke()
                }
            }
            tvGetItNow.setOnClickListener { v: View ->
                //立即领取
                receiveCoupon(voucherListBean)
                var couponPrice = ""
                try {
                    couponPrice = if (voucherListBean.discount != null && !voucherListBean.discount.isEmpty() && voucherListBean.discount.toDouble() > 0) {
                        voucherListBean.discount + "折"
                    } else {
                        "¥" + voucherListBean.moneyInVoucher
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            val isShowUnreceive = voucherListBean.state != VoucherListBean.COUPON_STATUS_RECEIVED && voucherListBean.itemType == CART_BOTTOM_COUPON_ITEM_TYPE_UNRECEIVE
            ybmBaseHolder.setGone(R.id.tv_get_it_now, isShowUnreceive)
            ybmBaseHolder.setGone(R.id.tv_coupon_immediate_use, !isShowUnreceive)
        }

        override fun bindItemView(baseViewHolder: YBMBaseHolder?, voucherListBean: VoucherListBean) {
            when (baseViewHolder?.itemViewType) {
                CART_BOTTOM_COUPON_HEAD_TYPE_RECEIVED -> bindReceivedHead(baseViewHolder, voucherListBean)
                CART_BOTTOM_COUPON_HEAD_TYPE_UNRECEIVE -> bindUnreceiveHead(baseViewHolder, voucherListBean)
                CART_BOTTOM_COUPON_ITEM_TYPE_RECEIVED -> bindReceivedItem(baseViewHolder, voucherListBean)
                CART_BOTTOM_COUPON_ITEM_TYPE_UNRECEIVE -> bindUnreceiveItem(baseViewHolder, voucherListBean)
            }
        }
    }


    private fun completion() {
        srlList?.finishRefresh()
    }

    /**
     * 选择商品
     */
    fun selectGoods(skuId: String?, status: Int, packageId: String?) {
        val params = RequestParams()
        params.put("merchantId", SpUtil.getMerchantid())
        params.put("skuId", skuId)
        params.put("status", status.toString() + "")
        params.put("shopCode", shopCode)
        params.put("packageId", packageId)
        params.put("terminalType", "1")
        if (!TextUtils.isEmpty(type)) {
            params.put("type", type)
        }
        HttpManager.getInstance().post(AppNetConfig.CART_SELECT_GOODS_FOR_COUPON, params, object : BaseResponse<CartVoucherListBean?>() {
            override fun onSuccess(content: String, obj: BaseBean<CartVoucherListBean?>?, data: CartVoucherListBean?) {
                super.onSuccess(content, obj, data)
                completion()
                if (obj != null && obj.isSuccess && data != null) {
                    mDatas.clear()
                    handleCouponData(data, mDatas)
                    setNewData(mDatas)
                }
            }

            override fun onFailure(error: NetError) {
                super.onFailure(error)
            }
        })
    }

    /**
     * 领取优惠券
     */
    fun receiveCoupon(voucherListBean: VoucherListBean?) {
        if (voucherListBean == null) return
        val params = RequestParams()
        var couponId = ""
        couponId = voucherListBean.voucherTemplateId.toString() + ""
        params.put("voucherTemplateId", couponId)
        params.put("merchantId", SpUtil.getMerchantid())
        HttpManager.getInstance().post(AppNetConfig.RECEIVE_USABLE_VOUCHER, params, object : BaseResponse<BaseBean<*>?>() {
            override fun onSuccess(content: String, obj: BaseBean<BaseBean<*>?>?, baseBean: BaseBean<*>?) {
                super.onSuccess(content, obj, baseBean)
                if (obj != null && obj.isSuccess) {
                    voucherListBean.setState(VoucherListBean.COUPON_STATUS_RECEIVED)
                    mAdapter.notifyDataSetChanged()
                }
            }

            override fun onFailure(error: NetError) {
                super.onFailure(error)
            }
        })
    }


    internal class CouponGoodsItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
            super.getItemOffsets(outRect, view, parent, state)
            outRect[0, 0, ConvertUtils.dp2px(10f)] = 0
        }
    }


    internal class CouponItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
            super.getItemOffsets(outRect, view, parent, state)
            val lm = parent.layoutManager
            val childPosition = parent.getChildAdapterPosition(view)
            val childCount = parent.adapter!!.itemCount
            if (childPosition == childCount - 1) {
                outRect[0, 0, 0] = ConvertUtils.dp2px(13f)
            }
        }
    }

    /**
     * 参数新增type 1店铺券 2平台券  默认不传查询的是店铺券
     * 1:查询店铺券(必传shopCode) 2:查询大平台券(不用传shopCode)
     * shopCode	 店铺编码
     *
     * @return RequestParams
     */
    override fun getParams(): RequestParams {
        val merchantid = SpUtil.getMerchantid()
        val params = RequestParams()
        params.url = AppNetConfig.CART_BOTTOM_COUPON
        params.put("merchantId", merchantid)
        params.put("shopCode", shopCode)
        //商品ids
        if (!TextUtils.isEmpty(skuIds)) {
            params.put("skuIds", skuIds)
        }
        //type
        if (!TextUtils.isEmpty(type)) {
            params.put("type", type)
        }
        return params
    }

    fun getData() {

        HttpManager.getInstance().post(params, object : BaseResponse<CartVoucherListBean?>() {
            override fun onSuccess(content: String, obj: BaseBean<CartVoucherListBean?>, data: CartVoucherListBean?) {
                completion()
                if (obj != null && obj.isSuccess && data != null) {
                    mDatas.clear()
                    handleCouponData(data, mDatas)
                    mAdapter.setNewData(mDatas)
                }
            }

            override fun onFailure(error: NetError) {
                completion()
                ToastUtils.showShort(error.message)
                mAdapter.loadMoreFail()
            }
        })
    }

    fun getTabShopCouponData() {
        val merchantid = SpUtil.getMerchantid()
        val mParams = RequestParams()
        mParams.url = AppNetConfig.CART_BOTTOM_SHOP_COUPON
        mParams.put("merchantId", merchantid)
        mParams.put("shopCode", shopCode)

        HttpManager.getInstance().post(mParams, object : BaseResponse<List<CartVoucherListResponse?>?>() {
            override fun onSuccess(content: String?, obj: BaseBean<List<CartVoucherListResponse?>?>?, data: List<CartVoucherListResponse?>?) {
                completion()
                if (obj != null && obj.isSuccess && data != null) {
                    mDatas.clear()

                    handleCouponData(handleCartVoucherListResponseTransform(data), mDatas)
                    mAdapter.setNewData(mDatas)
                }
            }

            override fun onFailure(error: NetError) {
                completion()
                ToastUtils.showShort(error.message)
                mAdapter.loadMoreFail()
            }
        })
    }

    private fun handleCartVoucherListResponseTransform(data:List<CartVoucherListResponse?>):CartVoucherListBean{
        val cartVoucherListBean = CartVoucherListBean()
        if (tabCoupon == TAB_FIND_COUPON_FIRST && data.isNotEmpty()){
            cartVoucherListBean.availableVoucherList = data[0]?.availableVoucherList
            cartVoucherListBean.unclaimedVoucherList = data[0]?.unclaimedVoucherList
        } else if (tabCoupon == TAB_FIND_COUPON_SECOND && data.size>1){
            cartVoucherListBean.availableVoucherList = data[1]?.availableVoucherList
            cartVoucherListBean.unclaimedVoucherList = data[1]?.unclaimedVoucherList
        }
        return cartVoucherListBean
    }

    /**
     * 处理返回的优惠券数据
     *
     * @param data
     */
    private fun handleCouponData(data: CartVoucherListBean?, dataList: MutableList<VoucherListBean>?): List<VoucherListBean>? {
        if (data != null && dataList != null) {
            if (data.availableVoucherList != null && !data.availableVoucherList.isEmpty()) {
                val receivedHeadBean = VoucherListBean()
                receivedHeadBean.itemType = CART_BOTTOM_COUPON_HEAD_TYPE_RECEIVED
                dataList.add(receivedHeadBean)
                for (i in data.availableVoucherList.indices) {
                    val item = data.availableVoucherList[i]
                    item.itemType = CART_BOTTOM_COUPON_ITEM_TYPE_RECEIVED
                    dataList.add(item)
                }
            }
            if (data.unclaimedVoucherList != null && !data.unclaimedVoucherList.isEmpty()) {
                val unReceiveHeadBean = VoucherListBean()
                unReceiveHeadBean.itemType = CART_BOTTOM_COUPON_HEAD_TYPE_UNRECEIVE
                dataList.add(unReceiveHeadBean)
                for (i in data.unclaimedVoucherList.indices) {
                    val item = data.unclaimedVoucherList[i]
                    item.itemType = CART_BOTTOM_COUPON_ITEM_TYPE_UNRECEIVE
                    dataList.add(item)
                }
            }
            mapCouponIdAndPosition(data.availableVoucherList, data.unclaimedVoucherList)
        }
        return dataList
    }

}