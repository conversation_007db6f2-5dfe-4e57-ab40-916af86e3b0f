package com.ybmmarket20.fragments;

import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.luck.picture.lib.tools.DoubleUtils;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.AptitudeBean;
import com.ybmmarket20.bean.AptitudeEvent;
import com.ybmmarket20.bean.AptitudeListBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.common.BaseFragment;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.eventbus.Event;
import com.ybmmarket20.common.eventbus.EventBusUtil;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.DefaultItemDecoration;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import butterknife.Bind;
import butterknife.OnClick;

import static com.ybmmarket20.constant.IntentCanst.RX_BUS_LICENSE_STATUS;
import static com.ybmmarket20.constant.IntentCanst.RX_BUS_LICENSE_XYY_DOWN_STATUS;
import static com.ybmmarket20.constant.IntentCanst.RX_BUS_NET_ERR;
import static com.ybmmarket20.constant.IntentCanst.RX_BUS_REFRESH_LICENCE_AUDIT_LIST;
import static com.ybmmarket20.constant.IntentCanst.RX_BUS_UPDATE_LICENCEDTAIL;

/**
 * 变更记录
 */
public class AptitudeAuditFragment extends BaseFragment implements BaseQuickAdapter.OnItemClickListener {
    @Bind(R.id.rv_aptitude_audit)
    CommonRecyclerView mAuditList;
    @Bind(R.id.layout_load_error)
    ViewGroup layout_load_error;

    private YBMBaseAdapter mAuditAdapter;
    private List<AptitudeBean> auditList = new ArrayList<>();
    private SimpleDateFormat dateFormat;
    private TextView tvError;
    private View emptyView;
    private ImageView iv;

    private int mPageSize = 10;
    private int mPage = 1;
    private boolean isLoadMore;
    private String customerType;

    @Override
    public int getLayoutId() {
        return R.layout.fragment_layout_aptitude_audit;
    }

    @Override
    protected void initData(String content) {
        initRecyclerView();
    }

    private void initRecyclerView() {
        dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        //审核列表
        mAuditAdapter = new YBMBaseAdapter<AptitudeBean>(R.layout.item_new_aptitude_audit, auditList) {
            @Override
            protected void bindItemView(YBMBaseHolder ybmBaseHolder, AptitudeBean bean) {
                String validateTime = dateFormat.format(new Date(bean.getCreateTime()));
                ybmBaseHolder.setText(R.id.tv_audit_id, bean.applicationNumber)
                        .setText(R.id.tv_audit_status, bean.auditStatusName)
                        .setText(R.id.tv_up_time, validateTime)
                        .setText(R.id.tv_org_name, bean.orgName)
                        .setText(R.id.tv_audit_reason, bean.getRemark());
                ybmBaseHolder.setTextColor(R.id.tv_audit_status, UiUtils.getColorFromAptitudeStatus(bean.auditStatus));
                ybmBaseHolder.getView(R.id.tv_audit_more).setOnClickListener(v -> {//点击查看详情
                    if (!DoubleUtils.isFastDoubleClick()) {
                        RoutersUtils.open("ybmpage://aptitudedetail/" + SpUtil.getMerchantid() + "/" + bean.applicationNumber + "/" + bean.getType());
                    }
                });
                RecyclerView.LayoutParams params = (RecyclerView.LayoutParams) ybmBaseHolder.getView(R.id.rl_audit).getLayoutParams();
                if (ybmBaseHolder.getAdapterPosition() == 0) {

                    params.topMargin = UiUtils.dp2px(10);

                } else if (ybmBaseHolder.getAdapterPosition() == auditList.size() - 1) {

                    params.bottomMargin = UiUtils.dp2px(17);

                } else {
                    params.bottomMargin = 0;
                }

            }

        };
        View emptyView = getNotNullActivity().getLayoutInflater().inflate(R.layout.layout_empty_view, (ViewGroup) mAuditList.getParent(), false);
        tvError = emptyView.findViewById(R.id.tv);
        tvError.setText("暂无变更记录");
        iv = emptyView.findViewById(R.id.iv);
        iv.setOnClickListener(this::clickTab);
        mAuditAdapter.setEmptyView(emptyView);
        // mAuditAdapter.setOnRecyclerViewItemClickListener(this);
        mAuditAdapter.setEnableLoadMore(false);//不支持分页了

        mAuditList.setEnabled(true);
        mAuditList.setRefreshEnable(true);
        // todo 这里取消了加载更多，但是有下拉刷新
        mAuditList.setListener(new CommonRecyclerView.Listener() {

            @Override
            public void onRefresh() {
                isLoadMore = false;
                getAptitudeInfo(1);
            }

            @Override
            public void onLoadMore() {
                isLoadMore = true;
                getAptitudeInfo(mPage);
            }
        });
        mAuditList.setNestedScrollingEnabled(false);
        DefaultItemDecoration itemDecoration = new DefaultItemDecoration(getContext(), UiUtils.dp2px(10));
        itemDecoration.setLineColorResId(R.color.color_aptitude_bg);
        mAuditList.addItemDecoration(itemDecoration);
        mAuditList.setAdapter(mAuditAdapter);
        mAuditList.setLayoutManager(new WrapLinearLayoutManager(getContext()));

    }

    public void getAptitudeInfo(int page) {
        showProgress();
        RequestParams params = new RequestParams();
        params.put("merchantId", SpUtil.getMerchantid());
        HttpManager.getInstance().post(AppNetConfig.QUERY_LICENSE_AUDIT_LIST, params, new BaseResponse<AptitudeListBean>() {

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                if (mAuditList != null) {
                    mAuditList.setVisibility(View.GONE);
                }
                if (layout_load_error != null) {
                    layout_load_error.setVisibility(View.VISIBLE);
                }
                Event<Boolean> event = new Event<>(RX_BUS_NET_ERR, true);
                EventBusUtil.sendEvent(event);
            }

            @Override
            public void onSuccess(String content, BaseBean<AptitudeListBean> baseBean, AptitudeListBean data) {
                dismissProgress();
                if (mAuditList != null) {
                    mAuditList.setRefreshing(false);
                }
                if (baseBean != null && baseBean.isSuccess()) {
                    if (data != null && data.getLicenseAuditList() != null && data.getLicenseAuditList().size() > 0) {
                        auditList.clear();
                        auditList.addAll(data.getLicenseAuditList());
                        mAuditAdapter.setNewData(auditList);
                    }else {
                        auditList.clear();
                        mAuditAdapter.notifyDataSetChanged();
                    }
                    //订阅成功发送event 事件
                    AptitudeEvent aptitudeEvent = new AptitudeEvent();
                    //这里需要把首营资质选择的客户类型customerType回显回去
                    customerType = data.getCustomerType();
                    aptitudeEvent.setFirstLicenseType(data.getCustomerType());
                    aptitudeEvent.setLicenseStatus(data.getLicenseStatus());
                    Event<AptitudeEvent> event = new Event<>(RX_BUS_LICENSE_STATUS, aptitudeEvent);

                    EventBusUtil.sendEvent(event);

                    Event<Integer> aptitudeUrlEvent = new Event<>(RX_BUS_LICENSE_XYY_DOWN_STATUS, data.getLicenseDownStatus());
                    EventBusUtil.sendEvent(aptitudeUrlEvent);
                }

                mAuditAdapter.setEnableLoadMore(false);

                if (mAuditList != null) {
                    mAuditList.setVisibility(View.VISIBLE);
                }
                if (layout_load_error != null) {
                    layout_load_error.setVisibility(View.GONE);
                }
                Event<Boolean> event = new Event<>(RX_BUS_NET_ERR, false);
                EventBusUtil.sendEvent(event);
            }
        });
    }


    @Override
    protected boolean isRegisterEventBus() {
        return true;
    }

    @Override
    protected void receiveEvent(Event event) {
        // Log.e("YBM", "event=============" + event.toString());
        if (event.getCode() == RX_BUS_UPDATE_LICENCEDTAIL || event.getCode() == RX_BUS_REFRESH_LICENCE_AUDIT_LIST) {
            boolean isSubscribe = (boolean) event.getData();
            if (isSubscribe) {//刷新数据
                mAuditList.setRefreshEnable(true);
                getAptitudeInfo(1);
            }
        }
    }

    @OnClick({R.id.tv_reload})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.iv://重新加载
                getAptitudeInfo(1);
                break;
            case R.id.tv_reload://重新加载
                getAptitudeInfo(1);
                break;
        }
    }

    @Override
    protected void initTitle() {

    }

    @Override
    protected RequestParams getParams() {
        return null;
    }

    @Override
    protected String getUrl() {
        return null;
    }

    @Override
    public void onItemClick(BaseQuickAdapter baseQuickAdapter, View view, int i) {

    }
}
