package com.ybmmarket20.fragments

import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.activity.AptitudeActivity
import com.ybmmarket20.bean.*
import com.ybmmarket20.common.*
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.home.IHomeSteadyFragment
import com.ybmmarket20.message.Message
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.AuditStatusHomeFloatManager
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.view.DialImageView
import com.ybmmarket20.view.ShowBottomSheetDialog

/**
 * <AUTHOR>
 * @date 2020-05-12
 * @description  处理首页弹框 和 消息
 */

const val HOME_ALERT_SCENE_LAUNCH = 1    //启动
const val HOME_ALERT_SCENE_REFRESH = 2   //下拉刷新
const val HOME_ALERT_SCENE_RESUME = 3    //显示

abstract class HomeSteadyAlertFragment: RefreshWrapperFragment<RowsBean>(), Message.Listener {

    private var auditStatusHomeFloatManager: AuditStatusHomeFloatManager? = null
    private var tvBubble: TextView? = null

    /**
     * 获取天降红包
     */
    fun getHeavenlyRedEnvelope(adSuspension: ImageView?) {
        if (SpUtil.getMerchantid().isNullOrEmpty()) return
        val params = RequestParams.newBuilder()
                .url(AppNetConfig.HOME_COUPON)
                .addParam("merchantId", SpUtil.getMerchantid())
                .build()
        HttpManager.getInstance().post(params, object : BaseResponse<AdBagListBean>() {
            override fun onSuccess(content: String, obj: BaseBean<AdBagListBean>?, adDataBeans: AdBagListBean?) {
                if (obj != null && obj.isSuccess) {
                    adDataBeans?.also {
                        if (it.bagList != null && it.bagList.size > 0) {
                            if (it.grantType == 2) {
                                AdDialog2.showDialog(adDataBeans)
                            } else {
                                adSuspension?.visibility = View.VISIBLE
                                SpUtil.writeInt("show_ad_collect_pop", 0)
                                AdDialog2.showDialog(adDataBeans)
                            }
                        } else {
                            SpUtil.writeInt("show_ad_collect_pop", 1)
                            LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(Intent(IntentCanst.ACTION_AD_COLLECT_HINT_POP))
                        }
                    }
                }
            }
        })
    }

    /**
     * 大转盘
     */
    fun getTurnTable(iv_dial_suspension: DialImageView){
        if (SpUtil.getMerchantid().isNullOrEmpty()) return
        val params = RequestParams.newBuilder()
                .url(AppNetConfig.HOME_DIAL_INFO)
                .addParam("merchantId", HttpManager.getInstance().merchant_id)
                .build()
        HttpManager.getInstance().post(params, object : BaseResponse<DialInfoBean>() {
            override fun onSuccess(content: String, obj: BaseBean<DialInfoBean>?, bean: DialInfoBean?) {
                if (obj != null && obj.isSuccess) {
                    bean?.also {
                        if (!TextUtils.isEmpty(bean.appImageUrl)) {
                            iv_dial_suspension.setItemData(it)
                            iv_dial_suspension.visibility = View.VISIBLE
                        } else {
                            iv_dial_suspension.visibility = View.GONE
                        }
                    }
                } else {
                    iv_dial_suspension.visibility = View.GONE
                }
            }

            override fun onFailure(error: NetError) {
                super.onFailure(error)
                iv_dial_suspension.visibility = View.GONE
            }
        })
    }

    /**
     * 获取首页弹窗
     * @param scene 场景 1 启动 2 下拉刷新 3显示（后台到前台和tab切换,不包含启动）
     */
    fun getHomeAlert(scene: Int) {
        if (SpUtil.getMerchantid().isNullOrEmpty()) return
        val params = RequestParams.newBuilder().url(AppNetConfig.HOME_DIALOG)
                .addParam("scene", "$scene")
                .addParam("merchantId", HttpManager.getInstance().merchant_id)
                .build()
        HttpManager.getInstance().post(params, object : BaseResponse<EmptyBean>(){})
    }

    /**
     * 获取资质临期提醒
     */
    fun getMerchantAptitudApproachingDate() {
        if (SpUtil.getMerchantid().isNullOrEmpty()) return
        val params = RequestParams.newBuilder()
                .url(AppNetConfig.HOME_GETMERCHANTCREDENTIALSDEADLINEINFO)
                .addParam("merchantId", HttpManager.getInstance().merchant_id)
                .build()
        HttpManager.getInstance().post(params, object : BaseResponse<CredentialsDeadlineInfoBean>() {
            override fun onSuccess(content: String, obj: BaseBean<CredentialsDeadlineInfoBean>?, bean: CredentialsDeadlineInfoBean?) {
                if (obj != null && obj.isSuccess) {
                    bean?.also {
                        showSaveDialog(it.title, it.credentialsDeadlineRemindMes, AlertDialogEx.OnClickListener { _, _ -> callPhone(bean.sysUserPhone) })
                    }
                }
            }
        })
    }

    /**
     * 处理一审悬浮弹窗是否显示
     * @param isShow 是否显示
     */
    private fun handleAuditPassedFloatView(isShow: Boolean) {
        if (auditStatusHomeFloatManager == null) {
            auditStatusHomeFloatManager = AuditStatusHomeFloatManager()
        }
        val vg = loadingPage.findViewById<ViewGroup>(R.id.fl_home_cms)
        if (isShow) {
            context?.let {
                auditStatusHomeFloatManager?.addParentLayout(it, vg)
                        ?.setOnAuditStatusFloatVieListener(object: AuditStatusHomeFloatManager.AuditStatusFloatViewListener{
                            override fun callback() {
                                (notNullActivity as BaseActivity).gotoAtivity(AptitudeActivity::class.java, null)
                            }
                        })
            }
        } else {
            auditStatusHomeFloatManager?.hiddenFloatView()
        }
    }

    /**
     * 处理首页消息数量
     */
    fun initMessageCountForBubble(tvBubbule: TextView?) {
        tvBubbule?.also {
            this.tvBubble = it
            Message.instance.bindUnreadMsgCount(this)
        }
    }

    /**
     * 资质过期提醒对话框
     */
    fun showSaveDialog(title: String, content: String, listener: AlertDialogEx.OnClickListener) {
        val titleStr = if (TextUtils.isEmpty(title)) "资质过期提醒" else title
        val contentStr = if (TextUtils.isEmpty(content)) "您的资质即将过期，为避免影响您的正常采购，请联系工作人员进行新的资质录入" else content
        AlertDialogEx(notNullActivity)
                .setTitle(titleStr)
                .setMessage(contentStr)
                .setCancelButton("取消") { dialog, _ ->
                    dialog.dismiss()
                }.setCancelable(false)
                .setConfirmButton("联系工作人员", listener)
                .setCanceledOnTouchOutside(false)
                .show()
    }

    /**
     * 拨打电话
     */
    private fun callPhone(phone: String) {
        showBottomSheetDialog(phone)
    }

    /**
     * 拨打电话
     */
    private fun showBottomSheetDialog(mobile: String) {
        if (TextUtils.isEmpty(mobile)) {
            ToastUtils.showShort("联系电话为空")
            return
        }
        ShowBottomSheetDialog(notNullActivity).also {
            it.initData(mobile, -1)
            it.show()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        Message.instance.releaseListener(this)
    }

    override fun countChange(count: Int) {
        if (count <= 0) {
            tvBubble?.visibility = View.GONE
        } else if (count in 1..9) {
            tvBubble?.visibility = View.VISIBLE
            tvBubble?.text = count.toString()
        } else {
            tvBubble?.visibility = View.VISIBLE
            tvBubble?.text = "9+"
        }
    }
}