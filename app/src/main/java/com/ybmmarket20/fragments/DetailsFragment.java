package com.ybmmarket20.fragments;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.ybmmarket20.R;
import com.ybmmarket20.bean.ProductDetailBean;
import com.ybmmarket20.bean.ProductDetailImageBean;
import com.ybmmarket20.common.BaseFragment;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.view.ImageLayout;

import java.util.List;

import butterknife.Bind;

/**
 * 商品详情-说明书页面
 */
@Deprecated
public class DetailsFragment extends BaseFragment {

    @Bind(R.id.il_specification)
    ImageLayout ilSpecification;
    @Bind(R.id.il_about)
    ImageView ilAbout;
    @Bind(R.id.ll_specification)
    LinearLayout llSpecification;

    @Override
    protected void initData(String content) {

    }

    public void updataUI(ProductDetailBean productDetail) {

        if (null != productDetail) {
            setDetailData(productDetail);
        }
    }

    @Override
    protected void initTitle() {

    }

    @Override
    protected RequestParams getParams() {
        return null;
    }

    @Override
    protected String getUrl() {
        return null;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_details;
    }


    /**
     * 设置数据
     * @param productDetail 商品详情-实体类
     */
    private void setDetailData(ProductDetailBean productDetail) {
        if (llSpecification == null) {
            return;
        }
        List<ProductDetailImageBean> images = productDetail.getSkuInstructionImageList();
        if (images != null && images.size() > 0) {
            ilSpecification.bindData(images);
            llSpecification.setVisibility(View.VISIBLE);
        } else {
            llSpecification.setVisibility(View.GONE);
        }
    }
}
