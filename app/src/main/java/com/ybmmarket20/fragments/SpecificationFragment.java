package com.ybmmarket20.fragments;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.ProductDetailBean;
import com.ybmmarket20.bean.ProductDetailImageBean;
import com.ybmmarket20.bean.ProductInstructionBean;
import com.ybmmarket20.common.BaseFragment;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.view.CommodityRecyclerLayout;
import com.ybmmarket20.view.ImageLayout;

import java.util.List;

import butterknife.Bind;
import butterknife.ButterKnife;

/**
 * 商品详情-说明书
 */
public class SpecificationFragment extends BaseFragment {

    @Bind(R.id.specification_list)
    CommodityRecyclerLayout mSpecificationList;
    @Bind(R.id.ll_specification_tv)
    TextView llSpecificationTv;
    @Bind(R.id.ll_specification)
    LinearLayout llSpecification;
    @Bind(R.id.il_specification)
    ImageLayout ilSpecification;
    @Bind(R.id.il_about)
    ImageView ilAbout;
    @Bind(R.id.three)
    LinearLayout three;

    @Override
    protected void initData(String content) {

    }

    /**
     * 设置数据
     *
     * @param productDetail 商品详情-实体类
     */
    private void setSpecificationData(ProductDetailBean productDetail) {
        if (llSpecification == null) {
            return;
        }
        List<ProductDetailImageBean> images = productDetail.getSkuInstructionImageList();
        if (images != null && images.size() > 0) {
            ilSpecification.bindData(images);
            ilSpecification.setVisibility(View.VISIBLE);
            llSpecificationTv.setVisibility(View.VISIBLE);
            llSpecification.setVisibility(View.VISIBLE);
        } else {
            ilSpecification.setVisibility(View.GONE);
            llSpecificationTv.setVisibility(View.GONE);
            llSpecification.setVisibility(View.GONE);
        }
    }

    /**
     * 说明书
     *
     * @param productDetail 商品详情-实体类
     */
    public void upDataUI(ProductDetailBean productDetail) {
        if (mSpecificationList == null) {
            return;
        }
        if (productDetail != null) {
            getSkuFindSkuInstruction(productDetail.id);
        }

        //图文说明书
        setSpecificationData(productDetail);
    }

    /**
     * 获取商品说明书
     *
     * @param id 商品id
     */
    public void getSkuFindSkuInstruction(int id) {

        String merchantId = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        if (id > 0) {
            params.put("id", id + "");
        }
        HttpManager.getInstance().post(AppNetConfig.SKU_FINDSKUINSTRUCTION, params, new BaseResponse<List<ProductInstructionBean>>() {

            @Override
            public void onSuccess(String content, BaseBean<List<ProductInstructionBean>> obj, List<ProductInstructionBean> beans) {
                if (mSpecificationList == null) {
                    return;
                }
                if (obj != null) {
                    if (obj.isSuccess()) {
                        if (beans != null && beans.size() > 0) {
                            setData(beans);
                        }
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
            }
        });
    }

    /**
     * 说明书
     *
     * @param data 商品详情-实体类
     */
    public void setData(List<ProductInstructionBean> data) {
        if (mSpecificationList == null) {
            return;
        }

        if (data == null || data.isEmpty()) {
            return;
        }

        mSpecificationList.setItemData(data);
    }

    @Override
    protected void initTitle() {

    }

    @Override
    protected RequestParams getParams() {
        return null;
    }

    @Override
    protected String getUrl() {
        return null;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_specification;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        ButterKnife.unbind(this);
    }

}
