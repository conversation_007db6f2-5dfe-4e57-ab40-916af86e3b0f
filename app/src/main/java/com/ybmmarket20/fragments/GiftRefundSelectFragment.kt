package com.ybmmarket20.fragments

import android.graphics.Color
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.View
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.adapter.GiftRefundSelectAdapter
import com.ybmmarket20.bean.RefundProductListBean
import com.ybmmarket20.common.BaseFragment
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.viewmodel.GiftRefundSelectBottomDialogVM
import kotlinx.android.synthetic.main.fragment_gift_select_refund.rv_gift_select
import kotlinx.android.synthetic.main.fragment_gift_select_refund.tvConfirm
import kotlinx.android.synthetic.main.fragment_gift_select_refund.tv_select
import java.util.regex.Matcher
import java.util.regex.Pattern


/**
 * @class   GiftSelectFragment
 * <AUTHOR>
 * @date  2024/9/13
 * @description
 */
class GiftRefundSelectFragment(
    val mData: MutableList<RefundProductListBean>,
    private val mGiftPromotionId: String,
    private val mSelectAmount: Int,
    private val mCallback: ((list: MutableList<RefundProductListBean>, giftPromotionId: String) -> Unit)? = null
) : BaseFragment() {

    private val mViewModel: GiftRefundSelectBottomDialogVM by lazy {
        ViewModelProvider(requireActivity())[GiftRefundSelectBottomDialogVM::class.java]
    }
    private val mAdapter: GiftRefundSelectAdapter by lazy {
        GiftRefundSelectAdapter()
    }

    private var mCanSelectNumber = mSelectAmount
    private val mSelectNumberLiveData = MutableLiveData(0)


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setObservable()

        rv_gift_select.let {
            it.layoutManager = LinearLayoutManager(requireActivity())
            it.adapter = mAdapter.apply {
                canSelectNumber = mCanSelectNumber

                selectedNumberChangeCallBack = {
                    mSelectNumberLiveData.value = mAdapter.getSelectGiftNumber()
                }
            }
        }

        tvConfirm.setOnClickListener {
            if (mSelectAmount != getSelectGiftNumber()) {
                ToastUtils.showShort("需退还${mSelectAmount}件赠品，还需选择${mSelectAmount - getSelectGiftNumber()}件商品，请选择后保存")
                return@setOnClickListener
            }
            mCallback?.invoke(mData, mGiftPromotionId)
        }

        setSelectText(mSelectNumberLiveData.value ?: 0)
        mAdapter.setNewData(mData)

        mSelectNumberLiveData.value = mAdapter.getSelectGiftNumber()
    }

    private fun setObservable() {

        mSelectNumberLiveData.observe(requireActivity()) {
            setSelectText(it)
        }

    }

    private fun setSelectText(selectNumber: Int) {
        val content = requireActivity().resources.getString(
            R.string.str_gift_refund_number,
            mCanSelectNumber.toString(),
            selectNumber.toString()
        )
        val spannableString = SpannableString(content)

        // 查找字符串中的所有数字
        val pattern: Pattern = Pattern.compile("\\d+")
        val matcher: Matcher = pattern.matcher(spannableString)

        // 改变数字的颜色
        while (matcher.find()) {
            spannableString.setSpan(
                ForegroundColorSpan(Color.RED),
                matcher.start(),
                matcher.end(),
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        tv_select.text = spannableString
    }

    /**
     * 获取目前选中的赠品数
     * @return Int
     */
    fun getSelectGiftNumber():Int{
        var selectNumber = 0
        (mData as ArrayList<RefundProductListBean>).forEach {
            if (it.isGiftSelected){
                selectNumber += it.curSelectedCount
            }
        }

        return selectNumber
    }

    override fun initData(content: String?) {
    }

    override fun initTitle() {
    }

    override fun getParams(): RequestParams? = null


    override fun getUrl(): String = ""

    override fun getLayoutId(): Int = R.layout.fragment_gift_select_refund
}