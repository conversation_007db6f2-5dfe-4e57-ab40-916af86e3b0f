package com.ybmmarket20.fragments

import android.os.Handler
import com.google.gson.reflect.TypeToken
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybmmarket20.R
import com.ybmmarket20.activity.*
import com.ybmmarket20.adapter.AgentOrderListAdapter
import com.ybmmarket20.bean.AgentOrderListBean
import com.ybmmarket20.bean.AgentOrderListRowBean
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.RefreshFragment
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.eventbus.Event
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst.*
import com.ybmmarket20.utils.SpUtil
import java.lang.reflect.Type

/**
 * 代下单列表Fragment
 */
class AgentOrderListFragment : RefreshFragment<AgentOrderListRowBean, AgentOrderListBean<AgentOrderListRowBean>>() {

    var adapter: AgentOrderListAdapter? = null

    override fun getRequestParams(): RequestParams = RequestParams().apply {
        put("merchantId", SpUtil.getMerchantid())
        put("status", arguments?.getInt(AGENT_ORDER_ARGUMENT_KEY).toString())
    }

    override fun getAdapter(rows: MutableList<AgentOrderListRowBean>?): YBMBaseAdapter<AgentOrderListRowBean> {
        if (adapter == null) {
            adapter = AgentOrderListAdapter(R.layout.item_agent_order, rows, activity as BaseActivity).apply {}
        }
        return adapter as AgentOrderListAdapter
    }

    override fun getUrl(): String = AppNetConfig.AGENT_ORDER_LIST

    override fun getType(): Type = object : TypeToken<BaseBean<AgentOrderListBean<AgentOrderListRowBean>>>() {}.type

    override fun getLayoutId(): Int = R.layout.fragment_authorization_area

    override fun getTitle(): String = when (arguments?.getInt(AGENT_ORDER_ARGUMENT_KEY)) {
        AGENT_ORDER_STATUS_ALL -> "全部"
        AGENT_ORDER_STATUS_UNCONFIRM -> "待确认"
        AGENT_ORDER_STATUS_REJECTED -> "已驳回"
        else -> "已取消"
    }

    override fun receiveEvent(event: Event<*>?) {
        super.receiveEvent(event)
        event?.apply {
            val currentStatus = arguments?.getInt(AGENT_ORDER_ARGUMENT_KEY)
            if (event.code == RX_BUS_AGENT_ORDER_REJECT_ORDER && event.data is AgentOrderListRowBean) {
                val data: AgentOrderListRowBean? = event.data as AgentOrderListRowBean
                when (currentStatus) {
                    //全部&&待确认，更新状态
                    AGENT_ORDER_STATUS_ALL,
                    AGENT_ORDER_STATUS_UNCONFIRM -> {
                        rows.forEachIndexed { index, bean ->
                            if (data != null && data.id == bean.id) {
                                if (currentStatus == AGENT_ORDER_STATUS_UNCONFIRM) rows.removeAt(index)
                                else bean.status = AGENT_ORDER_STATUS_REJECTED
                                getAdapter(rows).notifyDataSetChanged()
                                return@apply
                            }
                        }
                    }

                    //已驳回
                    AGENT_ORDER_STATUS_REJECTED -> {
                        rows.add(0, data)
                        getAdapter(rows).notifyItemChanged(0)
                    }
                }
            } else if(event.code == RX_BUS_AGENT_ORDER_CONFIRM_ORDER && event.data is String) {
                if(currentStatus == AGENT_ORDER_STATUS_ALL || currentStatus == AGENT_ORDER_STATUS_UNCONFIRM) {
                    val orderId = event.data as String
                    rows.forEachIndexed { index, agentOrderListRowBean ->
                        if(orderId ==agentOrderListRowBean.id) {
                            rows.removeAt(index)
                            getAdapter(rows).notifyDataSetChanged()
                            return@apply
                        }
                    }
                }
            } else if(event.code == RX_BUS_AGENT_ORDER_CANCLE && event.data is AgentOrderListRowBean) {
                //倒计时完成设置为取消状态
                val data: AgentOrderListRowBean? = event.data as AgentOrderListRowBean
                if(currentStatus == AGENT_ORDER_STATUS_ALL || currentStatus == AGENT_ORDER_STATUS_UNCONFIRM) {
                    rows.forEachIndexed{ index, agentOrderListRowBean ->
                        if(data?.id == agentOrderListRowBean.id) {
                            if(currentStatus == AGENT_ORDER_STATUS_ALL){
                                agentOrderListRowBean.status = RX_BUS_AGENT_ORDER_CANCLE
                            } else if(currentStatus == AGENT_ORDER_STATUS_UNCONFIRM) {
                                rows.removeAt(index)
                            }
                            Handler().post{
                                getAdapter(rows).notifyDataSetChanged()
                            }
                            return@apply
                        }
                    }
                }

                if(currentStatus == AGENT_ORDER_STATUS_CANCELED) {
                    data?.status = AGENT_ORDER_STATUS_CANCELED
                    //当前为已取消状态Tab，接到消息后应添加一个取消状态的订单，但倒计时成功后可能会接收到过个相同消息，所以判断当前列表是否包含该订单
                    //如果包含该订单则不添加，不包含就添加
                    if(rows.find { it.id == data?.id } == null){
                        rows.add(0, data as AgentOrderListRowBean)
                        getAdapter(rows).notifyDataSetChanged()
                    }
                }
            }
        }
    }

    override fun getEmptyImg(): Int = R.drawable.icon_empty

    override fun getEmptyMsg(): String = getString(R.string.no_order_data)

    /**
     * 更新待确认订单的时间
     */
    fun updateUnConfirmOrderTime() {
        rows?.let {
            rows?.forEach {
                if(it.status == AGENT_ORDER_STATUS_UNCONFIRM) it.currentDate = it.currentDate + 1000
            }
            getAdapter(rows).notifyDataSetChanged()
        }
    }

    /**
     * 获取当前tab状态
     */
    fun getAgentOrderTabStatus(): Int? {
        return arguments?.getInt(AGENT_ORDER_ARGUMENT_KEY)
    }
}