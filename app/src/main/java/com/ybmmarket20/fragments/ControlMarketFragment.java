package com.ybmmarket20.fragments;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;

import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.ProductDetailActivity;
import com.ybmmarket20.adapter.GoodsListAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.RowsListBean;
import com.ybmmarket20.common.BaseFragment;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.SpUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;

/**
 * 控销专区
 */

public class ControlMarketFragment extends BaseFragment {

    @Bind(R.id.lv)
    CommonRecyclerView lv;

    private int tab;
    private GoodsListAdapter detailAdapter;
    private int pageSize = 10;
    private int pager_1 = 1;
    private int pager_2 = 1;
    private List<RowsBean> controlPage_1;
    private List<RowsBean> controlPage_2;
    private List<RowsBean> rowsList_1 = new ArrayList<>();
    private List<RowsBean> rowsList_2 = new ArrayList<>();
    private String controlFlag = "1";
    private static final int CONTROL_MARKET_ALL = 0;
    private static final int CONTROL_MARKET_MORE = 1;
    private String mEmptyStr;

    @Override
    protected void initData(String content) {
        tab = getArguments().getInt("tab");
        switch (tab) {
            case CONTROL_MARKET_ALL:
                controlFlag = "1";
                break;
            case CONTROL_MARKET_MORE:
                controlFlag = "0";
                break;
        }
        detailAdapter = new GoodsListAdapter(R.layout.item_goods, rowsList_1, false, false);
        lv.setListener(new CommonRecyclerView.Listener() {

            @Override
            public void onRefresh() {
                getControlResult(0);
            }

            @Override
            public void onLoadMore() {
                if (tab == CONTROL_MARKET_ALL) {
                    getControlResult(pager_1);
                }
                if (tab == CONTROL_MARKET_MORE) {
                    getControlResult(pager_2);
                }
            }
        });
        lv.setEnabled(true);
        lv.setAdapter(detailAdapter);
        detailAdapter.openLoadMore(pageSize, true);
        lv.setEmptyView(R.layout.layout_empty_view, R.drawable.icon_empty, "这里没有可采购的控销商品\n我们去看看别的宝贝吧!");
        detailAdapter.setOnListItemClickListener(new GoodsListAdapter.OnListViewItemClickListener() {
            @Override
            public void onItemClick(RowsBean rows) {
                if (rows != null) {
                    Intent intent = new Intent(getNotNullActivity(), ProductDetailActivity.class);
                    intent.putExtra(IntentCanst.PRODUCTID, rows.getId() + "");
                    startActivity(intent);
                }
            }
        });
    }

    @Override
    protected void initTitle() {

    }

    @Override
    protected RequestParams getParams() {
        return null;
    }

    @Override
    protected String getUrl() {
        return null;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_control_market;
    }

    public static ControlMarketFragment getInstance(int tab) {
        ControlMarketFragment fragment = new ControlMarketFragment();
        fragment.setArguments(setArguments(tab));
        return fragment;
    }

    public static Bundle setArguments(int tab) {
        Bundle bundle = new Bundle();
        bundle.putInt("tab", tab);
        return bundle;
    }

    /**
     * 控销列表
     *
     * @param pager 分页
     *              controlFlag 全部控销或者我的控销
     *              url => "controlSales/fetchData"
     */
    private void getControlResult(final int pager) {

        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        params.put("limit", String.valueOf(10));
        params.put("offset", String.valueOf(0));
        if (pager >= 1) {
            params.put("offset", String.valueOf(pager));
        }
        if (!TextUtils.isEmpty(controlFlag)) {
            params.put("controlFlag", controlFlag);
        }

        HttpManager.getInstance().post(AppNetConfig.CONTROL_SALES, params, new BaseResponse<RowsListBean>() {

            @Override
            public void onSuccess(String content, BaseBean<RowsListBean> obj, RowsListBean bean) {
                if (lv == null) {
                    return;
                }
                completion();
                if (obj != null) {
                    if (obj.isSuccess()) {
                        if (bean != null && bean.rows != null) {
                            if (pager < 1) {//下拉刷新

                                pager_1 = 1;
                                pager_2 = 1;

                                if (tab == 0) {
                                    controlPage_1 = bean.rows;
                                    if (controlPage_1 != null) {
                                        diffData(controlPage_1, rowsList_1);
                                    }
                                    detailAdapter.setNewData(rowsList_1);
                                    if (controlPage_1 != null && controlPage_1 != null && controlPage_1.size() < 10) {
                                        detailAdapter.notifyDataChangedAfterLoadMore(true);
                                    }
                                } else if (tab == 1) {
                                    controlPage_2 = bean.rows;
                                    if (controlPage_2 != null) {
                                        diffData(controlPage_2, rowsList_2);
                                    }
                                    detailAdapter.setNewData(rowsList_2);
                                    if (controlPage_2 != null && controlPage_2 != null && controlPage_2.size() < 10) {
                                        detailAdapter.notifyDataChangedAfterLoadMore(true);
                                    }
                                }

                            } else {

                                if (tab == 0) {
                                    controlPage_1 = bean.rows;
                                    if (controlPage_1 != null) {
                                        int size_1 = controlPage_1.size();

                                        for (RowsBean b : controlPage_1) {
                                            if (rowsList_1.contains(b)) {
                                                rowsList_1.remove(b);
                                            }
                                        }

                                        rowsList_1.addAll(controlPage_1);
                                        if (size_1 >= pageSize) {
                                            pager_1++;
                                        }

                                        detailAdapter.notifyDataChangedAfterLoadMore(size_1 >= pageSize);
                                    }
                                } else if (tab == 1) {
                                    controlPage_2 = bean.rows;
                                    if (controlPage_2 != null) {

                                        int size_2 = controlPage_2.size();

                                        for (RowsBean b : controlPage_2) {
                                            if (rowsList_2.contains(b)) {
                                                rowsList_2.remove(b);
                                            }
                                        }

                                        rowsList_2.addAll(controlPage_2);

                                        if (size_2 >= pageSize) {
                                            pager_2++;
                                        }

                                        detailAdapter.notifyDataChangedAfterLoadMore(size_2 >= pageSize);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                if (lv == null) {
                    return;
                }
                completion();
            }
        });
    }

    private void diffData(List<RowsBean> news, List<RowsBean> old) {
        if (news == null || news.size() <= 0) {
            return;
        }

        if (old == null) {
            old = new ArrayList<>();
            old.addAll(news);
            return;
        }

        for (RowsBean controlBean : news) {
            if (old.contains(controlBean)) {
                old.remove(controlBean);
            }
        }
        old.addAll(0, news);
    }

    private void completion() {
        if (lv != null) {
            lv.setRefreshing(false);
        }
    }

    /**
     * 如果是当前用户
     */
    public void refreshList() {
        if (lv != null) {
            lv.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (detailAdapter != null && detailAdapter.getData() != null && !detailAdapter.getData().isEmpty()) {
                        detailAdapter.notifyDataSetChanged();
                    }
                }
            }, 500);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        refreshList();
    }
}
