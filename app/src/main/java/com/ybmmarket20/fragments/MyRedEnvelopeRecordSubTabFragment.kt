package com.ybmmarket20.fragments

import android.graphics.Color
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.Fragment
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.IRedEnvelopeBean
import com.ybmmarket20.bean.RedEnvelopeRecordSubItem
import com.ybmmarket20.utils.UiUtils


/**
 * 收支记录(全部记录,收入记录,支出记录)
 */
class MyRedEnvelopeRecordSubTabFragment(val redEnvelopeType: Int) :
    BaseRedEnvelopeSubTabFragment() {

    var mAdapter: YBMBaseAdapter<IRedEnvelopeBean>? = null

    override fun init() {
        getRedEnvelopeAdapter().setOnLoadMoreListener { getRedEnvelopeRecordList("$redEnvelopeType") }
    }

    override fun getRedEnvelopeAdapter(): YBMBaseAdapter<IRedEnvelopeBean> {
        if (mAdapter == null) mAdapter = MyRedEnvelopeRecordSubTabAdapter(mList)
        return mAdapter!!
    }

    override fun loadData() {
        getRedEnvelopeRecordList("$redEnvelopeType")
    }

    companion object {
        fun getInstance(redEnvelopeType: Int): Fragment =
            MyRedEnvelopeRecordSubTabFragment(redEnvelopeType)
    }

    inner class MyRedEnvelopeRecordSubTabAdapter(var list: ArrayList<IRedEnvelopeBean>) :
        YBMBaseAdapter<IRedEnvelopeBean>(R.layout.item_red_envelope_record_sub_tab, list) {
        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: IRedEnvelopeBean?) {
            if (baseViewHolder != null && t != null && t is RedEnvelopeRecordSubItem) {
                getMyRedEnvelopeRecordSubTabItem(redEnvelopeType).onHandle(baseViewHolder, t)
            }
        }
    }

    fun getMyRedEnvelopeRecordSubTabItem(redEnvelopeType: Int): MyRedEnvelopeRecordSubTabItem {
        return when (redEnvelopeType) {
            0 -> MyRedEnvelopeRecordSubTabItem.MyRedEnvelopeRecordSubTabItemAll
            1 -> MyRedEnvelopeRecordSubTabItem.MyRedEnvelopeRecordSubTabItemIncome
            else -> MyRedEnvelopeRecordSubTabItem.MyRedEnvelopeRecordSubTabItemExpend
        }
    }

}

abstract class MyRedEnvelopeRecordSubTabItem {

    fun onBaseHandle(holder: YBMBaseHolder, bean: RedEnvelopeRecordSubItem){
        holder.setText(R.id.tv_red_envelope_title, bean.typeText)
        holder.setText(R.id.tv_red_envelope_time, bean.ctimeStr)
        var itemRedEnvelopeRecordSubTabRow = holder.getView<ConstraintLayout>(R.id.item_red_envelope_record_sub_tab_row)
        val params =  itemRedEnvelopeRecordSubTabRow.layoutParams as ViewGroup.LayoutParams
        if(!bean.instanceRemark.isNullOrEmpty()){
            holder.setText(R.id.tv_instance_remark, bean.instanceRemark)
            holder.setVisible(R.id.tv_instance_remark, true)
            itemRedEnvelopeRecordSubTabRow.setLayoutParams(params)
        }else{
            holder.setVisible(R.id.tv_instance_remark, false)
            itemRedEnvelopeRecordSubTabRow.setLayoutParams(params)
        }
        if (bean.changeAmount > 0){
            holder.setText(R.id.tv_red_envelope_amount, "+${UiUtils.transform(bean.changeAmount)}")
        } else {
            holder.setText(R.id.tv_red_envelope_amount, "${UiUtils.transform(bean.changeAmount)}")
        }
    }
    abstract fun onHandle(holder: YBMBaseHolder, bean: RedEnvelopeRecordSubItem)

    object MyRedEnvelopeRecordSubTabItemAll: MyRedEnvelopeRecordSubTabItem() {
        override fun onHandle(holder: YBMBaseHolder, bean: RedEnvelopeRecordSubItem) {
            onBaseHandle(holder, bean)
            if (bean.changeAmount > 0) {
                holder.setTextColor(R.id.tv_red_envelope_amount, Color.parseColor("#FF0803"))
            } else {
                holder.setTextColor(R.id.tv_red_envelope_amount, Color.parseColor("#00B679"))
            }
        }
    }

    object MyRedEnvelopeRecordSubTabItemIncome: MyRedEnvelopeRecordSubTabItem() {
        override fun onHandle(holder: YBMBaseHolder, bean: RedEnvelopeRecordSubItem) {
            onBaseHandle(holder, bean)
            holder.setTextColor(R.id.tv_red_envelope_amount, Color.parseColor("#FF0803"))
        }
    }

    object MyRedEnvelopeRecordSubTabItemExpend: MyRedEnvelopeRecordSubTabItem() {
        override fun onHandle(holder: YBMBaseHolder, bean: RedEnvelopeRecordSubItem) {
            onBaseHandle(holder, bean)
            holder.setTextColor(R.id.tv_red_envelope_amount, Color.parseColor("#00B679"))
        }
    }
}