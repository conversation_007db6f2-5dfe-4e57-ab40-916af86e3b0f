package com.ybmmarket20.fragments

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.view.View
import com.flyco.tablayout.listener.OnTabSelectListenerV2
import com.ybmmarket20.bean.homesteady.Banner
import com.ybmmarket20.bean.homesteady.BaseBannerBean
import com.ybmmarket20.bean.homesteady.FastEntryItem
import com.ybmmarket20.common.LicenseStatusFragment
import com.ybmmarket20.home.MainActivity
import com.ybmmarket20.home.newpage.adapter.IHomeComponentAnalysis
import com.ybmmarket20.message.Message
import com.ybmmarket20.report.ReportActionSubModuleClickBean
import com.ybmmarket20.report.ReportPageExposureBean
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.*
import com.ybmmarket20.view.homesteady.callback.IHomeAlertAnalysisCallback
import com.ybmmarket20.view.homesteady.callback.IHomeAlertAnalysisCallbackV3
import com.ybmmarket20.view.homesteady.callback.IHomeDialogCustomCallback
import com.ybmmarket20.view.homesteady.callback.IRecommendShopAnalysisCallback
import com.ybmmarket20.view.homesteady.callback.ISeckillAnalysisCallback
import com.ybmmarket20.view.homesteady.callback.IShopAnalysisCallback
import com.ybmmarket20.xyyreport.ReportUtil
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.page.home.HomeReportEvent
import com.ybmmarket20.xyyreport.spm.IPageParams
import com.ybmmarket20.xyyreport.spm.TrackData
import com.ybmmarket20.xyyreport.spm.XyyReportActivity
import org.json.JSONObject

/**
 * 处理首页V2埋点回调
 */
abstract class HomeSteadyAnalysisFragmentV2
    : LicenseStatusFragment(),
    Message.Listener,
    ISeckillAnalysisCallback,
    IShopAnalysisCallback,
    OnTabSelectListenerV2,
    IHomeAlertAnalysisCallback,
    IRecommendShopAnalysisCallback,
    IHomeAlertAnalysisCallbackV3,
    IHomeComponentAnalysis,
    IHomeDialogCustomCallback{

        var allDrugTrackData: TrackData? = null
        val mCacheHomeTabCommonRecord = mutableSetOf<Int>()

    /**
     * banner点击埋点回调
     */
    fun bannerAnalysisCallback(baseBannerInfo: BaseBannerBean, position: Int) {
        try {
            if (baseBannerInfo is Banner) {
                homeSteadyAnalysisBannerClick(
                        AnalysisConst.HomeSteady.ACTION_HOME_BANNER_CLICK_V2,
                        baseBannerInfo.getActionLink(),
                        "${position + 1}",
                    (baseBannerInfo as Banner).image
                )
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 快捷入口点击埋点回调
     */
    fun fastEntryAnalysisCallback(
        action: String,
        position: Int,
        item: FastEntryItem,
        view: View,
        size: Int,
        image_url: String?
    ) {
        val index = ((view.tag as Int) * size + position + 1).toString()
        homeSteadyAnalysisFastEntryClick(
            AnalysisConst.HomeSteady.ACTION_HOME_FASTENTRY_CLICK_V2,
            action,
            index,
            item.entry,
            item.icon
        ) {
            it.putExtraString("text", item.entry)
            it.putExtraString("located", XyyIoUtil.PAGE_HOMEPAGE)
            it.putExtraString("offset", index)
            it.putExtraString("image_url", image_url?: "")
        }
    }

    /**
     * 胶囊位入口点击埋点回调
     */
    private fun streamerAnalysisCallback(action: String, text: String, actionKey: String) {
        homeSteadyAnalysisStreamerClick(actionKey, action, text)
    }

    /**
     * 胶囊位入口点击埋点回调(第一个胶囊位)
     */
    fun streamerAnalysisCallbackFirst(action: String, text: String) {
        streamerAnalysisCallback(
            action,
            text,
            AnalysisConst.HomeSteady.ACTION_HOME_STREAMER_CLICK_V2_FIRST
        )
    }

    /**
     * 胶囊位入口点击埋点回调(第二个胶囊位)
     */
    fun streamerAnalysisCallbackSecond(action: String, text: String) {
        streamerAnalysisCallback(
            action,
            text,
            AnalysisConst.HomeSteady.ACTION_HOME_STREAMER_CLICK_V2_SECOND
        )
    }

    /**
     * 导购入口点击埋点
     */
    fun shoppingGuideAnalysisCallback(action: String, offset: Int, text: String, sku_id: String) {
        homeSteadyAnalysisShoppingGuide(
            AnalysisConst.HomeSteady.ACTION_HOME_SHOPPING_GUIDE_CLICK_V2,
            action,
            "${offset + 1}",
            text,
            sku_id
        )
    }

    /**
     * 定时活动顶栏点击
     */
    override fun onHomeSteadyAnalysisSeckillTopClick(action: String?, text: String?) {
        homeSteadyAnalysisSeckillTopClick(
            AnalysisConst.HomeSteady.ACTION_HOME_SECKILL_TOP_CLICK_V2,
            action,
            text
        )
    }

    /**
     * 定时活动更多点击
     */
    override fun onHomeSteadyAnalysisSeckillMoreClick(action: String?, text: String?) {
        homeSteadyAnalysisSeckillMoreClick(
            AnalysisConst.HomeSteady.ACTION_HOME_SECKILL_MORE_CLICK_V2,
            action,
            text
        )
    }

    /**
     * 定时活动商品点击
     */
    override fun onHomeSteadyAnalysisSeckillGoodsClick(
        action: String?,
        offset: Int,
        text: String?,
        id: String?,
        actionUrl: String?
    ) {
        homeSteadyAnalysisSeckillGoodsClick(
            AnalysisConst.HomeSteady.ACTION_HOME_SECKILL_GOODS_CLICK_V2,
            action,
            "${offset + 1}",
            text,
            id
        )
        RoutersUtils.open(actionUrl)
    }

    /**
     * 定时活动商品曝光
     */
    override fun onHomeSteadyAnalysisSeckillGoodsExposure(
        action: String?,
        offset: Int,
        text: String?,
        id: String?
    ) {
        homeSteadyAnalysisSeckillGoodsExposure(
            AnalysisConst.HomeSteady.ACTION_HOME_SECKILL_GOODS_EXPOSURE_V2,
            action,
            "${offset + 1}",
            text,
            id
        )
    }

    /**
     * 精选店铺顶栏点击
     */
    override fun onHomeSteadyAnalysisShopTopClick(action: String?, text: String?) {
        homeSteadyAnalysisShopTopClick(
            AnalysisConst.HomeSteady.ACTION_HOME_SHOP_TOP_CLICK_V2,
            action,
            text
        )
    }

    /**
     * 精选店铺点击店铺
     */
    override fun onHomeSteadyAnalysisShopClick(
        action: String?,
        offset: String?,
        text: String?,
        sku_id: String?
    ) {
        homeSteadyAnalysisShopClick(
            AnalysisConst.HomeSteady.ACTION_HOME_SHOP_SHOP_CLICK_V2,
            action,
            offset,
            text,
            sku_id
        )
    }

    /**
     * 精选店铺店铺曝光
     */
    override fun onHomeSteadyAnalysisShopGoodsExposure(
        action: String?,
        offset: String?,
        text: String?,
        sku_id: String?
    ) {
        homeSteadyAnalysisShopGoodsExposure(
            AnalysisConst.HomeSteady.ACTION_HOME_SHOP_GOODS_EXPOSURE_V2,
            action,
            offset,
            text,
            sku_id
        )
    }

    /**
     * 首页商品feed流Tab点击
     */
    override fun onTabSelect(position: Int, title: String?, subTitle: String?) {
        homeSteadyAnalysisGoodsFeedTabClick(
            AnalysisConst.HomeSteady.ACTION_HOME_GOODS_FEED_TAB_CLICK_V2,
            "${position + 1}",
            title
        )
    }

    /**
     * 首页商品feed流重复点击
     */
    override fun onTabReselect(position: Int, title: String?, subTitle: String?) {
    }

    /**
     * 触发事件
     */
    fun trackEvent(eventName: String) {
        XyyIoUtil.track(eventName)
    }

    override fun onTurnTableExposureCallback() {
        trackEvent(XyyIoUtil.ACTION_NEWFLOAT_EXPOSURE)
    }

    override fun onTurnTableClickCallback(actionUrl: String) {
        XyyIoUtil.track(XyyIoUtil.ACTION_NEWFLOAT_CLICK, JSONObject().apply {
            put("link", actionUrl)
        })
    }

    override fun onHomeSteadyAnalysisRecommendShopMoreClick(action: String?, text: String?) {
        XyyIoUtil.track(AnalysisConst.HomeSteady.ACTION_HOME_RECOMMEND_SHOP_MORE_CLICK, hashMapOf(
            "action" to action,
            "text" to text
        ))
    }

    override fun onHomeSteadyAnalysisRecommendShopClick(
        shopCode: String?,
        offset: String?
    ) {
        XyyIoUtil.track(AnalysisConst.HomeSteady.ACTION_HOME_RECOMMEND_SHOP_ITEM_CLICK, hashMapOf(
            "shopCode" to offset,
            "offset" to shopCode
        ))
    }

    override fun onHomeSteadyAnalysisRecommendShopExposure(
        shopCode: String?,
        offset: String?
    ) {
        XyyIoUtil.track(AnalysisConst.HomeSteady.ACTION_HOME_RECOMMEND_SHOP_EXPOSURE, hashMapOf(
            "shopCode" to shopCode,
            "offset" to offset
        ))
    }

    /**
     * 大转盘曝光
     */
    override fun onAlertExposureCallback(trackData: TrackData?) {
        context?.let { HomeReportEvent.onHomeBigWheelAlertPV(it, trackData?.spmEntity) }
    }

    /**
     * 大转盘点击
     */
    override fun onAlertClickCallback(trackData: TrackData?) {
        context?.let { HomeReportEvent.onAlertClick(it, trackData?.spmEntity, trackData?.scmEntity) }
    }

    override fun onComponentExposure(context: Context, trackData: TrackData?, position: Int, block: (()-> Unit)?) {
        block?.invoke()
        HomeReportEvent.trackHomeComponentExposure(context, trackData?.spmEntity)
    }

    override fun onSubcomponentClick(context: Context, trackData: TrackData?) {
        HomeReportEvent.trackHomeSubComponentClick(context, trackData?.spmEntity, trackData?.scmEntity)
    }

    fun onTabClick(context: Context, trackData: TrackData?, position: Int) {
        if (context !is IPageParams) return
        SpmLogUtil.print("首页-tab点击")
        val newTrackData = trackData?.newTrackData()
        newTrackData?.spmEntity?.spmC = if (context.getSpmCtn()?.spmB?.startsWith("newhome") == true) {
            "newIndexSearch@1"
        }
        else "tabSearch@1"
        if (context is XyyReportActivity) {
            newTrackData?.spmEntity?.spmB = context.getSpmCtn()?.spmB
        }
        onSubcomponentClick(context, newTrackData)
    }

    fun onSubcomponentALLDrugClick(context: Context, trackData: TrackData?) {
        if (context !is IPageParams) return
        val pageSpm = (context as IPageParams).getSpmCtn()
        val newSpm = trackData?.spmEntity?.newInstance()?.apply {
            spmB = pageSpm?.spmB
            if (context is XyyReportActivity) {
                spmC = if (context.getSpmCtn()?.spmB?.startsWith("newhome") == true) "newIndexSearch@1"
                else "tabSearch@1"
            }
        }
        if (context is XyyReportActivity) {
            trackData?.scmEntity?.scmC = context.mScmCnt?.scmC
        }
        SpmLogUtil.print("首页-全部药品点击")
        HomeReportEvent.trackHomeSubComponentClick(context, newSpm, trackData?.scmEntity)
    }

    fun homeCommonTabClickBottomExposure(position: Int) {
        if (!mCacheHomeTabCommonRecord.contains(position)) {
            mCacheHomeTabCommonRecord.add(position)
        } else {
            Handler(Looper.getMainLooper()).postDelayed({(notNullActivity as MainActivity).trackTabComponentExposure(requireActivity(), null)}, 200)

        }
    }

    override fun onDialogCustomExposure(trackData: TrackData?) {
        HomeReportEvent.onDialogCustomExposure(requireActivity(), trackData?.spmEntity)
    }

    override fun onDialogImageExposure(trackData: TrackData?) {
        HomeReportEvent.onDialogImageExposure(requireActivity(), trackData)
    }

    override fun onDialogImageClick(trackData: TrackData?) {
        HomeReportEvent.onDialogImageClick(requireActivity(), trackData)
    }

    override fun onDialogCouponExposure(trackData: TrackData?) {
        HomeReportEvent.onDialogCouponExposure(requireActivity(), trackData)
    }

    override fun onDialogCouponClick(trackData: TrackData?) {
        HomeReportEvent.onDialogCouponClick(requireActivity(), trackData)
    }

    override fun resetExposureRecord(){}
}