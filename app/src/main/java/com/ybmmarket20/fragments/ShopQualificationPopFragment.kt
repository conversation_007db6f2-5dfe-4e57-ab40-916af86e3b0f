package com.ybmmarket20.fragments

import androidx.lifecycle.Observer
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.view.ShowBottomSheetDialog
import com.ybmmarket20.viewmodel.ShopQualificationViewModel

/**
 * Pop店铺-店铺资质
 */
class ShopQualificationPopFragment: ShopQualificationSelfFragment() {

    override fun getListData(viewModel: ShopQualificationViewModel) {
        viewModel.callPhoneLiveData.observe(this, Observer { num ->
            RoutersUtils.telPhone(true, num)
        })
        viewModel.getPopQualification()
    }
}