package com.ybmmarket20.fragments;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import com.google.android.material.appbar.AppBarLayout;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.ElectronicPlanDetailActivity;
import com.ybmmarket20.adapter.PlanProductAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.PlanProductInfoBean;
import com.ybmmarket20.bean.PlanProductInfoData;
import com.ybmmarket20.bean.SearchFilterBean;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.LicenseStatusFragment;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.ViewOnClickListener;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.DialogUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.view.BaseFilterPopWindow;
import com.ybmmarket20.view.ListFilterPopWindow;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * Created by ybm on 2017/6/21.
 * 计划单全部商品
 */

public class PlanAllFragment extends LicenseStatusFragment implements
        PlanProductAdapter.OnSwipeListener, PlanProductAdapter.OnItemClickListener {

    @Bind(R.id.crv_product)
    CommonRecyclerView crvProduct;
    @Bind(R.id.rb_all_product)
    RadioButton rbAllProduct;
    @Bind(R.id.rb_sale_product)
    RadioButton rbSaleProduct;
    @Bind(R.id.rb_all)
    TextView rbAll;
    @Bind(R.id.tv_total_count)
    TextView tvTotalCount;
    @Bind(R.id.rb_yh)
    TextView rbYh;
    @Bind(R.id.rb_qh)
    TextView rbQh;
    @Bind(R.id.rb_wh)
    TextView rbWh;
    @Bind(R.id.ll_filter)
    LinearLayout llFilter;
    @Bind(R.id.appbar)
    AppBarLayout appbar;
    @Bind(R.id.rg_product_tab)
    RadioGroup radioGroup;
    @Bind(R.id.rb_filter)
    RadioGroup radioFilter;
    @Bind(R.id.rl_total_and_filtrate)
    RelativeLayout RlTotalAndFiltrate;

    private ElectronicPlanDetailActivity mActivity;
    private String mPlanId, mPlanName;
    private int mTabProductType;
    //商品状态（0,全部1：有货；2：缺货；3：无货）
    public int productStatus;
    private PlanProductAdapter mAdapter;
    private List<PlanProductInfoBean> allProductList = new ArrayList<>();
    private List<PlanProductInfoBean> mData = new ArrayList<>();
    private BroadcastReceiver br;
    private ListFilterPopWindow mPopPlanScreen;
    private String searchKey = "";

    public static PlanAllFragment getInstance(Bundle bundle) {
        if (bundle == null) {
            return new PlanAllFragment();
        }
        PlanAllFragment fragment = new PlanAllFragment();
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (context instanceof ElectronicPlanDetailActivity) {
            mActivity = (ElectronicPlanDetailActivity) context;
        }
    }

    @Override
    protected void initData(String content) {
        Bundle bundle = getArguments();
        if (bundle != null) {
            mPlanId = bundle.getString(ElectronicPlanDetailActivity.BUNDLE_PLAN_ID);
            mPlanName = bundle.getString(ElectronicPlanDetailActivity.BUNDLE_PLAN_NAME);
            mTabProductType = bundle.getInt(ElectronicPlanDetailActivity.BUNDLE_PLAN_TYPE);
        }
        mAdapter = new PlanProductAdapter(R.layout.list_item_plan_all_product, mData);
        View emptyView = getNotNullActivity().getLayoutInflater().inflate(R.layout.layout_empty_view, (ViewGroup) crvProduct.getParent(), false);
        mAdapter.setEmptyView(emptyView);
        mAdapter.setOnItemClickListener(this);
        mAdapter.setOnSwipeListener(this);
        mAdapter.setEnableLoadMore(false);
        crvProduct.setRefreshEnable(false);
        crvProduct.setShowAutoRefresh(false);
        crvProduct.setAdapter(mAdapter);
        YBMAppLike.changeThemeBg(R.drawable.base_header_dynamic_bg, radioGroup);
        tvTotalCount.setText("数量：" + 0 + "种");
        getAllPlanProduct();
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(IntentCanst.ACTION_PLAN_EDIT_PRODUCT_NUM);
        br = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                int num = intent.getIntExtra("num", 0);
                int from = intent.getIntExtra("from", 0);
                String code = intent.getStringExtra("code");
                if (!TextUtils.isEmpty(code) && from == 2) {
                    if (num == -1) {//同步删除
                        int index = -1;
                        for (int a = 0; a < allProductList.size(); a++) {
                            if (code.equals(allProductList.get(a).code)) {
                                index = a;
                            }
                        }
                        if (index >= 0) {
                            PlanProductInfoBean bean = allProductList.get(index);
                            allProductList.remove(index);
                            mData.remove(bean);
                            mAdapter.notifyDataSetChanged();
                        }
                        tvTotalCount.setText("数量：" + mData.size() + "种");
                    } else {
                        for (PlanProductInfoBean bean : allProductList) {
                            if (code.equals(bean.code)) {
                                bean.purchaseNumber = num;
                                if (mAdapter != null) {
                                    mAdapter.notifyDataSetChanged();
                                }
                            }
                        }
                    }
                }
            }
        };
        LocalBroadcastManager.getInstance(getContext()).registerReceiver(br, intentFilter);
    }

    @Override
    protected void initTitle() {

    }

    @Override
    protected RequestParams getParams() {
        return null;
    }

    @Override
    protected String getUrl() {
        return null;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_plan_all;
    }

    @OnClick({R.id.tv_scan_code, R.id.tv_manual_record, R.id.tv_plan_filtrate})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tv_scan_code:
                StringBuilder sb = new StringBuilder("ybmpage://captureactivity_plan/");
                sb.append(mPlanId).append("-").append(mPlanName);
                RoutersUtils.open(sb.toString());
                break;
            case R.id.tv_manual_record:
                if (!TextUtils.isEmpty(mPlanId)) {
                    RoutersUtils.open("ybmpage://replenishproduct/1/" + mPlanId + "/" + mPlanName);
                }
                break;
            case R.id.tv_plan_filtrate:
                if (mAdapter == null || RlTotalAndFiltrate == null) {
                    return;
                }
                initPopPlanScreen();
                mPopPlanScreen.show(RlTotalAndFiltrate);
                break;
        }
    }

    public void getAllPlanProduct(String key) {
        searchKey = key;
        String merchant_id = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("planningScheduleId", mPlanId);
        params.put("status", mTabProductType + "");
        showProgress();
        HttpManager.getInstance().post(AppNetConfig.PLAN_SCHEDULE_DETAIL, params, new BaseResponse<PlanProductInfoData>() {
            @Override
            public void onSuccess(String content, BaseBean<PlanProductInfoData> obj, PlanProductInfoData data) {
                dismissProgress();
                if (obj != null && obj.isSuccess()) {
                    updateLicenseStatus(data.licenseStatus, getCurrentLicenseStatusListener());
                    if (data.list == null) {
                        data.list = new ArrayList<>();
                    }
                    allProductList = data.list;
                    setListData(allProductList);
                    if (!TextUtils.isEmpty(key)) {
                        search(key);
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }
        });
    }


    public void getAllPlanProduct() {
        getAllPlanProduct("");
    }


    private void setListData(List<PlanProductInfoBean> listData) {
        if(tvTotalCount==null){
            return;
        }
        mData.clear();
        if (listData != null && !listData.isEmpty()) {
            mData.addAll(listData);
        }
        if (mAdapter != null) {
            mAdapter.setNewData(mData);
        }
        if (mData != null) {
            tvTotalCount.setText("数量：" + mData.size() + "种");
        }
    }


    @Override
    public void onItemClick(int position) {

    }

    @Override
    public void onEditNum(final int position) {
        if (mData == null || position >= mData.size()) {
            return;
        }
        String title = "修改补货数量";
        final String confirm = "确定";
        String cancel = "取消";
        String num = mData.get(position).purchaseNumber + "";
        String price = mData.get(position).getPrice();
        if (price == null) {
            price = "";
        }
        DialogUtil.modifyProductNum(getNotNullActivity(), num, price, new DialogUtil.DialogClickListener() {
            @Override
            public void confirm(String content) {
                if (TextUtils.isEmpty(content) || content.equals("&") || !content.contains("&")) {
                    ToastUtils.showShort("请输入正确内容");
                    return;
                }
                hideSoftInput();
                String[] strs = content.split("&");
                if (strs != null) {
                    modifyProductNum(strs[0], strs.length == 2 ? strs[1] : "", position);
                }
            }

            @Override
            public void cancel() {
                hideSoftInput();
            }

            @Override
            public void showSoftInput(View view) {
                if (mActivity == null || mActivity.isFinishing()) {
                    return;
                }
                InputMethodManager imm = (InputMethodManager) mActivity.getSystemService(Context.INPUT_METHOD_SERVICE);
                imm.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT);
            }

        });
    }

    @Override
    public void onDelete(final int position) {
        final AlertDialogEx alert = new AlertDialogEx(getNotNullActivity());
        alert.setTitle("删除");
        alert.setMessage("您确认删除吗？");
        alert.setCancelButton("取消", null);
        alert.setConfirmButton("确定", new ViewOnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                String productCode = mData.get(position).code;
                String planId = mData.get(position).planningScheduleId + "";
                if (TextUtils.isEmpty(productCode) || TextUtils.isEmpty(planId)) {
                    return;
                }
                deleteProduct(productCode, planId, position);
            }
        });
        alert.show();
    }

    @Override
    public void onSwipe(int position) {

    }

    /**
     * 删除商品
     *
     * @param code
     * @param planId
     */
    private void deleteProduct(String code, String planId, int position) {
        showProgress();
        String merchant_id = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchant_id);
        params.put("code", code);
        params.put("planningScheduleId", planId);
        HttpManager.getInstance().post(AppNetConfig.PLAN_PRODUCT_DELETE, params, new BaseResponse<EmptyBean>() {
            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean data) {
                dismissProgress();
                if (obj != null && obj.isSuccess()) {
                    Intent intent = new Intent(IntentCanst.ACTION_PLAN_EDIT_PRODUCT_NUM);
                    intent.putExtra("code", code);
                    intent.putExtra("num", -1);
                    intent.putExtra("from", 1);
                    LocalBroadcastManager.getInstance(getContext()).sendBroadcast(intent);
                    int index = -1;
                    for (int a = 0; a < allProductList.size(); a++) {
                        if (code.equals(allProductList.get(a).code)) {
                            index = a;
                        }
                    }
                    if (index >= 0) {
                        PlanProductInfoBean bean = allProductList.get(index);
                        allProductList.remove(index);
                        mData.remove(bean);
                        mAdapter.notifyItemRemoved(position);
                    }
                    tvTotalCount.setText("数量：" + mData.size() + "种");
                } else {
                    ToastUtils.showShort("删除失败");
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }
        });
    }

    /**
     * 修改商品的数量
     */
    private void modifyProductNum(String number, String price, int position) {
        if (mData == null || position >= mData.size() || mData.get(position) == null) {
            return;
        }
        if (TextUtils.isEmpty(number) || Integer.parseInt(number) < 1) {
            ToastUtils.showShort("登记数量应大于0");
            return;
        }
        RequestParams params = new RequestParams();
        params.put("merchantId", SpUtil.getMerchantid());
        params.put("code", mData.get(position).code);
        params.put("planningScheduleId", mData.get(position).planningScheduleId + "");
        params.put("purchaseNumber", number);
        PlanProductInfoBean bean = mData.get(position);
        if (!TextUtils.isEmpty(price)) {
            params.put("price", price);
            bean.price = price;
        } else {
            params.put("price", "-1");
            bean.price = "";
        }
        bean.purchaseNumber = Integer.parseInt(number);
        mAdapter.notifyItemChanged(position);
        HttpManager.getInstance().post(AppNetConfig.PLAN_PRODUCT_MODIFY, params, new BaseResponse<EmptyBean>() {
            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean data) {
                if (obj == null || !obj.isSuccess()) {
                    ToastUtils.showShort("修改失败");
                    getAllPlanProduct();
                } else {
                    ToastUtils.showShort("修改成功");
                    Intent intent = new Intent(IntentCanst.ACTION_PLAN_EDIT_PRODUCT_NUM);
                    intent.putExtra("code", bean.code);
                    intent.putExtra("num", bean.purchaseNumber);
                    intent.putExtra("from", 1);
                    LocalBroadcastManager.getInstance(getContext()).sendBroadcast(intent);
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                ToastUtils.showShort("修改失败");
                getAllPlanProduct();
            }
        });

    }

    /**
     * 综合排序
     */
    private void initPopPlanScreen() {

        if (mPopPlanScreen == null) {

            mPopPlanScreen = new ListFilterPopWindow();
            mPopPlanScreen.setNewData(mPopPlanScreen.getPlanScreen());
            mPopPlanScreen.setOnSelectListener(new BaseFilterPopWindow.OnSelectListener() {
                @Override
                public void getValue(SearchFilterBean show) {
                    int productStatus = PlanAllFragment.this.productStatus;
                    try {
                        productStatus = Integer.parseInt(show.id);
                    } catch (NumberFormatException e) {
                        productStatus = 0;
                    }
                    if (productStatus != PlanAllFragment.this.productStatus) {
                        List<PlanProductInfoBean> tmp = new ArrayList<>();
                        if (productStatus != 0) {

                            for (PlanProductInfoBean bean : allProductList) {

                                if (bean.productStatus != 1) {
                                    bean.productStatus = 3;
                                }

                                if (bean.productStatus == productStatus) {
                                    tmp.add(bean);
                                }

                            }
                        } else {
                            tmp = allProductList;
                        }
                        setListData(tmp);
                        PlanAllFragment.this.productStatus = productStatus;
                    }
                }

                @Override
                public void OnDismiss(String multiSelectStr) {

                }
            });
        }
    }

    @OnClick({R.id.fl_sale_product, R.id.rb_all, R.id.rb_yh, R.id.rb_qh, R.id.rb_wh})
    public void tabClick(View view) {
        int productStatus = this.productStatus;
        switch (view.getId()) {
            case R.id.fl_sale_product:
                mActivity.showFragment(2);
                break;
            case R.id.rb_all:
                productStatus = 0;
                break;
            case R.id.rb_yh:
                productStatus = 1;
                break;
            case R.id.rb_qh:
                productStatus = 2;
                break;
            case R.id.rb_wh:
                productStatus = 3;
                break;
        }
        if (productStatus != this.productStatus) {
            List<PlanProductInfoBean> tmp = new ArrayList<>();
            if (productStatus != 0) {
                for (PlanProductInfoBean bean : allProductList) {
                    if (bean.productStatus == productStatus) {
                        tmp.add(bean);
                    }
                }
            } else {
                tmp = allProductList;
            }
            setListData(tmp);
            this.productStatus = productStatus;
        }
    }

    public int search(String key) {
        List<PlanProductInfoBean> tmp = new ArrayList<>();
        if (TextUtils.isEmpty(key)) {
            tmp = allProductList;
        } else {
            for (PlanProductInfoBean bean : allProductList) {
                if ((!TextUtils.isEmpty(bean.productName) && bean.productName.contains(key)) || (!TextUtils.isEmpty(bean.manufacturer) && bean.manufacturer.contains(key)) || (!TextUtils.isEmpty(bean.zjm) && bean.zjm.contains(key.toUpperCase()))) {
                    tmp.add(bean);
                }
            }
        }
        setListData(tmp);
        return tmp.size();
    }

    public void startSearch() {
        radioFilter.setVisibility(View.GONE);
        rbAll.performClick();
    }

    public void endSearch() {
        radioFilter.setVisibility(View.VISIBLE);

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (br != null) {
            LocalBroadcastManager.getInstance(getContext()).unregisterReceiver(br);
        }
    }

    @Override
    public boolean onLicenseStatusEnable() {
        return true;
    }

    @Override
    public void handleLicenseStatusChange(int status) {
        getAllPlanProduct(searchKey);
    }
}
