package com.ybmmarket20.fragments

import android.app.Activity
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.adapter.ShopManagerAdapter
import com.ybmmarket20.bean.LoginMerchantInfo
import com.ybmmarket20.bean.SelectLoginShopInfo
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseFragment
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.AccountStatus
import com.ybmmarket20.viewmodel.AssociateShopsViewModel
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.viewmodel.AccountInfoViewModel
import com.ybmmarketkotlin.activity.LoginVertificationActivity
import kotlinx.android.synthetic.main.fragment_shop_manager.*

/**
 * 店铺管理
 */
class ShopManagerFragment: BaseFragment(), View.OnClickListener {

    private val mViewModel: AssociateShopsViewModel by viewModels()
    private val accountInfoViewModel: AccountInfoViewModel by viewModels()
    private val mList = mutableListOf<SelectLoginShopInfo>()
    private var mAdapter: ShopManagerAdapter = ShopManagerAdapter(mList)
    private var mJumpUrl: String = ""

    override fun initData(content: String?) {
        initObserver()
        initListener()
        showProgress()
        rv.layoutManager = WrapLinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
        rv.adapter = mAdapter
        mAdapter.setEnableLoadMore(true)
        mAdapter.setOnSelectListener {
            if (mList[it].editStatus == 0 && mList[it].merchantId != SpUtil.getMerchantid()) {
                //选中
//                accountInfoViewModel.login()
//                mViewModel.switchStatusToSelected(it)
            } else if (mList[it].editStatus == 2) {
                //取消关联
                cancelAssociateMerchant(it, mList[it])
            }
        }
        mAdapter.onItemClickListener =
            BaseQuickAdapter.OnItemClickListener { _, _, position ->
                val selectLoginShopInfo = mList[position]
                val loginShopState = AssociateShopsViewModel.SelectLoginShopState.getLoginShopState(
                    selectLoginShopInfo.merchantStatus,
                    selectLoginShopInfo.associateStatus,
                    selectLoginShopInfo.merchantId ?: ""
                )
                mJumpUrl = loginShopState?.getRouterUrl()?: ""
                AccountStatus.updateStatus(1)
                showProgress()
                accountInfoViewModel.getMerchantInfoWithMerchantIdUnSave(selectLoginShopInfo.merchantId?: "", SpUtil.getLoginPhone(), mJumpUrl)
            }
        mAdapter.setOnLoadMoreListener {
            mViewModel.queryAssociatedShops()
        }
        mViewModel.queryAssociatedShops()
    }

    /**
     * 删除店铺
     */
    private fun cancelAssociateMerchant(position: Int, selectLoginShopInfo: SelectLoginShopInfo) {
        if (selectLoginShopInfo.role == 1) {
            //店长
            AlertDialogEx(context).apply {
                setMessage("您为该店铺中的店长角色，暂无法取消关联，请联系客服处理")
                setCancelButton("我知道了") { _, _ -> this.dismiss() }
                show()
            }
        } else {
            //店员
            AlertDialogEx(context).apply {
                setMessage("取消关联后将无法访问该店铺，确定要取消该关联吗？")
                setCancelButton("取消") { _, _ -> }
                setConfirmButton("确定") { _, _ ->
                    showProgress()
                    selectLoginShopInfo.merchantId?.let {
                        mViewModel.cancelAssociateMerchant(it, position)
                    }
                }
                show()
            }
        }
    }

    private fun initListener() {
        tvEdit.setOnClickListener(this)
        tvOtherShop.setOnClickListener(this)
        tvFinish.setOnClickListener(this)
    }

    override fun initTitle() { }

    override fun getParams(): RequestParams = RequestParams()

    override fun getUrl(): String = ""

    override fun getLayoutId(): Int = R.layout.fragment_shop_manager

    private val launcher = this.registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
        if (it.data != null && it.data!!.getBooleanExtra(IntentCanst.LOGINVERTIFICATIONRESULT, false)) {
            processShopManagerLoginData()
        }
    }

    fun initObserver() {
        mViewModel.queryAssociateShopsLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                it.data.list?.let { it1 -> mList.addAll(it1) }
                mAdapter.notifyDataChangedAfterLoadMore(!it.data.isEnd())
            }
        }
        mViewModel.switchStatusLiveData.observe(this) {
            if (it.isSuccess) {
                mAdapter.notifyDataSetChanged()
            }
        }
        mViewModel.cancelAssociatedShopLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                val cancelAssociatedPositionBean = it.data
                if (mList[cancelAssociatedPositionBean.position].merchantId == SpUtil.getMerchantid()) {
                    //当前关联店铺
                    RoutersUtils.open("ybmpage://selectloginshop")
                    if (context is Activity) (context as Activity).finish()
                } else {
                    mList.removeAt(it.data.position)
                    mAdapter.notifyItemChanged(it.data.position)
                }
            }
        }
        accountInfoViewModel.getMerchantInfoLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                if (it.data.isCrawler) {
                    launcher.launch(LoginVertificationActivity.getStartIntent(notNullActivity, SpUtil.getLoginPhone(), SpUtil.getMerchantid()))
                } else {
                    processShopManagerLoginData()
                }
            }
        }
    }

    private fun processShopManagerLoginData() {
        if (mJumpUrl.startsWith("ybmpage://main")){
            processLoginData(notNullActivity, mJumpUrl)
        } else {
            RoutersUtils.open(mJumpUrl)
        }
    }
    override fun onClick(v: View?) {
        when(v?.id) {
            //编辑
            R.id.tvEdit -> {
                tvEdit.visibility = View.GONE
                tvOtherShop.visibility = View.GONE
                tvFinish.visibility = View.VISIBLE
                mViewModel.switchStatusToEdit()
            }
            //选择其他店铺
            R.id.tvOtherShop -> {
                RoutersUtils.open("ybmpage://linkshop?mineFrom=1")
                XyyIoUtil.trackForAccountWithMerchantId("action_managePoi_associatePoi_click")
            }
            //编辑完成
            R.id.tvFinish -> {
                tvEdit.visibility = View.VISIBLE
                tvOtherShop.visibility = View.VISIBLE
                tvFinish.visibility = View.GONE
                mViewModel.switchStatusToNormal()
            }
        }
    }
}