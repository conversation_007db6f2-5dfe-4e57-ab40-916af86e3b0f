package com.ybmmarket20.fragments;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Rect;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.TextView;

import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.WishBean;
import com.ybmmarket20.bean.WishListResult;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.LicenseStatusFragment;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.AuditStatusSyncUtil;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import butterknife.Bind;

/**
 * 药品心愿单
 */

public class WishFragment extends LicenseStatusFragment {

    @Bind(R.id.lv)
    CommonRecyclerView lv;

    private int mTab;
    private static final int WISH_FINISHED = 0;
    private static final int WISH_COLLECT = 1;
    private static final int WISH_UNFINISHED = 2;
    private static final String STATUS_COLLECT = "1";
    private static final String STATUS_FINISHED = "2";//1 搜集中 2 已完成 3 未完成
    private static final String STATUS_UNFINISHED = "3";
    private SimpleDateFormat mDateFormat;
    private YBMBaseAdapter<WishBean> mWishAdapter;
    List<WishBean> list_1 = new ArrayList<>();
    List<WishBean> list_2 = new ArrayList<>();
    List<WishBean> list_3 = new ArrayList<>();
    private WishListResult mOrderList_1;
    private WishListResult mOrderList_2;
    private WishListResult mOrderList_3;
    private int page_1 = 1;
    private int page_2 = 1;
    private int page_3 = 1;
    private int pageSize = 10;
    private int bottom = ConvertUtils.dp2px(1);

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initReceiver();
    }

    @Override
    protected void initData(String content) {
        mTab = getArguments().getInt("tab");

        mDateFormat = new SimpleDateFormat("yyyy/MM/dd", Locale.getDefault());
        mWishAdapter = new YBMBaseAdapter<WishBean>(R.layout.activity_wish_item, list_1) {

            @Override
            protected void bindItemView(YBMBaseHolder baseViewHolder, final WishBean rowsBean) {
                String format = mDateFormat.format(new Date(rowsBean.createTime));
                if (rowsBean.status == 2) {
                    if (rowsBean.product != null) {
                        baseViewHolder.setText(R.id.wish_name, rowsBean.product.showName);
                        baseViewHolder.setText(R.id.wish_specification, rowsBean.product.spec);
                        baseViewHolder.setText(R.id.wish_manufacturers, rowsBean.product.manufacturer);
                        baseViewHolder.setImageUrl(R.id.wish_iv, AppNetConfig.LORD_IMAGE + rowsBean.product.imageUrl
                                , (rowsBean.status == 2 ? R.drawable.jiazaitu : (rowsBean.status == 1
                                        ? R.drawable.jiazaitu_wish : R.drawable.jiazaitu_wish01)));
                    } else {
                        baseViewHolder.setText(R.id.wish_name, rowsBean.showName);
                        baseViewHolder.setText(R.id.wish_specification, rowsBean.spec);
                        baseViewHolder.setText(R.id.wish_manufacturers, rowsBean.manufacturer);
                        baseViewHolder.setImageUrl(R.id.wish_iv, AppNetConfig.getCDNHost() + rowsBean.url
                                , (rowsBean.status == 2 ? R.drawable.jiazaitu : (rowsBean.status == 1
                                        ? R.drawable.jiazaitu_wish : R.drawable.jiazaitu_wish01)));
                    }
                } else {
                    baseViewHolder.setText(R.id.wish_name, rowsBean.showName);
                    baseViewHolder.setText(R.id.wish_specification, rowsBean.spec);
                    baseViewHolder.setText(R.id.wish_manufacturers, rowsBean.manufacturer);
                    baseViewHolder.setImageUrl(R.id.wish_iv, AppNetConfig.getCDNHost() + rowsBean.url
                            , (rowsBean.status == 2 ? R.drawable.jiazaitu : (rowsBean.status == 1
                                    ? R.drawable.jiazaitu_wish : R.drawable.jiazaitu_wish01)));
                }
                baseViewHolder.getView(R.id.wish_btn).setEnabled(rowsBean.status == 2);
                baseViewHolder.setGone(R.id.wish_time, rowsBean.status != 2);
                TextView btn = baseViewHolder.getView(R.id.wish_btn);
                if (rowsBean.status == 2) {
                    btn.setBackgroundResource(R.color.home_back_selected);
                    btn.setText("立即购买");
                    btn.setTextColor(getResources().getColor(R.color.white));
                } else if (rowsBean.status == 1) {
                    btn.setBackgroundResource(R.color.white);
                    btn.setText("搜集中");
                    btn.setTextColor(getResources().getColor(R.color.tv_control));
                } else if (rowsBean.status == 3) {
                    btn.setBackgroundResource(R.color.white);
                    btn.setText("未完成");
                    btn.setTextColor(getResources().getColor(R.color.tv_unfinished));
                }
                baseViewHolder.setOnClickListener(R.id.wish_btn, new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (rowsBean.product != null) {
                            RoutersUtils.open("ybmpage://productdetail/" + rowsBean.product.id);
                        } else {
                            RoutersUtils.open("ybmpage://productdetail/" + rowsBean.id);
                        }
                    }
                });
                if (rowsBean.createTime > 0) {
                    baseViewHolder.setText(R.id.wish_time, "登记时间:" + format);
                } else {
                    baseViewHolder.setText(R.id.wish_time, "");
                }
                if(rowsBean.status == 2) {
                    //已完成
                    btn.setVisibility(AuditStatusSyncUtil.getInstance().isAuditFirstPassed()?View.VISIBLE:View.GONE);

                }
            }
        };
        lv.setListener(new CommonRecyclerView.Listener() {
            @Override
            public void onRefresh() {
                getWishListResult(0);
            }

            @Override
            public void onLoadMore() {
                if (mTab == WISH_FINISHED) {
                    getWishListResult(page_1);
                } else if (mTab == WISH_COLLECT) {
                    getWishListResult(page_2);
                } else if (mTab == WISH_UNFINISHED) {
                    getWishListResult(page_3);
                }
            }
        });
        lv.setEnabled(true);
        lv.setAdapter(mWishAdapter);
        switch (mTab) {
            case WISH_FINISHED:
                lv.setEmptyView(R.layout.layout_empty_view, R.drawable.icon_empty, "还没有已完成的心愿单");
                break;
            case WISH_COLLECT:
                lv.setEmptyView(R.layout.layout_empty_view, R.drawable.icon_empty, "您还未提交过心愿单\n点击下方角添加按钮，登记您想要的商品吧!");
                break;
            case WISH_UNFINISHED:
                lv.setEmptyView(R.layout.layout_empty_view, R.drawable.icon_empty, "还没有未完成的心愿");
                break;
        }
        mWishAdapter.openLoadMore(10, true);
        lv.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                outRect.bottom = bottom;
            }
        });
    }

    public static WishFragment getInstance(int tab) {
        WishFragment fragment = new WishFragment();
        fragment.setArguments(setArguments(tab));
        return fragment;
    }

    public static Bundle setArguments(int tab) {
        Bundle bundle = new Bundle();
        bundle.putInt("tab", tab);
        return bundle;
    }

    @Override
    protected void initTitle() {

    }

    @Override
    protected RequestParams getParams() {
        return null;
    }

    @Override
    protected String getUrl() {
        return null;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_wish;
    }

    /**
     * 心愿单
     *
     * @param pager 分页
     *              status => 已完成 未完成 搜集中
     *              url => "wishList/getWishList"
     */
    public void getWishListResult(final int pager) {
        if (lv == null) {
            return;
        }
        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        params.put("limit", String.valueOf(10));
        params.put("offset", String.valueOf(0));
        if (pager >= 1) {
            params.put("offset", String.valueOf(pager));
        }

        if (mTab == WISH_FINISHED) {
            params.put("status", STATUS_FINISHED);
        } else if (mTab == WISH_COLLECT) {
            params.put("status", STATUS_COLLECT);
        } else if (mTab == WISH_UNFINISHED) {
            params.put("status", STATUS_UNFINISHED);
        }
        HttpManager.getInstance().post(AppNetConfig.WISH_LIST, params, new BaseResponse<WishListResult>() {

            @Override
            public void onSuccess(String content, BaseBean<WishListResult> obj, WishListResult data) {
                if (lv == null) {
                    return;
                }
                lv.setRefreshing(false);
                if (obj != null) {
                    if (obj.isSuccess()) {

                        if (data != null) {
                            updateLicenseStatus(data.licenseStatus, pager==0?getCurrentLicenseStatusListener():null);
                            if (pager < 1) {//下拉刷新

                                page_1 = 1;
                                page_2 = 1;
                                page_3 = 1;

                                if (mTab == WISH_FINISHED) {
                                    mOrderList_1 = data;
                                    if (mOrderList_1 != null) {
                                        diffData(mOrderList_1.getRows(), list_1);
                                    }
                                    mWishAdapter.setNewData(list_1);
                                    if (mOrderList_1 != null && mOrderList_1.getRows() != null && mOrderList_1.getRows().size() < 10) {
                                        mWishAdapter.notifyDataChangedAfterLoadMore(true);
                                    }
                                } else if (mTab == WISH_COLLECT) {
                                    mOrderList_2 = data;
                                    if (mOrderList_2 != null) {
                                        diffData(mOrderList_2.getRows(), list_2);
                                    }
                                    mWishAdapter.setNewData(list_2);
                                    if (mOrderList_2 != null && mOrderList_2.getRows() != null && mOrderList_2.getRows().size() < 10) {
                                        mWishAdapter.notifyDataChangedAfterLoadMore(true);
                                    }
                                } else if (mTab == WISH_UNFINISHED) {
                                    mOrderList_3 = data;
                                    if (mOrderList_3 != null) {
                                        diffData(mOrderList_3.getRows(), list_3);
                                    }
                                    mWishAdapter.setNewData(list_3);
                                    if (mOrderList_3 != null && mOrderList_3.getRows() != null && mOrderList_3.getRows().size() < 10) {
                                        mWishAdapter.notifyDataChangedAfterLoadMore(true);
                                    }
                                }

                            } else {
                                if (mTab == WISH_FINISHED) {
                                    mOrderList_1 = data;
                                    if (mOrderList_1 != null) {
                                        int size_1 = mOrderList_1.getRows().size();

                                        for (WishBean bean : mOrderList_1.getRows()) {
                                            list_1.remove(bean);
                                        }

                                        list_1.addAll(mOrderList_1.getRows());
                                        if (size_1 >= pageSize) {
                                            page_1++;
                                        }

                                        mWishAdapter.notifyDataChangedAfterLoadMore(size_1 >= pageSize);
                                    }
                                } else if (mTab == WISH_COLLECT) {
                                    mOrderList_2 = data;
                                    if (mOrderList_2 != null) {
                                        int size_2 = mOrderList_2.getRows().size();

                                        for (WishBean bean : mOrderList_2.getRows()) {
                                            list_2.remove(bean);
                                        }

                                        list_2.addAll(mOrderList_2.getRows());
                                        if (size_2 >= pageSize) {
                                            page_2++;
                                        }

                                        mWishAdapter.notifyDataChangedAfterLoadMore(size_2 >= pageSize);
                                    }
                                } else if (mTab == WISH_UNFINISHED) {
                                    mOrderList_3 = data;
                                    if (mOrderList_3 != null) {
                                        int size_3 = mOrderList_3.getRows().size();

                                        for (WishBean bean : mOrderList_3.getRows()) {
                                            list_3.remove(bean);
                                        }

                                        list_3.addAll(mOrderList_3.getRows());
                                        if (size_3 >= pageSize) {
                                            page_3++;
                                        }

                                        mWishAdapter.notifyDataChangedAfterLoadMore(size_3 >= pageSize);
                                    }
                                }
                            }
                            completion();
                            //更新用户基本信息,心愿单列表数量
                            LocalBroadcastManager.getInstance(getNotNullActivity()).sendBroadcast(new Intent(IntentCanst.ACTION_MERCHANTBASEINFO));
                        }
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                if (lv == null) {
                    return;
                }
                completion();
            }
        });
    }

    private void diffData(List<WishBean> news, List<WishBean> old) {
        if (news == null || news.size() <= 0) {
            return;
        }

        if (old == null) {
            old = new ArrayList<>();
            old.addAll(news);
            return;
        }

        for (WishBean controlBean : news) {
            if (old.contains(controlBean)) {
                old.remove(controlBean);
            }
        }
        old.addAll(0, news);
    }

    private void completion() {
        lv.setRefreshing(false);
    }

    //动态注册广播
    private void initReceiver() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(IntentCanst.ACTION_WISHLIST);
        LocalBroadcastManager.getInstance(getNotNullActivity()).registerReceiver(mRefreshBroadcastReceiver, intentFilter);
    }

    private BroadcastReceiver mRefreshBroadcastReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (IntentCanst.ACTION_WISHLIST.equals(action)) {
                getWishListResult(0);
            }
        }
    };

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mRefreshBroadcastReceiver != null) {
            LocalBroadcastManager.getInstance(getNotNullActivity()).unregisterReceiver(mRefreshBroadcastReceiver);
        }
    }

    @Override
    public boolean onLicenseStatusEnable() {
        return true;
    }

    @Override
    public void handleLicenseStatusChange(int status) {
        getWishListResult(0);
    }
}
