package com.ybmmarket20.fragments

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.VirtualMoneyBean
import com.ybmmarket20.common.LazyFragment
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.viewmodel.VirtualMoneyViewModel
import com.ybmmarketkotlin.utils.TimeUtils

/**
 * 我的购物金
 * (全部记录、收入记录、支出记录)
 */
class MyVirtualMoneyFragment private constructor(private val virtualGoldType: String): LazyFragment() {

    private val data = arrayListOf<VirtualMoneyBean>()
    private val mViewModel: VirtualMoneyViewModel by viewModels()
    private var mOffset = 1
    var isInit = false

    companion object {
        fun getInstance(virtualGoldType: String): MyVirtualMoneyFragment {
            return MyVirtualMoneyFragment(virtualGoldType)
        }
    }

    override fun initTitle() {
    }

    override fun getParams(): RequestParams = RequestParams()

    override fun getUrl(): String = ""

    override fun getLayoutId(): Int = R.layout.fragment_my_virtual_money_tab

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val root = View.inflate(context, R.layout.fragment_my_virtual_money_tab, null)
        val noData = root.findViewById<TextView>(R.id.tv_no_data)
        val rvVirtualMoney = root.findViewById<RecyclerView>(R.id.rv_virtual_money)
        rvVirtualMoney.layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
        val adapter = MyVirtualMoneyAdapter(data)
        rvVirtualMoney.adapter = adapter
        adapter.setEnableLoadMore(true)
        adapter.setOnLoadMoreListener {
            mOffset++
            mViewModel.getVirtualMoneyList("$mOffset", "10", virtualGoldType)
        }
        mViewModel.virtualMoneyListLiveData.observe(viewLifecycleOwner, Observer {
            dismissProgress()
            if (it.isSuccess && it.data != null && it.data.rows != null) {
                if (mOffset == 1){
                    data.clear()
                }
                data.addAll(it.data.rows!!)
                if (it.data.rows!!.isNotEmpty()) {
                    adapter.notifyDataChangedAfterLoadMore(mOffset != it.data.pageCount)
                    noData.visibility = View.GONE
                } else if (mOffset == 1) {
                    noData.visibility = View.VISIBLE
                }
            }
        })

        isInit = true
        return root
    }

    fun refreshData(){
        if (isInit){
            mOffset = 1
            mViewModel.getVirtualMoneyList("$mOffset", "10", virtualGoldType)
        }
    }

    override fun loadData() {
        showProgress()
        mViewModel.getVirtualMoneyList("$mOffset", "10", virtualGoldType)
    }

    inner class MyVirtualMoneyAdapter(var list: ArrayList<VirtualMoneyBean>) :
        YBMBaseAdapter<VirtualMoneyBean>(R.layout.item_my_virtual_money, list) {
        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: VirtualMoneyBean?) {
            whenAllNotNull(baseViewHolder, t) { holder, bean ->
                val tvTitle = holder.getView<TextView>(R.id.tv_virtual_money_title)
                val tvOrderNo = holder.getView<TextView>(R.id.tv_virtual_money_order_no)
                val tvTime = holder.getView<TextView>(R.id.tv_virtual_money_time)
                val tvVirtualMoney = holder.getView<TextView>(R.id.tv_virtual_money_amount)
                tvTitle.text = bean.changeDesc
                tvTime.text = TimeUtils.getFormatTime1(bean.createTime)
                tvOrderNo.text = bean.tranNo
                val symbol = if (bean.changeType == 2) "+" else "-"
                tvVirtualMoney.text = "$symbol${UiUtils.transform(bean.virtualGold)}"
                tvVirtualMoney.setTextColor(Color.parseColor(if (bean.changeType == 2) "#FF0803" else "#00B679"))
                holder.itemView.setOnClickListener {
                    if (!bean.orderId.isNullOrEmpty()) {
                        if (bean.requestFlag == 1) {
                            "ybmpage://orderdetail?order_id=${bean.orderId}"
                        } else {
                            "ybmpage://refunddetail?orderId=${bean.orderId}"
                        }.let(RoutersUtils::open)
                    }
                }
            }
        }
    }
}