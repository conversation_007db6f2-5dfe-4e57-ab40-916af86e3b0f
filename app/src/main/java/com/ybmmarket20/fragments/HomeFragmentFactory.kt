package com.ybmmarket20.fragments

import android.os.Bundle
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.ybm.app.bean.NetError
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.adapter.GoodsListAdapter
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.ColumnProduct
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.common.*
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.AdapterUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.utils.AuditStatusSyncUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.view.cms.DynamicHomeLayoutCms
import com.ybmmarket20.view.homeview.SnapNestedSCrollView
import kotlinx.android.synthetic.main.fragment_home_activity_area.*
import org.json.JSONException
import org.json.JSONObject
import java.util.*

/**
 * <AUTHOR> Brin
 * @date : 2019/7/9 - 15:34
 * @Description :
 * @version
 */
@Deprecated("cms相关的不维护了")
class HomeFragmentFactory {
    companion object {

        var homeAreaFragment: HomeAreaFragment? = null;
        var homeRecommendFragment: HomeRecommendFragment? = null;
        var homeOftenByFragment: HomeOftenBuyFragment? = null;

        @JvmStatic
        fun get(position: Int): HomeTabFragment? {
            when (position) {
                0 -> {
                    return if (homeAreaFragment == null) {
                        homeAreaFragment = HomeAreaFragment()
                        homeAreaFragment as HomeAreaFragment
                    } else {
                        homeAreaFragment as HomeAreaFragment
                    }
                }
                1 -> {
                    return if (homeRecommendFragment == null) {
                        homeRecommendFragment = HomeRecommendFragment()
                        homeRecommendFragment as HomeRecommendFragment
                    } else {
                        homeRecommendFragment as HomeRecommendFragment
                    }
                }
                2 -> {
                    return if (homeOftenByFragment == null) {
                        homeOftenByFragment = HomeOftenBuyFragment()
                        homeOftenByFragment as HomeOftenBuyFragment
                    } else {
                        homeOftenByFragment as HomeOftenBuyFragment
                    }
                }
            }
            return homeAreaFragment
        }
    }
}

abstract class HomeTabFragment : BaseFragment() {

    open var adapter: GoodsListAdapter? = null
    open var rowsList: ArrayList<RowsBean> = arrayListOf<RowsBean>()
    var homeScrollView: SnapNestedSCrollView? = null
    var isTabLayoutOnTop: Boolean = false
    val pageSize = 10
    open var pageNum = 1
    var isRefresh = false

    open fun isInTop(): Boolean {
        // 垂直方向的偏移值
        val offset = rv_home_tab.computeVerticalScrollOffset();
        // 可视区域 - 滚动范围
        val range = rv_home_tab.computeVerticalScrollRange() - rv_home_tab.computeVerticalScrollExtent()
        if (range == 0) return true
        return offset <= 0
    }

    open fun setNestedScrollingEnabled(boolean: Boolean) {
        rv_home_tab?.isNestedScrollingEnabled = boolean
    }

    override fun initData(content: String?) {
        getListData()
    }

    open fun initView() {
        rv_home_tab?.layoutManager = WrapLinearLayoutManager(context)
        adapter = GoodsListAdapter(R.layout.item_goods, rowsList)
        adapter?.setOnListItemClickListener(object : GoodsListAdapter.OnListViewItemClickListener {
            override fun onItemClick(rows: RowsBean?) {
                // 竖向列表埋点
                val jsonObject = JSONObject()
                try {
                    jsonObject.put("id", rows?.id)
                } catch (e: JSONException) {
                    e.printStackTrace()
                }

                XyyIoUtil.track(XyyIoUtil.ACTION_HOME_PRODUCT, jsonObject,rows)

                RoutersUtils.open("ybmpage://productdetail/" + rows?.id)
            }

        })
        adapter?.openLoadMore(pageSize, true)
        adapter?.setOnLoadMoreListener(object : BaseQuickAdapter.RequestLoadMoreListener {
            override fun onLoadMoreRequested() {
                pageNum++
                getListData()
            }
        })
        rv_home_tab?.adapter = adapter
        rv_home_tab?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                // 向上滚动 dy 是正数   向下滚动 dy 是负数
                //LogUtils.e("HomeTabFragment SnapNestedSCrollView rv_home dy = $dy")
                if (dy > 0 && isTabLayoutOnTop) {

                    homeScrollView?.setNeedConsume(false)
                    //LogUtils.e("HomeTabFragment SnapNestedSCrollView rv_home isTop = $isTabLayoutOnTop")
                } else {
                    homeScrollView?.setNeedConsume(true)
                    //LogUtils.e("HomeTabFragment SnapNestedSCrollView rv_home isTop = $isTabLayoutOnTop")
                }

                if (dy < 0 && isInTop()) {
                    homeScrollView?.setNeedConsume(true)
                } else {
                    homeScrollView?.setNeedConsume(false)
                }
            }
        })
        rv_home_tab?.onFlingListener = object : RecyclerView.OnFlingListener() {
            override fun onFling(velocityX: Int, velocityY: Int): Boolean {

                // velocityY 向上为正  向下为负
                if (velocityY > 0 && isTabLayoutOnTop) {
                    homeScrollView?.setNeedConsume(false)
                } else {
                    homeScrollView?.setNeedConsume(true)
                }


                if (velocityY < 0 && isInTop()) {
                    homeScrollView?.setNeedConsume(true)
                } else {
                    homeScrollView?.setNeedConsume(false)
                }
                return false;
            }

        }
    }

    override fun getUrl(): String? {
        return null
    }

    override fun initTitle() {
        initView()
    }

    open fun refresh() {
        rv_home_tab?.scrollToPosition(0)
        isRefresh = true
    }

    open fun scrollToTop() {
        rv_home_tab?.scrollToPosition(0)
    }

    abstract fun getListData();
}

abstract class HomeTabLicenseStatusFragment : HomeTabFragment(), ILicenseStatus {
    private val licenseStatusListener: AuditStatusSyncUtil.AuditStatusSyncListener? = createListener()

    /**
     * 开启状态下添加监听
     */
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        if (onLicenseStatusEnable()) AuditStatusSyncUtil.getInstance().addLicenseStatusListener(licenseStatusListener)
    }

    /**
     * 开启状态下移除监听
     */
    override fun onDestroy() {
        if (onLicenseStatusEnable()) AuditStatusSyncUtil.getInstance().removeLicenseStatusListener(licenseStatusListener)
        super.onDestroy()
    }

    /**
     * 创建监听器
     */
    private fun createListener(): AuditStatusSyncUtil.AuditStatusSyncListener? {
        return if (onLicenseStatusEnable()) AuditStatusSyncUtil.AuditStatusSyncListener(::handleLicenseStatusChange)
        else null
    }

    /**
     * 更新审核状态
     * @param status 当前状态
     */
    protected fun updateLicenseStatus(status: Int, currentListener: AuditStatusSyncUtil.AuditStatusSyncListener?) {
        if (currentListener != null) {
            AuditStatusSyncUtil.getInstance().updateLicenseStatus(status, currentListener)
        }
    }

    /**
     * 获取当前监听器
     */
    protected fun getCurrentLicenseStatusListener(): AuditStatusSyncUtil.AuditStatusSyncListener? = licenseStatusListener
}


class HomeAreaFragment : HomeTabFragment() {

    var exhibitionId = ""
    override fun getListData() {
        if (exhibitionId.isEmpty()) {
            return
        }
        HttpManager.getInstance().post(AppNetConfig.HOME_ACTIVITYS, params, object : BaseResponse<ColumnProduct>() {
            override fun onSuccess(content: String?, baseData: BaseBean<ColumnProduct>?, data: ColumnProduct?) {
                if (baseData != null && baseData.isSuccess && data != null && data.list != null) {
                    if (isRefresh) {
                        rowsList.clear()
                        isRefresh = false
                    }

                    if (data != null && data.list.size == 10) {
                        rowsList.addAll(data.list)
                        // 请求并更新折后价
                        adapter?.let { AdapterUtils.getAfterDiscountPrice(data.list, it) }
                        adapter?.notifyDataChangedAfterLoadMore(true)
                    } else if (data != null && data.list.size < 10) {
                        rowsList.addAll(data.list)
                        // 请求并更新折后价
                        adapter?.let { AdapterUtils.getAfterDiscountPrice(data.list, it) }
                        adapter?.notifyDataChangedAfterLoadMore(false)
                    } else {
                        adapter?.notifyDataChangedAfterLoadMore(false)
                    }
                } else {
                    adapter?.notifyDataChangedAfterLoadMore(false)
                }
            }

            override fun onFailure(error: NetError?) {
                adapter?.notifyDataChangedAfterLoadMore(false)
            }
        })
    }


    override fun getParams(): RequestParams? {
        val requestParams = RequestParams()
        requestParams.put("pageNum", pageNum.toString())
        requestParams.put("pageSize", pageSize.toString())
        requestParams.put("merchantId", SpUtil.getMerchantid())
        requestParams.put("exhibitionId", exhibitionId)
        return requestParams
    }


    override fun getLayoutId(): Int {
        return R.layout.fragment_home_activity_area;
    }

    fun addHeader(homeActivityAreaHeader: DynamicHomeLayoutCms) {
        adapter?.addHeaderView(homeActivityAreaHeader)
    }

    fun setExhibionId(code: String) {
        if ("-0x00000001".equals(code)) {
            rowsList.clear()
            adapter?.notifyDataChangedAfterLoadMore(false)
        } else {
            exhibitionId = code
            pageNum = 1;
            isRefresh = true;
            getListData()
        }
    }


}

