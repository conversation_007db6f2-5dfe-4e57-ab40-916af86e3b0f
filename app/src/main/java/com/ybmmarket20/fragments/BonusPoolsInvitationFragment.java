package com.ybmmarket20.fragments;

import android.os.Bundle;

import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.BonusPoolsBean;
import com.ybmmarket20.bean.BonusPoolsListBean;
import com.ybmmarket20.common.BaseFragment;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.view.DividerLine;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;
import butterknife.ButterKnife;

public class BonusPoolsInvitationFragment extends BaseFragment {

    @Bind(R.id.lv)
    CommonRecyclerView lv;

    private YBMBaseAdapter<BonusPoolsListBean> mInvitationAdapter;
    List<BonusPoolsListBean> list = new ArrayList<>();
    private int mPage = 0;
    private int pageSize = 10;

    @Override
    protected void initData(String content) {

        mInvitationAdapter = new YBMBaseAdapter<BonusPoolsListBean>(R.layout.activity_invitation_item, list) {

            @Override
            protected void bindItemView(YBMBaseHolder baseViewHolder, final BonusPoolsListBean bean) {

                baseViewHolder.setText(R.id.tv_name, bean.realName).setText(R.id.tv_state, getState(bean.state));
            }

            public String getState(int state) {

                switch (state) {
                    default:
                        return "";
                    case 1:
                        return "已注册";
                    case 2:
                        return "已下单";
                }
            }

        };
        mInvitationAdapter.openLoadMore(10, true);
        lv.setEnabled(true);

        lv.setEmptyView(R.layout.layout_empty_view, R.drawable.icon_empty, "暂无邀请");

        lv.setListener(new CommonRecyclerView.Listener() {
            @Override
            public void onRefresh() {
                getResult(mPage = 0);
            }

            @Override
            public void onLoadMore() {
                getResult(mPage);
            }
        });

        lv.setAdapter(mInvitationAdapter);

        DividerLine divider = new DividerLine(DividerLine.VERTICAL);
        divider.setSize(1);
        divider.setColor(0xffeeeeee);
        lv.addItemDecoration(divider);
    }

    private void getResult(int page) {

        String merchantId = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        params.put("pageSize", String.valueOf(pageSize));
        params.put("pageNum", String.valueOf(String.valueOf(page)));
        HttpManager.getInstance().post(AppNetConfig.BONUS_POOLS, params, new BaseResponse<BonusPoolsBean>() {

            @Override
            public void onSuccess(String content, BaseBean<BonusPoolsBean> obj, BonusPoolsBean data) {
                dismissProgress();

                lv.setRefreshing(false);
                if (obj != null) {
                    if (obj.isSuccess()) {
                        if (data != null) {
                            if (data.data != null) {

                                if (data.data.size() > 0) {
                                    if (page <= 0) {
                                        BonusPoolsInvitationFragment.this.mPage = 1;
                                    } else {
                                        BonusPoolsInvitationFragment.this.mPage++;
                                    }
                                }

                                if (page <= 0) {
                                    if (list == null) {
                                        list = new ArrayList<>();
                                    }
                                    list.clear();
                                    if (list.size() <= 0 && data.data != null) {
                                        list.addAll(data.data);
                                    } else {
                                        if (data.data == null || data.data.isEmpty()) {

                                        } else {
                                            for (BonusPoolsListBean bean : data.data) {
                                                if (list.contains(bean)) {
                                                    list.remove(bean);
                                                }
                                            }
                                            list.addAll(0, data.data);
                                        }
                                    }
                                    mInvitationAdapter.setNewData(list);
                                    mInvitationAdapter.notifyDataChangedAfterLoadMore(list.size() >= pageSize);
                                } else {
                                    if (data.data == null || data.data.size() <= 0) {
                                        mInvitationAdapter.notifyDataChangedAfterLoadMore(false);
                                    } else {

                                        for (BonusPoolsListBean bean : data.data) {
                                            if (list.contains(bean)) {
                                                list.remove(bean);
                                            }
                                        }
                                        list.addAll(data.data);
                                        mInvitationAdapter.setNewData(list);
                                        mInvitationAdapter.notifyDataChangedAfterLoadMore(data.data.size() >= pageSize);
                                    }
                                }
                            } else {
                                mInvitationAdapter.notifyDataChangedAfterLoadMore(false);
                            }
                        }
                    } else {
                        mInvitationAdapter.setNewData(list);
                    }
                } else {
                    mInvitationAdapter.notifyDataChangedAfterLoadMore(false);
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                ToastUtils.showShort(error.message);
                lv.setRefreshing(false);
                mInvitationAdapter.notifyDataChangedAfterLoadMore(false);
            }
        });

    }

    @Override
    protected void initTitle() {

    }

    @Override
    protected RequestParams getParams() {
        return null;
    }

    @Override
    protected String getUrl() {
        return null;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_bonus_pools_invitation;

    }

    public static BonusPoolsInvitationFragment getInstance(int tab) {
        BonusPoolsInvitationFragment fragment = new BonusPoolsInvitationFragment();
        fragment.setArguments(setArguments(tab));
        return fragment;
    }

    public static Bundle setArguments(int tab) {
        Bundle bundle = new Bundle();
        bundle.putInt("tab", tab);
        return bundle;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        ButterKnife.unbind(this);
    }
}
