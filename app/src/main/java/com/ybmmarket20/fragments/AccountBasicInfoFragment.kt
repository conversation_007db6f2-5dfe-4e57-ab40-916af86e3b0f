package com.ybmmarket20.fragments

import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.bean.AptitudeBasicInfoBean
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.common.BaseFragment
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.SpUtil
import kotlinx.android.synthetic.main.fragment_account_basic_info.*

class AccountBasicInfoFragment : BaseFragment() {
    override fun initData(content: String?) {
        getBasicInfoData()
//        val tabIndex=arguments?.get(EXTRA_BASIC_INFO_TABINDEX)
//        when(tabIndex){
//            1->{//基本信息
//                //ll_invoice_info.visibility= View.GONE
//            }
//            2->{//发票信息
//               // ll_basic_info.visibility=View.GONE
//            }
//        }
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_account_basic_info
    }

    private fun getBasicInfoData() {
        //showProgress()
        val merchantId = SpUtil.getMerchantid()
        val params = RequestParams().apply {
            put("merchantId", merchantId)
        }
        HttpManager.getInstance().post(AppNetConfig.QUERY_LICENSE_AUDIT_BASICINFO, params, object : BaseResponse<AptitudeBasicInfoBean?>() {
            override fun onSuccess(content: String?, baseBean: BaseBean<AptitudeBasicInfoBean?>?, data: AptitudeBasicInfoBean?) {
                super.onSuccess(content, baseBean, data)
                // dismissProgress()
                if (baseBean != null && baseBean.isSuccess && data != null) {
                    val basicInfo = data.info
                    tv_company_name?.text = basicInfo?.realName
                    tv_invoice_company_name?.text = basicInfo?.realName
                    tv_company_type?.text = basicInfo?.customerTypeName
                    tv_company_address?.text = basicInfo?.registerAddress
                    tv_business_scope?.text=basicInfo?.scopeOfExperience
                    tv_invoice_company_name?.text = basicInfo?.realName
                    tv_invoice_type?.text=basicInfo?.invoiceName
                }
            }

            override fun onFailure(error: NetError) {
                // dismissProgress()
            }
        })
    }

    override fun initTitle() {

    }

    override fun getParams(): RequestParams? {
        return null
    }

    override fun getUrl(): String {
        return "";
    }

}