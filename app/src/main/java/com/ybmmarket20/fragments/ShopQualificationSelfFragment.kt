package com.ybmmarket20.fragments

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.DividerItemDecoration
import com.luck.picture.lib.tools.ScreenUtils
import com.ybm.app.bean.NetError
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.activity.AptitudeXyyPdfActivity
import com.ybmmarket20.adapter.ShopQualificationAdapter
import com.ybmmarket20.bean.AptitudePdfUrlBean
import com.ybmmarket20.bean.AptitudeXyyBean
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.view.CORNER_TOP
import com.ybmmarket20.view.ShopQualificationItemView
import com.ybmmarket20.viewmodel.ShopQualificationViewModel
import kotlinx.android.synthetic.main.fragment_shop_qualication.view.*

const val IDS_ALL = "0"
const val FLAG_ONE = "1"
const val FLAG_ALL = "2"

/**
 * 自营店铺-店铺资质
 */
open class ShopQualificationSelfFragment : LoadingFragment(), View.OnClickListener {

    var orgId: String? = null
    var shopCode: String? = null
    var mRootView: View? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val rootView = View.inflate(context, R.layout.fragment_shop_qualication, null)
        mRootView = rootView
        orgId = arguments?.getString("orgId")
        shopCode = arguments?.getString("shopCode")
        val viewModel = ViewModelProvider(this).get(ShopQualificationViewModel::class.java)
        viewModel.initData(arguments)
        viewModel.shopQualificationViewModel.observe(viewLifecycleOwner, Observer {
            dismissProgress()
            if (it.data !=null && it.isSuccess) {
                rootView.rv.layoutManager = WrapLinearLayoutManager(context)
                val adapter = ShopQualificationAdapter(it.data.rows, it.data.isSelf).apply {
                    setOnItemClickListener { adapter, view, position ->
                        if (isSelf) {
                            val url = "ybmpage://aptitudexyypdf?&title=${it.data.rows[position].name}&contractId=${it.data.rows[position].id}&shopCode=${it.data.shopCode?: ""}&qualificationName=${it.data.rows[position].qualificationName?: ""}&qualificationCode=${it.data.rows[position].qualificationCode?: ""}"
                            RoutersUtils.open(url)
                        } else {
                            context?.startActivity(
                                Intent(context, AptitudeXyyPdfActivity::class.java).apply {
                                    putStringArrayListExtra("links", it.data.rows[position].links as ArrayList<String>?)
                                    putExtra("title", it.data.rows[position].name)
                                    putExtra("orgId", orgId)
                                    putExtra("contractId", it.data.rows[position].id)
                                    putExtra("qualificationName", it.data.rows[position].qualificationName?: "")
                                    putExtra("qualificationCode", it.data.rows[position].qualificationCode?: "")
                                })
                        }
                    }
                }
                //设置头部
                handleHeaderView(it.data, adapter)
                rootView.rv.adapter = adapter
                if (it.data.rows == null || it.data.rows.isEmpty()) {
                    showEmpty()
                } else {
                    hiddenEmpty()
                }
            } else if (rootView.rv.adapter == null) {
                showEmpty()
            }
            val divider = DividerItemDecoration(context, DividerItemDecoration.VERTICAL)
            context?.let { c -> ContextCompat.getDrawable(c, R.drawable.shape_shop_qualication_divider)?.let(divider::setDrawable) }
            rootView.rv.addItemDecoration(divider)
        })
        showProgress()
        getListData(viewModel)
        rootView.tv_download_to_email.setOnClickListener(this)
        return rootView
    }

    /**
     * 设置头部
     */
    private fun handleHeaderView(bean: AptitudeXyyBean, adapter: ShopQualificationAdapter) {
        if (context == null) return
        if (!bean.corporationItemList.isNullOrEmpty()) {
            bean.corporationItemList?.let {
                val corporationHeaderView = ShopQualificationItemView(requireContext())
                corporationHeaderView.setTitle("企业信息")
                corporationHeaderView.setContent(it)
                corporationHeaderView.setViewMarginTop(ScreenUtils.dip2px(context, 10f))
                adapter.addHeaderView(corporationHeaderView)
            }
        }
        if (!bean.shopNoticeItemList.isNullOrEmpty()) {
            bean.shopNoticeItemList?.let {
                val shopNoticeHeaderView = ShopQualificationItemView(requireContext())
                shopNoticeHeaderView.setTitle("服务介绍")
                shopNoticeHeaderView.setContent(it)
                shopNoticeHeaderView.setViewMarginTop(ScreenUtils.dip2px(context, 10f))
                adapter.addHeaderView(shopNoticeHeaderView)
            }
        }

        if (bean.rows != null && bean.rows.isNotEmpty()) {
            val rowsHeaderView = ShopQualificationItemView(requireContext())
            rowsHeaderView.hiddenDivider()
            rowsHeaderView.setTitle("企业资质", CORNER_TOP)
            rowsHeaderView.setViewMarginTop(ScreenUtils.dip2px(context, 10f))
            adapter.addHeaderView(rowsHeaderView)
        }
    }


    /**
     * 获取列表数据
     */
    open fun getListData(viewModel: ShopQualificationViewModel) {
        viewModel.getSelfQualification()
    }

    /**
     * 切换到空页面
     */
    fun showEmpty() {
        mRootView?.emptyView?.visibility = View.VISIBLE
        mRootView?.rv?.visibility = View.GONE
    }

    /**
     * 切换到列表
     */
    private fun hiddenEmpty() {
        mRootView?.emptyView?.visibility = View.GONE
        mRootView?.rv?.visibility = View.VISIBLE
    }

    override fun onClick(v: View?) {
        if (v?.id == R.id.tv_download_to_email) {

            if (!TextUtils.isEmpty(orgId)) {
                getAptitudePopZipUrl()
            } else {
                var routerStr = "ybmpage://aptitudexyyemail?flag=$FLAG_ALL&contractId=$IDS_ALL"
                shopCode?.let {
                    routerStr += "&shopCode=$it"
                }
                routerStr.let(RoutersUtils::open)
            }

        }
    }


    private fun getAptitudePopZipUrl() { //获取pop资质zip地址
        showProgress()
        val params = RequestParams()
        params.put("orgId", orgId)
        params.put("contractId", IDS_ALL)
        params.url = AppNetConfig.APTITUDE_XYY_ZIP_POP_DOWN_URL
        HttpManager.getInstance().post(params, object : BaseResponse<AptitudePdfUrlBean?>() {
            override fun onFailure(error: NetError) {
                dismissProgress()
            }

            override fun onSuccess(content: String?, baseBean: BaseBean<AptitudePdfUrlBean?>?, data: AptitudePdfUrlBean?) {
                dismissProgress()
                if (baseBean != null && baseBean.isSuccess) {
                    data?.url?.let {
                        RoutersUtils.open("ybmpage://aptitudexyyemail?zipurl=${data.url}")
                    }
                }
            }
        })
    }
}