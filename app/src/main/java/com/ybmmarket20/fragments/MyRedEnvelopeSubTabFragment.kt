package com.ybmmarket20.fragments

import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import androidx.fragment.app.Fragment
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.IRedEnvelopeBean
import com.ybmmarket20.bean.RedEnvelopeSubItem
import com.ybmmarket20.utils.StringUtil
import com.ybmmarket20.utils.UiUtils
import java.math.BigDecimal
import java.text.DecimalFormat

/**
 * 我的红包(可使用,已用完,已失效)
 */
class MyRedEnvelopeSubTabFragment(val redEnvelopeType: Int) :
    BaseRedEnvelopeSubTabFragment() {

    var mAdapter: YBMBaseAdapter<IRedEnvelopeBean>? = null

    override fun init() {
        getRedEnvelopeAdapter().setOnLoadMoreListener { getRedEnvelopeList("$redEnvelopeType") }
    }

    override fun getRedEnvelopeAdapter(): YBMBaseAdapter<IRedEnvelopeBean> {
        if (mAdapter == null) mAdapter = MyRedEnvelopeSubTabAdapter(mList)
        return mAdapter!!
    }

    override fun loadData() {
        getRedEnvelopeList("$redEnvelopeType")
    }


    companion object {
        fun getInstance(redEnvelopeType: Int): Fragment =
            MyRedEnvelopeSubTabFragment(redEnvelopeType)
    }

    inner class MyRedEnvelopeSubTabAdapter(var list: ArrayList<IRedEnvelopeBean>) :
        YBMBaseAdapter<IRedEnvelopeBean>(R.layout.item_red_envelope_sub_tab, list) {

        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: IRedEnvelopeBean?) {
            if (baseViewHolder != null && t != null && t is RedEnvelopeSubItem) {
                getMyRedEnvelopeSubTabItem(redEnvelopeType).onHandle(baseViewHolder, t)
            }
        }
    }

    fun getMyRedEnvelopeSubTabItem(redEnvelopeType: Int): MyRedEnvelopeSubTabItem {
        return when (redEnvelopeType) {
            1 -> MyRedEnvelopeSubTabItem.MyRedEnvelopeSubTabItemCanUse
            2 -> MyRedEnvelopeSubTabItem.MyRedEnvelopeSubTabItemUsedUp
            else -> MyRedEnvelopeSubTabItem.MyRedEnvelopeSubTabItemNotUsed
        }
    }
}

abstract class MyRedEnvelopeSubTabItem {
    abstract fun onHandle(holder: YBMBaseHolder, bean: RedEnvelopeSubItem)

    fun onBaseHandle(holder: YBMBaseHolder, bean: RedEnvelopeSubItem){
        holder.setText(R.id.tv_red_envelope_title, bean.templateName)
        holder.setText(R.id.tv_red_envelope_time, "${bean.validDateStr}-${bean.expireDateStr}")
        holder.setText(R.id.tv_red_envelope_remain, "剩余￥${bean.canUseMoney}")
        holder.setVisible(R.id.tv_red_envelope_remain, false)
        val builder = SpannableStringBuilder("￥").let {
            it.setSpan(AbsoluteSizeSpan(12, true), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            var tempStr = UiUtils.transform(bean.originMoney)
//            val originMoney: String = if (null != tempStr && tempStr.indexOf(".") > 0) {
//                tempStr = tempStr.replace("0+?\$".toRegex(), "")
//                tempStr = tempStr.replace("[.]\$".toRegex(), "")
//                tempStr
//            } else ""
//            it.append(originMoney)
            it.append(StringUtil.removeZeroAfterDot(tempStr))
        }
        holder.setText(R.id.tv_red_envelope_amount, builder)
    }
    //可使用
    object MyRedEnvelopeSubTabItemCanUse : MyRedEnvelopeSubTabItem() {
        override fun onHandle(holder: YBMBaseHolder, bean: RedEnvelopeSubItem) {
            onBaseHandle(holder, bean)
            holder.setTextColor(R.id.tv_red_envelope_title, Color.parseColor("#B54A02"))
            holder.setTextColor(R.id.tv_red_envelope_time, Color.parseColor("#D47F47"))
            holder.setTextColor(R.id.tv_red_envelope_amount, Color.parseColor("#FF0803"))
            holder.setVisible(R.id.v_red_envelope_divider, true)
            holder.setVisibleOrInvisible(R.id.iv_red_envelope_status, false)
            holder.itemView.setBackgroundResource(R.drawable.shape_red_envelope_all)
            holder.setImageResource(R.id.iv_red_envelope, R.drawable.icon_red_envelope_red)
            holder.setVisible(R.id.tv_red_envelope_remain, bean.canUseMoney != 0f && bean.usedMoney > 0f)
            holder.setTextColor(R.id.tv_red_envelope_remain, Color.parseColor("#C34F02"))
        }
    }

    //已用完
    object MyRedEnvelopeSubTabItemUsedUp : MyRedEnvelopeSubTabItem() {
        override fun onHandle(holder: YBMBaseHolder, bean: RedEnvelopeSubItem) {
            onBaseHandle(holder, bean)
            holder.setTextColor(R.id.tv_red_envelope_title, Color.parseColor("#B1B1B1"))
            holder.setTextColor(R.id.tv_red_envelope_time, Color.parseColor("#B1B1B1"))
            holder.setTextColor(R.id.tv_red_envelope_amount, Color.parseColor("#9E9E9E"))
            holder.setVisible(R.id.v_red_envelope_divider, false)
            holder.setVisibleOrInvisible(R.id.iv_red_envelope_status, true)
            holder.setImageResource(R.id.iv_red_envelope_status, R.drawable.icon_red_envelope_status_used_up)
            holder.itemView.setBackgroundResource(R.drawable.shape_red_envelope_used_up)
            holder.setImageResource(R.id.iv_red_envelope, R.drawable.icon_red_envelope_gray)
        }
    }

    //已用完
    object MyRedEnvelopeSubTabItemNotUsed : MyRedEnvelopeSubTabItem() {
        override fun onHandle(holder: YBMBaseHolder, bean: RedEnvelopeSubItem) {
            MyRedEnvelopeSubTabItemUsedUp.onHandle(holder, bean)
            holder.setImageResource(R.id.iv_red_envelope_status, R.drawable.icon_red_envelope_status_not_used)
        }
    }
}