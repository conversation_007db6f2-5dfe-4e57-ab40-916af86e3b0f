package com.ybmmarket20.fragments

import android.graphics.Rect
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.View
import com.google.gson.reflect.TypeToken
import com.luck.picture.lib.tools.ScreenUtils
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.view.WrapGridLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.adapter.GoodsListAdapter
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RefreshWrapperPagerBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.common.RefreshWrapperFragment
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.flowDataPageCommoditySearch
import kotlinx.android.synthetic.main.fragment_coupon.crv_refresh_common
import kotlinx.android.synthetic.main.fragment_home_subject.*
import java.lang.reflect.Type
import java.text.SimpleDateFormat
import java.util.*

/**
 * <AUTHOR>
 * @date 2020-05-12
 * @description 专题页fragment
 */
class SubjectFragment: RefreshWrapperFragment<RowsBean>() {

    var strategyTypeId: String? = null
    var strategyCategoryId: String? = null
    var strategyCategoryType: String? = null
    var majorTitle: String? = null
    var minorTitle: String? = null
    var adapter: GoodsListAdapter? = null
    var gridLayoutManager: WrapGridLayoutManager? = null
    var isLoadData = false
    var bgHeight: Float = 0f
    var dyTotal: Float = 0f

    override fun initData(content: String?) {
        super.initData(content)
        showProgress()
        strategyTypeId = arguments?.getString("strategyTypeId")
        strategyCategoryId = arguments?.getString("strategyCategoryId")
        strategyCategoryType = arguments?.getString("strategyCategoryType")
        majorTitle = arguments?.getString("majorTitle")
        minorTitle = arguments?.getString("minorTitle")
        gridLayoutManager = WrapGridLayoutManager(context, 2)
        crv_refresh_common.layoutManager = gridLayoutManager
        crv_refresh_common.addItemDecoration(SubjectItemDecoration())
        gridLayoutManager?.spanSizeLookup = object: GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                return if(position == rows.size) {
                    return gridLayoutManager?.spanCount?:1
                } else 1
            }
        }
        (getAdapter(rows) as GoodsListAdapter).setOnListItemClickListener {
            if(it != null) {
                openUrl("ybmpage://productdetail?${IntentCanst.PRODUCTID}=${it.id}")
            }
        }
        setBackground(majorTitle?: "")
        crv_refresh_common.recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener(){
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                //处理背景跟随
                dyTotal += dy
                if(-dyTotal < bgHeight) {
                    iv_subject_bottom.scrollTo(0, dyTotal.toInt())
                }
            }
        })
    }

    private fun setBackground(title: String) {
        when(title) {
            "每日特惠" -> {
                iv_subject_bottom.setImageResource(R.drawable.icon_subject_daily_discount_bottom)
            }
            "新品首推" -> {
                iv_subject_bottom.setImageResource(R.drawable.icon_subject_new_recommend_bottom)
            }
            "高毛专区" -> {
                iv_subject_bottom.setImageResource(R.drawable.icon_subject_gross_bottom)
            }
            else -> {
                iv_subject_bottom.setImageResource(R.drawable.icon_subject_gross_bottom)
            }
        }

        val screenWidth = ScreenUtils.getScreenWidth(context)
        val bottomRat =  375f / 73f
        val bottomHeight = screenWidth/ bottomRat
        val bottomLp: ConstraintLayout.LayoutParams = iv_subject_bottom.layoutParams as ConstraintLayout.LayoutParams
        bottomLp.height = bottomHeight.toInt()
        bottomLp.width = screenWidth
        iv_subject_bottom.layoutParams = bottomLp
    }

    override fun getRequestParams(): RequestParams = RequestParams().also {
        it.put("strategyTypeId", strategyTypeId)
        it.put("strategyCategoryId", strategyCategoryId)
        it.put("strategyCategoryType", strategyCategoryType)
    }

    override fun getAdapter(rows: MutableList<RowsBean>?): YBMBaseAdapter<RowsBean> {
        if(adapter == null) {
            adapter = GoodsListAdapter(R.layout.detail_gridview_item, rows)
        }
        return adapter as GoodsListAdapter
    }

    override fun getEmptyMsg(): String {
        return "暂无相关数据"
    }
    override fun getUrl(): String = if (isKaUser)AppNetConfig.KA_HOME_SUBJECT_LIST else AppNetConfig.HOME_SUBJECT_LIST

    override fun getType(): Type = object : TypeToken<BaseBean<RefreshWrapperPagerBean<RowsBean>>>() {}.type

    override fun getLayoutId(): Int = R.layout.fragment_home_subject

    override fun getStartPage(): Int = 0

    override fun onResponseSuccess(content: String?, obj: BaseBean<RefreshWrapperPagerBean<RowsBean>>?, t: RefreshWrapperPagerBean<RowsBean>?) {
        super.onResponseSuccess(content, obj, t)
        t?.let {
            updateLicenseStatus(t.licenseStatus, getCurrentLicenseStatusListener())
            if(!isLoadData) {
                isLoadData = true
                try {
                    mFlowData.let {
                        it.spType = "-888"
                        it.spId = "${majorTitle}_${minorTitle}"
                        it.sId = getSId()
                    }
                    ((getAdapter(rows) as GoodsListAdapter)).setFlowData(mFlowData)
                    flowDataPageCommoditySearch(mFlowData)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    private fun getSId(): String {
        val strDateFormat = "yyyyMMdd"
        val sdf = SimpleDateFormat(strDateFormat)
        return "${sdf.format(Date())}-${SpUtil.getMerchantid()}-LUCKC-1"
    }

    override fun onLicenseStatusEnable(): Boolean = true

    override fun handleLicenseStatusChange(status: Int) {
        super.handleLicenseStatusChange(status)
        getAdapter(rows).notifyDataSetChanged()
    }
}


/**
 * 列表网格分割线
 */
class SubjectItemDecoration : RecyclerView.ItemDecoration() {
    private val space = ConvertUtils.dp2px(1f)

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        outRect.bottom = 0
        if (parent.getChildLayoutPosition(view) % 2 == 0) {
            outRect.left = space * 8
            outRect.right = space * 4
        } else {
            outRect.left = space * 4
            outRect.right = space * 8
        }

        if (parent.getChildLayoutPosition(view) > 1) {
            outRect.top = space * 8
        } else {
            outRect.top = 0
        }
    }
}