package com.ybmmarket20.fragments;

import android.view.View;
import android.widget.RelativeLayout;

import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybm.app.view.refresh.RecyclerRefreshLayout;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.GoodsListAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.bean.RowsListBeanWrapper;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.LicenseStatusFragment;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.AdapterUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.analysis.AnalysisConst;
import com.ybmmarket20.utils.analysis.FlowDataAnalysisManagerKt;
import com.ybmmarket20.utils.analysis.FlowDataEventAnalysisKt;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;


/**
 * 发现 -- 常购清单
 */
public class DiscoverTabOftenBuyFragment extends LicenseStatusFragment implements RecyclerRefreshLayout.OnRefreshListener, GoodsListAdapter.OnListViewItemClickListener, CommonRecyclerView.Listener, GoodsListAdapter.OnDeleteItemClickCallback {

    @Bind(R.id.rv_discover_tab)
    CommonRecyclerView mRecyclerView;
    @Bind(R.id.ll_title)
    RelativeLayout ll_title;

    private GoodsListAdapter mAdapter;
    private List<RowsBean> data = new ArrayList<>();

    private final int PAGE_START_POSITION = 0;
    private final int PAGE_SIZE = 10;
    private int pageIndex = PAGE_START_POSITION;
    private boolean isCancleDelete = true;
    //是否已经接收到埋点参数
    private boolean isReceivedAnalysis = false;

    public static DiscoverTabOftenBuyFragment getInstance() {
        DiscoverTabOftenBuyFragment fragment = new DiscoverTabOftenBuyFragment();
        return fragment;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_discover_tab;
    }

    @Override
    protected void initTitle() {

    }

    @Override
    protected void initData(String content) {

        mAdapter = new GoodsListAdapter(R.layout.item_goods, data);
        mAdapter.setOnListItemClickListener(this);
        mAdapter.setIsShowSalesQuantity(false);
        mAdapter.openLoadMore(PAGE_SIZE, true);
        mAdapter.setIsCanDelete(isCancleDelete);
//        mAdapter.setOnDeleteItemClickCallback(this);
        mRecyclerView.setListener(this);
        mRecyclerView.setLoadMoreEnable(true);
        mRecyclerView.setEmptyView(R.layout.layout_empty_view, R.drawable.no_join_goods, "清单中暂无商品，赶快加购吧");
        mRecyclerView.setAdapter(mAdapter);
        if (!isCancleDelete) ll_title.setVisibility(View.VISIBLE);
//        showProgress("加载中...");
//        requestData(PAGE_START_POSITION);
    }

    private void requestData(int page) {

        RequestParams params = new RequestParams();
        params.put("merchantId", SpUtil.getMerchantid());//商户编号  string
        params.put("offset", String.valueOf(page));
        params.put("limit", String.valueOf(PAGE_SIZE));
        params.put("spFrom", AnalysisConst.FlowDataChain.FLOWDATACHAIN_FROM_FIND_OFTEN_BUY_LIST);
        if (isReceivedAnalysis) {
            FlowDataAnalysisManagerKt.addAnalysisRequestParams(params, mFlowData);
        }
        HttpManager.getInstance().post(AppNetConfig.FIND_MY_ORDER_LIST, params, new BaseResponse<RowsListBeanWrapper>() {
            @Override
            public void onSuccess(String content, BaseBean<RowsListBeanWrapper> obj, RowsListBeanWrapper rowsListBean) {
                dismissProgress();
                mRecyclerView.setRefreshing(false);
                if (rowsListBean != null && rowsListBean.pageData != null && rowsListBean.pageData.rows != null) {
                    updateLicenseStatus(rowsListBean.licenseStatus, page == PAGE_START_POSITION ? getCurrentLicenseStatusListener() : null);
                    if (!isReceivedAnalysis && rowsListBean.pageData != null) {
                        FlowDataAnalysisManagerKt.updateFlowData(mFlowData, rowsListBean.sptype, rowsListBean.spid, rowsListBean.sid, null);
                        FlowDataEventAnalysisKt.flowDataPageCommoditySearch(mFlowData);
                        mAdapter.setFlowData(mFlowData);
                        isReceivedAnalysis = true;
                    }
                    pageIndex = page;
                    if (page == PAGE_START_POSITION) {
                        //是刷新数据
                        data.clear();
                        data.addAll(rowsListBean.pageData.rows);
                        mAdapter.notifyDataChangedAfterLoadMore(rowsListBean.pageData.rows.size() >= PAGE_SIZE);
                    } else {
                        data.addAll(rowsListBean.pageData.rows);
                        mAdapter.notifyDataChangedAfterLoadMore(rowsListBean.pageData.rows.size() >= PAGE_SIZE);
                    }
                    // 请求并更新折后价
                    AdapterUtils.INSTANCE.getAfterDiscountPrice(rowsListBean.pageData.rows, mAdapter);
                } else {
                    failureHandle(page == PAGE_START_POSITION);
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                failureHandle(page == PAGE_START_POSITION);
            }
        });
    }

    private void failureHandle(boolean isRefresh) {
        if (isRefresh) {
            mRecyclerView.setRefreshing(false);
        } else {
            mAdapter.notifyDataChangedAfterLoadMore(false);
        }
    }

    @Override
    protected RequestParams getParams() {
        return null;
    }

    @Override
    protected String getUrl() {
        return null;
    }


    @Override
    public void onRefresh() {
        requestData(PAGE_START_POSITION);
    }

    @Override
    public void onLoadMore() {
        requestData(pageIndex + 1);
    }


    @Override
    public void onItemClick(RowsBean rows) {
        if (rows != null) {
            openUrl("ybmpage://productdetail/" + rows.getId());
        }
    }

    @Override
    public void onDeleteItemClick(int position) {
        if (position >= 0 && position < data.size()) {
            deleteOftenBuyGoods(data.get(position), position);
        }
    }


    /**
     * 删除常购商品
     *
     * @param rowsBean
     */
    private void deleteOftenBuyGoods(RowsBean rowsBean, int position) {
        showProgress();
        RequestParams params = new RequestParams();
        String merchantId = SpUtil.getMerchantid();
        params.put("merchantId", merchantId);
        params.put("skuId", String.valueOf(rowsBean.getId()));
        HttpManager.getInstance().post(AppNetConfig.CANCEL_ORDER_SELL_NO, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean baseBean) {
                dismissProgress();
                if (null != obj && obj.isSuccess()) {
                    mAdapter.remove(position);
                    //DialogUtil.showCommonStatus(CommonDialogLayout.CommonTip.success, "删除成功");
                }
            }

            @Override
            public void onFailure(NetError error) {
                super.onFailure(error);
                dismissProgress();
            }
        });
    }

    @Override
    public boolean onLicenseStatusEnable() {
        return true;
    }

    @Override
    public void handleLicenseStatusChange(int status) {
        requestData(PAGE_START_POSITION);
    }

    public void showTitle() {
        isCancleDelete = false;
    }


}
