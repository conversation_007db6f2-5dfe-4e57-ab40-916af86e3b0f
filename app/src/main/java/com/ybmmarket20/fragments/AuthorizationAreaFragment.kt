package com.ybmmarket20.fragments

import android.util.Log
import com.google.gson.reflect.TypeToken
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybmmarket20.R
import com.ybmmarket20.activity.AUTHORIZATION_AREA_ALL
import com.ybmmarket20.activity.AUTHORIZATION_AREA_AUTHORIZATIONED
import com.ybmmarket20.activity.AUTHORIZATION_AREA_UNAUTHORIZATION
import com.ybmmarket20.adapter.AuthorizationAreaAdapter
import com.ybmmarket20.bean.AuthorizationAreaBean
import com.ybmmarket20.bean.AuthorizationAreaRowBean
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.common.RefreshFragment
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.eventbus.Event
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst.RX_BUS_AGENT_ORDER_AUTHORIZATION_READ_FLAG
import com.ybmmarket20.constant.IntentCanst.RX_BUS_AGENT_ORDER_AUTHORIZATION_STATUS
import java.lang.reflect.Type

/**
 * 代下单授权Fragment
 * <AUTHOR>
 * 2019-10-14
 */

const val AGENT_ORDER_AUTHORIZATION_FLAG_UNREAD: Int = 0
const val AGENT_ORDER_AUTHORIZATION_FLAG_READ: Int = 1

class AuthorizationAreaFragment : RefreshFragment<AuthorizationAreaRowBean, AuthorizationAreaBean<AuthorizationAreaRowBean>>() {

    var adapter: AuthorizationAreaAdapter? = null
    var authorizationCategory = 0

    override fun initData(content: String?) {
        super.initData(content)
        authorizationCategory = arguments?.getInt("authorizationCategory") ?: 0
        Log.i("authorizationCategory", "$authorizationCategory")
    }

    override fun getLayoutId(): Int = R.layout.fragment_authorization_area

    override fun getTitle(): String {
        return when (arguments?.getInt("authorizationCategory")) {
            AUTHORIZATION_AREA_ALL -> "全部"
            AUTHORIZATION_AREA_AUTHORIZATIONED -> "已授权"
            else -> "未授权"
        }
    }

    override fun getRequestParams(): RequestParams = RequestParams().apply { put("status", "${arguments?.getInt("authorizationCategory")}") }

    override fun getAdapter(rows: List<AuthorizationAreaRowBean>?): YBMBaseAdapter<AuthorizationAreaRowBean>? {
        if (adapter == null) {
            adapter = AuthorizationAreaAdapter(R.layout.item_authorization_order, rows)
        }
        return adapter
    }

    override fun getUrl(): String = AppNetConfig.AGENT_ORDER_AUTHORIZATION_LIST

    override fun getType(): Type = object : TypeToken<BaseBean<AuthorizationAreaBean<AuthorizationAreaRowBean>>>() {}.type

    /**
     * 更新为已读状态
     */
    override fun receiveEvent(event: Event<*>?) {
        super.receiveEvent(event)
        event?.also {
            when(it.code) {
                //处理已读状态
                RX_BUS_AGENT_ORDER_AUTHORIZATION_READ_FLAG -> {
                    if (rows != null) {
                        rows.map { bean ->
                            if (bean.id == event.data && bean.readFlag == AGENT_ORDER_AUTHORIZATION_FLAG_UNREAD) {
                                bean.readFlag = AGENT_ORDER_AUTHORIZATION_FLAG_READ
                                getAdapter(rows)?.notifyDataSetChanged()
                                return
                            }
                        }
                    }
                }
                //处理授权状态
                RX_BUS_AGENT_ORDER_AUTHORIZATION_STATUS -> {
                    if (rows != null) {
                        handleAuthorizationStatus(event)
                    }
                }
            }
        }
    }

    /**
     * 处理授权状态
     * （接收的状态已经修改过状态）
     */
    private fun handleAuthorizationStatus(event: Event<*>) {
        val data = event.data
        if(data is AuthorizationAreaRowBean) {
            when(arguments?.getInt("authorizationCategory")) {
                //全部标签下查找相同id，更改状态
                AUTHORIZATION_AREA_ALL -> {
                    rows.forEachIndexed { index, bean ->
                        if (bean.id == data.id && bean.status == AUTHORIZATION_AREA_UNAUTHORIZATION) {
                            bean.status = AUTHORIZATION_AREA_AUTHORIZATIONED
                            getAdapter(rows)?.notifyItemChanged(index)
                            return
                        }
                    }
                }
                //状态与当前标签状态相同添加到列表
                AUTHORIZATION_AREA_AUTHORIZATIONED -> {
                    rows.add(0, data)
                    getAdapter(rows)?.notifyItemInserted(0)
                }

                //未授权
                AUTHORIZATION_AREA_UNAUTHORIZATION -> {
                    rows.forEachIndexed {index, value->
                        if(value.id == data.id) {
                            rows.removeAt(index)
                            getAdapter(rows)?.notifyDataSetChanged()
                            return
                        }
                    }
                }
            }
        }
    }

    override fun getEmptyImg(): Int = R.drawable.icon_empty

    override fun getEmptyMsg(): String = getString(R.string.no_data)
}