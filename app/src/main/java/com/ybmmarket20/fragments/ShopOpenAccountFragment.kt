package com.ybmmarket20.fragments

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.SimpleItemAnimator
import com.luck.picture.lib.tools.ScreenUtils
import com.ybm.app.common.BaseYBMApp
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.adapter.OpenAnAccountAdapter
import com.ybmmarket20.bean.OpenAccountMapping
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.view.CORNER_TOP
import com.ybmmarket20.view.FreightTipDialog
import com.ybmmarket20.view.ShopQualificationItemView
import com.ybmmarket20.viewmodel.ShopOpenAccountViewModel
import kotlinx.android.synthetic.main.fragment_shop_open_account.view.*

/**
 * Pop店铺-开户
 */
class ShopOpenAccountFragment : LoadingFragment() {

    lateinit var mOpenAccountMapping: OpenAccountMapping
    var mRootView: View? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val rootView = View.inflate(context, R.layout.fragment_shop_open_account, null)
        mRootView = rootView
        val viewModel = ViewModelProvider(this).get(ShopOpenAccountViewModel::class.java)
        viewModel.initData(arguments)
        viewModel.openAccountLiveData.observe(viewLifecycleOwner, Observer {
            dismissProgress()
            if (it.isSuccess) {
                rootView.empty_view.visibility = View.GONE
                mOpenAccountMapping = it.data
                setOpenAccountData(it.data)
            } else {
                rootView.empty_view.visibility = View.VISIBLE
            }
        })
        showProgress()
        viewModel.getShopOpenAccountData()
        return rootView
    }

    /**
     * 设置开户数据
     */
    private fun setOpenAccountData(openAccountMapping: OpenAccountMapping) {
        mRootView?.rv_open_account_data?.layoutManager = WrapLinearLayoutManager(context)
        mRootView?.rv_open_account_data?.isNestedScrollingEnabled = false
        mRootView?.rv_open_account_data?.isEnabled = true
        (mRootView?.rv_open_account_data?.itemAnimator as SimpleItemAnimator).supportsChangeAnimations = false
        val adapter = OpenAnAccountAdapter(mOpenAccountMapping.openAccountData)
        handleHeader(openAccountMapping, adapter)
        adapter.setEnableLoadMore(false)
        mRootView?.rv_open_account_data?.adapter = adapter
    }

    private fun handleHeader(openAccountMapping: OpenAccountMapping, adapter: OpenAnAccountAdapter) {
        if (context == null) return
        if (!openAccountMapping.openAccountModeList.isNullOrEmpty() || !mOpenAccountMapping.openAccountInstruction.isNullOrEmpty()) {
            val shopQualificationItemView = ShopQualificationItemView(requireContext())
            shopQualificationItemView.setTitle("开户方式")
            openAccountMapping.openAccountModeList?.let {
                shopQualificationItemView.setContent(it)
            }
            shopQualificationItemView.setViewMarginTop(ScreenUtils.dip2px(context, 10f))
            if (!mOpenAccountMapping.openAccountInstruction.isNullOrEmpty()) {
                shopQualificationItemView.setItemEntry("商家开户说明") {
                    FreightTipDialog(context as Activity).showNormal(mOpenAccountMapping.openAccountInstruction, "商家开户说明")
                }
            }
            adapter.addHeaderView(shopQualificationItemView)
        }

        if (!openAccountMapping.openAccountData.isNullOrEmpty()) {
            val listHeaderItemView = ShopQualificationItemView(requireContext())
            listHeaderItemView.setTitle("开户所需资料", CORNER_TOP)
            listHeaderItemView.setViewMarginTop(ScreenUtils.dip2px(context, 10f))
            adapter.addHeaderView(listHeaderItemView)
        }
    }

    /**
     * 呼叫客服
     */
    private fun showCustomerService(phone: String) {
        val dialogEx = AlertDialogEx(BaseYBMApp.getApp().currActivity)
        dialogEx.setMessage("呼叫客服：$phone")
            .setCancelButton("取消", null)
            .setConfirmButton("呼叫") { _, _ ->
                val intent = Intent(Intent.ACTION_DIAL, Uri.parse("tel:$phone"))
                BaseYBMApp.getApp().currActivity.startActivity(intent)
            }.show()
    }

}