package com.ybmmarket20.fragments;

import static android.app.Activity.RESULT_OK;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.google.gson.Gson;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.AvailableVoucherBeanV2;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.VoucherListBean;
import com.ybmmarket20.common.BaseFragment;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarketkotlin.adapter.VoucherAvailableAdapter2;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.Bind;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * 可用优惠券
 */
public class VoucherAvailableFragment extends BaseFragment {

    @Bind(R.id.tv_voucher_title)
    TextView mTvVoucherTitle;
    @Bind(R.id.tv_select_Optimal)
    TextView mTvSelectOptimal;
    @Bind(R.id.available_lv)
    CommonRecyclerView mAvailableLv;
    @Bind(R.id.tv_not)
    TextView mTvNot;
    @Bind(R.id.tv_btn_confirm)
    TextView mTvBtnConfirm;
    @Bind(R.id.ll_not)
    LinearLayout mLlNot;

    private VoucherAvailableAdapter2 adapter;
    private List<VoucherListBean> rows = new ArrayList<>();
    private int mTab;
    private String mEmptyStr, mSkus, mPrice, purchaseNo;//skus(商品id:小计,商品id:小计), mPrice 药帮忙自营订单总额（去除优惠运费之后的价格，叠加券超出校验）,purchaseNo代下单参数
    private boolean isFromAgentOrder;//是否代下单过来的
    public static final int VOUCHER_AVAILABLE = 0;//可用优惠券
    public static final int WISH_DISABLED = 1;//不可用优惠券
    private static final int VOUCHER_TEXT = 10;
    private static final int VOUCHER_IDS = 20;
    private static final int VOUCHER_RECOMMENDED = 30;
    private String mVoucherIds = "";//选中优惠券列表
    private String needOptimal = "0";  // 是否需要最优 1需要0不需要
    private int num;//已选择优惠券数量

    @Override
    protected void initData(String content) {
        mEmptyStr = "您还没有优惠券哦!";
        mTab = getArguments().getInt("tab");
        mSkus = getArguments().getString("skus");
        mVoucherIds = getArguments().getString("selectVoucherIds","");
        mPrice = getArguments().getString("price", "0");
        isFromAgentOrder = getArguments().getBoolean("isFromAgentOrder", false);
        purchaseNo = getArguments().getString("purchaseNo", "");
        switch (mTab) {
            case VOUCHER_AVAILABLE:
            default:
                mTvVoucherTitle.setVisibility(View.VISIBLE);
                mTvSelectOptimal.setVisibility(View.VISIBLE);
                mLlNot.setVisibility(View.VISIBLE);
                break;
            case WISH_DISABLED:
                mTvVoucherTitle.setVisibility(View.GONE);
                mTvSelectOptimal.setVisibility(View.GONE);
                mLlNot.setVisibility(View.GONE);
                break;
        }
        init();
    }

    @Override
    protected void initTitle() {

    }

    @Override
    protected RequestParams getParams() {
        return null;
    }

    @Override
    protected String getUrl() {
        return null;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_voucher_available;
    }

    private void init() {
        adapter = new VoucherAvailableAdapter2(R.layout.coupon_list_item_v5, rows, mTab, mHandler);
        adapter.setOrderTotalAmount(Double.parseDouble(mPrice));
        adapter.setEnableLoadMore(false);
        adapter.setEmptyView(getContext(), R.layout.layout_empty_view, R.drawable.icon_empty, mEmptyStr);

        mAvailableLv.setAdapter(adapter);
        mAvailableLv.setRefreshEnable(false);
        getVoucherAvailable();
    }

    /*
     * 优惠券信息
     * @param page 分页加载页码
     * productIdAndSingleTotalAmount => 上传服务器优惠券id和金额
     * orderAmount => 订单金额
     * isUse => 可用优惠券和不可用优惠券tab切换状态码
     * url => "voucher/findAvailableVoucherInfo"
     */
    public void getVoucherAvailable() {
        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", "" + merchantid);
        params.put("productIdAndSingleTotalAmount", "" + mSkus);
        // params.put("orderAmount", "" + mPrice);
        params.put("selectVoucherIds", "" + mVoucherIds);
        params.put("needOptimal", needOptimal);  // 是否需要最优 1需要0不需要
        if (isFromAgentOrder) {
            params.put("purchaseNo", purchaseNo);
        }
        switch (mTab) {
            case VOUCHER_AVAILABLE:
            default:
                params.put("isUse", "0");  // 0查询可用优惠券列表1查询不可用优惠券列表
                break;
            case WISH_DISABLED:
                params.put("isUse", "1");
                break;
        }
        showProgress();
        HttpManager.getInstance().post(AppNetConfig.VOUCHER_FINDVOUCHERINFO_V2, params, new BaseResponse<AvailableVoucherBeanV2>() {

                    @Override
                    public void onSuccess(String content, BaseBean<AvailableVoucherBeanV2> data, AvailableVoucherBeanV2 voucherBean) {
                        if (data != null && data.isSuccess()) {
                            dismissProgress();
                            if (voucherBean == null) {
                                adapter.setNewData(null);
                                return;
                            }

                            rows.clear();
                            if (mTab == VOUCHER_AVAILABLE) {
                                // 本地拼接已选择优惠券id
                                if (voucherBean.voucherList != null) {
                                    Map<String, String> map = new HashMap<>();
                                    int listsize = voucherBean.voucherList.size();
                                    for (int i = 0; i < listsize; i++) {
                                        VoucherListBean bean=voucherBean.voucherList.get(i);
                                        if (bean.isSelected) {
                                            map.put(bean.id + "", bean.shopCode + ":" + bean.shopPatternCode);
                                        }
                                    }
                                    mVoucherIds = new Gson().toJson(map);
                                    num=map.size();
                                }
                                // 设置已使用优惠券头部提示语
                                String str = "已使用" + num + "张优惠券，已优惠" + voucherBean.totalDiscount + "元";
                                mTvVoucherTitle.setText(str);
                                //券不可选择时的toast提示文案
                                adapter.setMVoucherText(voucherBean.voucherText);
                                double totalDiscount = 0.0;
                                try {
                                    totalDiscount = Double.parseDouble(voucherBean.totalDiscount);
                                } catch (NumberFormatException e) {
                                    e.printStackTrace();
                                }
                                adapter.setTotalDiscount(totalDiscount);
                            }
                            rows.addAll(voucherBean.voucherList);
                            adapter.notifyDataSetChanged();
                        }
                    }

                    @Override
                    public void onFailure(NetError error) {
                        dismissProgress();
                    }
                }
        );
    }

    @OnClick({R.id.tv_not, R.id.tv_btn_confirm, R.id.tv_select_Optimal})
    public void clickTab(View view) {
        switch (view.getId()) {
            //不使用优惠
            case R.id.tv_not:
                setNotVoucher();
                break;
            //确定选择优惠券
            case R.id.tv_btn_confirm:
                setIntentPay();
                break;
            case R.id.tv_select_Optimal:
                needOptimal = "1";
                getVoucherAvailable();
                break;

        }
    }


    /*
     * 不使用优惠券
     * */
    private void setNotVoucher() {
        Intent intent = new Intent();
        Bundle bundle = new Bundle();
        VoucherListBean coupon = new VoucherListBean();
        coupon.setId(-1);
        coupon.setVoucherTemplateId(-1);
        bundle.putSerializable("availableIndex", coupon);
        bundle.putInt("voucherId", -1);
        bundle.putString("voucherIds", "");
        // bundle.putString("shopCode", shopCode);
        intent.putExtras(bundle);
        getNotNullActivity().setResult(RESULT_OK, intent);
        getNotNullActivity().finish();
    }

    /*
     * 成功返回success-返回优惠券id
     * voucherIds的值返回格式
     * 以json字符串格式传递多个优惠券信息，
     * 结构体：key ->voucherId，value ->shopCode1:shopPatternCode1
     * 如：{"voucherId1":"shopCode1:shopPatternCode","voucherId2":"shopCode2:shopPatternCode"}
     * */
    public void setIntentPay() {
        if (null != rows && !TextUtils.isEmpty(mVoucherIds)) {
            Intent intent = new Intent();
            Bundle bundle = new Bundle();
            bundle.putString("voucherIds", mVoucherIds);
            intent.putExtras(bundle);
            getNotNullActivity().setResult(RESULT_OK, intent);
            getNotNullActivity().finish();
        } else {
            setNotVoucher();
        }
    }

    @SuppressLint("HandlerLeak")
    Handler mHandler = new Handler() {

        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            if (msg.what == VOUCHER_IDS) {//返回选择的优惠券
                if (mTvBtnConfirm == null) {
                    return;
                }
                needOptimal = "0";
                mVoucherIds = (String) msg.obj;
                getVoucherAvailable();
            } else if (msg.what == VOUCHER_RECOMMENDED) {
                needOptimal = "1";
                getVoucherAvailable();
            }
        }
    };

    public static VoucherAvailableFragment getInstance(int tab, String skus, String selectVoucherIds, String price, boolean isFromAgentOrder, String purchaseNo) {
        VoucherAvailableFragment fragment = new VoucherAvailableFragment();
        fragment.setArguments(setArguments(tab, skus, selectVoucherIds, price, isFromAgentOrder, purchaseNo));
        return fragment;
    }

    public static Bundle setArguments(int tab, String skus, String selectVoucherIds, String price, boolean isFromAgentOrder, String purchaseNo) {
        Bundle bundle = new Bundle();
        bundle.putInt("tab", tab);
        bundle.putString("skus", skus);
        bundle.putString("selectVoucherIds", selectVoucherIds);
        bundle.putString("price", price);
        bundle.putBoolean("isFromAgentOrder", isFromAgentOrder);
        bundle.putString("purchaseNo", purchaseNo);
        return bundle;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        ButterKnife.unbind(this);
    }
}
