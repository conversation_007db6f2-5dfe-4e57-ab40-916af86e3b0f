package com.ybmmarket20.fragments;

import android.graphics.Rect;
import android.os.Bundle;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.View;

import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.CouponMemberAdapter;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.VoucherListBean;
import com.ybmmarket20.bean.VoucherPage2Bean;
import com.ybmmarket20.common.BaseFragment;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;

public class BoundsPoolsRewardFragment extends BaseFragment implements CouponMemberAdapter.OnAvailableClickListener {

    @Bind(R.id.lv)
    CommonRecyclerView lv;

    private CouponMemberAdapter adapter;
    private List<VoucherListBean> mVoucherList = new ArrayList<>();
    private int mCouponTab = 2;
    private int mPageSize = 10;
    private int mPage = 0;

    @Override
    protected void initData(String content) {

        adapter = new CouponMemberAdapter(R.layout.coupon_list_item_v2, true, mVoucherList);
        adapter.openLoadMore(10, true);
        adapter.setOnItemClickListener(this);
        lv.setEnabled(false);
        lv.setRefreshEnable(true);
        lv.setListener(new CommonRecyclerView.Listener() {

            @Override
            public void onRefresh() {
                getRefreshOrLoadMoreResponse(mPage = 0);
            }

            @Override
            public void onLoadMore() {
                getRefreshOrLoadMoreResponse(mPage);
            }
        });
        lv.setAdapter(adapter);

        lv.setEmptyView(R.layout.layout_empty_view, R.drawable.icon_empty_coupon, "暂无奖励");
        lv.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
                outRect.bottom = ConvertUtils.dp2px(8);
            }
        });
    }

    /**
     * 获取商户优惠券信息
     */
    public void getRefreshOrLoadMoreResponse(final int page) {
        final int requestTab = mCouponTab;
        String merchantId = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantId);
        params.put("pageSize", String.valueOf(mPageSize));
        params.put("pageNum", String.valueOf(String.valueOf(page)));
        HttpManager.getInstance().post(AppNetConfig.SELECT_INVITE_USER_VOUCHER, params, new BaseResponse<VoucherPage2Bean>() {

            @Override
            public void onSuccess(String content, BaseBean<VoucherPage2Bean> obj, VoucherPage2Bean data) {
                dismissProgress();
                //确保是最后一次的请求
                if (requestTab != mCouponTab) {
                    return;
                }
                lv.setRefreshing(false);
                if (obj != null) {
                    if (obj.isSuccess()) {
                        if (data != null) {
                            if (data.getRows() != null) {

                                if (data.getRows().size() > 0) {
                                    if (page <= 0) {
                                        BoundsPoolsRewardFragment.this.mPage = 1;
                                    } else {
                                        BoundsPoolsRewardFragment.this.mPage++;
                                    }
                                }

                                if (page <= 0) {
                                    if (mVoucherList == null) {
                                        mVoucherList = new ArrayList<>();
                                    }
                                    mVoucherList.clear();
                                    if (mVoucherList.size() <= 0 && data.getRows() != null) {
                                        mVoucherList.addAll(data.getRows());
                                    } else {
                                        if (data.getRows() == null || data.getRows().isEmpty()) {

                                        } else {
                                            for (VoucherListBean bean : data.getRows()) {
                                                if (mVoucherList.contains(bean)) {
                                                    mVoucherList.remove(bean);
                                                }
                                            }
                                            mVoucherList.addAll(0, data.getRows());
                                        }
                                    }
                                    adapter.setNewData(mVoucherList);
                                    adapter.notifyDataChangedAfterLoadMore(mVoucherList.size() >= mPageSize);
                                } else {
                                    if (data.getRows() == null || data.getRows().size() <= 0) {
                                        adapter.notifyDataChangedAfterLoadMore(false);
                                    } else {

                                        for (VoucherListBean bean : data.getRows()) {
                                            if (mVoucherList.contains(bean)) {
                                                mVoucherList.remove(bean);
                                            }
                                        }
                                        mVoucherList.addAll(data.getRows());
                                        adapter.setNewData(mVoucherList);
                                        adapter.notifyDataChangedAfterLoadMore(data.getRows().size() >= mPageSize);
                                    }
                                }
                            }
                        }
                    } else {
                        adapter.setNewData(mVoucherList);
                    }
                } else {
                    adapter.notifyDataChangedAfterLoadMore(false);
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                if (requestTab != mCouponTab) {
                    return;
                }
                ToastUtils.showShort(error.message);
                lv.setRefreshing(false);
                adapter.notifyDataChangedAfterLoadMore(false);
            }
        });
    }


    @Override
    protected void initTitle() {

    }

    @Override
    protected RequestParams getParams() {
        return null;
    }

    @Override
    protected String getUrl() {
        return null;
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_bonus_pools_reward;

    }

    public static BoundsPoolsRewardFragment getInstance(int tab) {
        BoundsPoolsRewardFragment fragment = new BoundsPoolsRewardFragment();
        fragment.setArguments(setArguments(tab));
        return fragment;
    }

    public static Bundle setArguments(int tab) {
        Bundle bundle = new Bundle();
        bundle.putInt("tab", tab);
        return bundle;
    }

    @Override
    public void onAvailableClick(VoucherListBean coupon) {
        if (coupon == null) {
            return;
        }
        String router;
        if (TextUtils.isEmpty(coupon.appUrl)) {
            router = "ybmpage://couponavailableactivity/" + coupon.getVoucherTemplateId();
        } else {
            router = coupon.appUrl;
        }
        if (router.startsWith("ybmpage")) {
            RoutersUtils.open(router);
        }
    }

}
