package com.ybmmarket20.fragments

import android.view.View
import com.ybmmarket20.bean.homesteady.Banner
import com.ybmmarket20.bean.homesteady.BaseBannerBean
import com.ybmmarket20.bean.homesteady.FastEntryItem
import com.ybmmarket20.utils.analysis.*

/**
 * 处理首页V2埋点回调
 */
abstract class HomeSteadyAnalysisFragment: HomeSteadyAlertFragment() {

    /**
     * banner点击埋点回调
     */
    fun bannerAnalysisCallback(baseBannerInfo: BaseBannerBean, position: Int) {
        if (baseBannerInfo is Banner) {
            homeSteadyAnalysisBannerClick(AnalysisConst.HomeSteady.ACTION_HOME_BANNER_CLICK_V1, baseBannerInfo.getActionLink(), "${position + 1}", (baseBannerInfo as Banner).image)
        }
    }

    /**
     * 快捷入口点击埋点回调
     */
    fun fastEntryAnalysisCallback(action: String, position: Int, item: FastEntryItem, view: View, size: Int) {
        homeSteadyAnalysisFastEntryClick(AnalysisConst.HomeSteady.ACTION_HOME_FASTENTRY_CLICK_V1, action,"${position + 1}", item.entry, item.icon) {
            it.putExtraString("title", item.entry)
            it.putExtraString("located", XyyIoUtil.PAGE_HOMEPAGE)
        }
    }

    /**
     * 胶囊位入口点击埋点回调
     */
    fun streamerAnalysisCallback(action: String, text: String) {
        homeSteadyAnalysisStreamerClick(AnalysisConst.HomeSteady.ACTION_HOME_STREAMER_CLICK_V1, action, text)
    }

    /**
     * 导购入口点击埋点
     */
    fun shoppingGuideAnalysisCallback(action: String, offset: Int, text: String, sku_id: String) {
        homeSteadyAnalysisShoppingGuide(AnalysisConst.HomeSteady.ACTION_HOME_SHOPPING_GUIDE_CLICK_V1, action, "${offset + 1}", text, sku_id)
    }

}