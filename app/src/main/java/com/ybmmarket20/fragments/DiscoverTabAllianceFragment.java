

package com.ybmmarket20.fragments;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import com.google.android.material.appbar.AppBarLayout;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.util.TypedValue;
import android.view.View;
import android.widget.ImageView;
import android.widget.ScrollView;

import com.apkfuns.logutils.LogUtils;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybm.app.view.refresh.RecyclerRefreshLayout;
import com.ybmmarket20.R;
import com.ybmmarket20.home.MainActivity;
import com.ybmmarket20.adapter.AllianceGoodsAdapter;
import com.ybmmarket20.adapter.AllianceLeftLevel2Adapter;
import com.ybmmarket20.adapter.FlowTagAdapter;
import com.ybmmarket20.bean.AllianceGoodsBean;
import com.ybmmarket20.bean.AllianceGoodsBeanWrapper;
import com.ybmmarket20.bean.AllianceLeve2Bean;
import com.ybmmarket20.bean.AllianceTagBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.LicenseStatusFragment;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.statusview.StatusViewLayout;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.MTextView;
import com.ybmmarket20.view.TagGroup;
import com.ybmmarket20.view.flowtag.FlowTagLayout;

import java.util.ArrayList;
import java.util.List;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 用药指导
 */
public class DiscoverTabAllianceFragment extends LicenseStatusFragment implements FlowTagAdapter.OnTagItemCallback, RecyclerRefreshLayout.OnRefreshListener, CommonRecyclerView.Listener {

    @Bind(R.id.rv_right_level3)
    CommonRecyclerView rv_Leve3;
    @Bind(R.id.rv_left_level2)
    CommonRecyclerView rv_left_level2;
    @Bind(R.id.tagLayout)
    FlowTagLayout tagLayout;
    @Bind(R.id.iv_is_extend)
    ImageView iv_is_extend;
    @Bind(R.id.iv_is_extend_shadow)
    ImageView iv_is_extend_shadow;
    @Bind(R.id.status_view_layout_1)
    StatusViewLayout status_view_layout_1;
    @Bind(R.id.status_view_layout_2)
    StatusViewLayout status_view_layout_2;
    @Bind(R.id.status_view_layout_3)
    StatusViewLayout status_view_layout_3;
    @Bind(R.id.scrollview_tag)
    ScrollView scrollview_tag;
    @Bind(R.id.view_masking)
    View view_masking;
    @Bind(R.id.tv_announcement_info)
    MTextView tv_announcement_info;
    @Bind(R.id.appbar)
    AppBarLayout appbar;

    private final int TAG_1_LINE_HEIGHT = UiUtils.dp2px(39);
    private boolean isExtendedStatus = false;//是否是扩展状态
    private FlowTagAdapter tagLevel1Adapter;
    private AllianceGoodsAdapter goodsAdapter;
    private AllianceLeftLevel2Adapter leftLevel2Adapter;

    private final int START_PAGE_INDEX = 1;
    private final int PAGE_SIZE = 10;
    private int pageNum = START_PAGE_INDEX;
    private String level2ResultId;//二级列表返回结果
    private int loadCount = 0;//为了防止刚进入界面自动回调onRefresh
    private String level1ResultId;//一级及列表返回结果id

    public static DiscoverTabAllianceFragment getInstance() {
        return new DiscoverTabAllianceFragment();
    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_discover_alliance;
    }

    @Override
    protected void initTitle() {

    }


    private void registerRec() {
        IntentFilter intentFilter = new IntentFilter(IntentCanst.ACTION_REFRESH_RECOMMEND);
        if (broadcastReceiver != null)
            LocalBroadcastManager.getInstance(getNotNullActivity()).registerReceiver(broadcastReceiver, intentFilter);
    }

    @Override
    protected void initData(String content) {
        registerRec();

        status_view_layout_1.showContent();
        status_view_layout_1.setOnRetryListener(new RetrLoadLeve1());
        status_view_layout_2.setOnRetryListener(new RetrLoadLeve2());
        status_view_layout_3.setOnRetryListener(new RetrLoadLeve3());

        tv_announcement_info.setMText("以下信息仅供参考,具体用药方案请在医师指导下购买和使用");
        tv_announcement_info.setTextColor(getResources().getColor(R.color.alliance_announcement_text_color));
        tv_announcement_info.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);

        initTagLayout();
        initRecyclerView();

        ((MainActivity) getActivity()).setBottomNavigationMaskingClickListener(v -> tagToMerge());

        ((FindFragment) getParentFragment()).setOnMaskingClickListener(v -> tagToMerge());


        requestGoodsCategoryLevel1();


        appbar.addOnOffsetChangedListener((appBarLayout, verticalOffset) -> {
            rv_Leve3.setEnabled(verticalOffset == 0);
        });

        appbarOpt();

    }

    //处理appbar隐藏于显示有时候不灵的问题
    private void appbarOpt() {
        appbar.setExpanded(true);
        rv_Leve3.setOnScrollListener(new CommonRecyclerView.OnScrollListener() {
            @Override
            public void onScrollChanged(int i, int i1) {
            }

            @Override
            public void onScrollRollingDistance(int i, int i1) {
                if (Math.abs(i1) < 20) {
                    return;
                }
                if (i1 < 0) {
                    appbar.setExpanded(true);
                } else {
                    appbar.setExpanded(false);
                }
            }

            @Override
            public void onScrollState(int i) {
            }
        });
    }

    private BroadcastReceiver broadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            //当切换账号时  刷新
            if (SpUtil.isKa()) return;
            requestGoodsCategoryLevel1();
        }
    };


    private void initRecyclerView() {
        rv_left_level2.setRefreshEnable(false);
        leftLevel2Adapter = new AllianceLeftLevel2Adapter(null);
        rv_left_level2.setLayoutManager(new WrapLinearLayoutManager(getContext()));
        rv_left_level2.setAdapter(leftLevel2Adapter);
        leftLevel2Adapter.setOnItemClickCallback(new Leve2ItemClickCallback());

        goodsAdapter = new AllianceGoodsAdapter(null);
        rv_Leve3.setLayoutManager(new WrapLinearLayoutManager(getActivity()));
        rv_Leve3.setListener(this);
        rv_Leve3.setAdapter(goodsAdapter);
        goodsAdapter.openLoadMore(PAGE_SIZE, true);
    }

    private void initTagLayout() {
        tagLayout.setTagShowMode(FlowTagLayout.FLOW_TAG_SHOW_FREE);
        scrollview_tag.getLayoutParams().height = TAG_1_LINE_HEIGHT;

        tagLevel1Adapter = new FlowTagAdapter(null, this);
        tagLayout.setAdapter(tagLevel1Adapter);

    }


    /**
     * 获取分类列表
     */
    private void requestGoodsCategoryLevel1() {
        RequestParams params = new RequestParams();
        params.put("merchantId", SpUtil.getMerchantid());
        //showProgress();
        status_view_layout_1.showLoading();
        HttpManager.getInstance().post(AppNetConfig.FIND_ALLIANCE_CATEGORY, params, new BaseResponse<List<AllianceTagBean>>() {

            @Override
            public void onSuccess(String content, BaseBean<List<AllianceTagBean>> obj, List<AllianceTagBean> level1listBean) {
                LogUtils.e("分类 content = " + content);
                dismissProgress();
                if (!obj.isSuccess()) {
                    status_view_layout_1.showNetWorkException();
                    return;
                }
                if (level1listBean == null || level1listBean.size() == 0) {
                    status_view_layout_1.showEmpty("暂无联合用药推荐，看看其他的药品吧~");
                } else {
                    status_view_layout_1.showContent();
                    showTagLayoutData(level1listBean);
                    pageNum = START_PAGE_INDEX;
                    level1ResultId = level1listBean.get(0).getId();
                    requestLeftTitleListLevel2();
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                status_view_layout_1.showNetWorkException();
                status_view_layout_1.setVisibility(View.VISIBLE);
            }
        });
    }


    /**
     * 获取左侧二级列表数据
     */
    private void requestLeftTitleListLevel2() {
        RequestParams params = new RequestParams();
        params.put("drugTypeId", level1ResultId);//联合用药分类id
        params.put("merchantId", SpUtil.getMerchantid());
        //showProgress();
        status_view_layout_2.showLoading();
        HttpManager.getInstance().post(AppNetConfig.FIND_ALLIANCE_CATEGORY_LEVEL2, params, new BaseResponse<List<AllianceLeve2Bean>>() {
            @Override
            public void onSuccess(String content, BaseBean<List<AllianceLeve2Bean>> obj, List<AllianceLeve2Bean> leve2BeanList) {
                LogUtils.e("二级分类 content = " + content);
                dismissProgress();
                if (!obj.isSuccess()) {
                    status_view_layout_2.showNetWorkException();
                    return;
                }
                status_view_layout_2.showContent();
                if (leve2BeanList == null || leve2BeanList.size() == 0) {
                    status_view_layout_2.showEmpty();
                } else {
                    leftLevel2Adapter.setNewData(leve2BeanList);
                    leftLevel2Adapter.setSelectedPosition(0);
                    rv_left_level2.getRecyclerView().scrollToPosition(0);
                    appbar.setExpanded(true);//展开appbar
                    level2ResultId = leve2BeanList.get(0).getDrugId();
                    requestGoodsData(START_PAGE_INDEX);
                }

            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                status_view_layout_2.setVisibility(View.VISIBLE);
                status_view_layout_2.showNetWorkException();
            }
        });
    }

    private void showTagLayoutData(List<AllianceTagBean> listBean) {
        tagLevel1Adapter.setNewData(listBean);
        tagLevel1Adapter.getItem(0).setChecked(true);
        tagLevel1Adapter.notifyDataSetChanged();
        tagLayout.postDelayed(() -> {
            int lineCount = tagLayout.getLineCount();
            LogUtils.e("lineCount = " + lineCount);
            if (lineCount == 1) {
                //只有一行
                iv_is_extend.setVisibility(View.INVISIBLE);
                iv_is_extend_shadow.setVisibility(View.INVISIBLE);
            }
        }, 500);

    }


    /**
     * 获取商品列表
     *
     * @param page
     */
    private void requestGoodsData(int page) {
        RequestParams params = new RequestParams();
        params.put("merchantId", SpUtil.getMerchantid());
        params.put("limit", String.valueOf(PAGE_SIZE));
        params.put("offset", page + "");
        params.put("drugId", level2ResultId);
        //showProgress();
        status_view_layout_3.showLoading();
        HttpManager.getInstance().post(AppNetConfig.FIND_ALLIANCE_GOODS_LIST, params, new BaseResponse<AllianceGoodsBeanWrapper>() {
            @Override
            public void onSuccess(String content, BaseBean<AllianceGoodsBeanWrapper> obj, AllianceGoodsBeanWrapper bean) {
                LogUtils.e("药品信息 content = " + content);
                dismissProgress();
                rv_Leve3.setRefreshing(false);
                if (!obj.isSuccess()) {
                    status_view_layout_3.showNetWorkException();
                    return;
                }
                pageNum = page;
                updateLicenseStatus(bean.licenseStatus, getCurrentLicenseStatusListener());
                goodsAdapter.setFlowData(mFlowData);
                if (bean == null || bean.rows == null) {
                    status_view_layout_3.showEmpty("暂无联合用药推荐，看看其他的药品吧~");
                } else {
                    status_view_layout_3.setVisibility(View.VISIBLE);
                    status_view_layout_3.showContent();
                    List<AllianceGoodsBean> list = new ArrayList<>();
                    list.add(bean.rows);
                    goodsAdapter.setNewData(list);
                    rv_Leve3.getRecyclerView().scrollToPosition(0);
                    appbar.setExpanded(true);
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                status_view_layout_3.showNetWorkException();
            }
        });
    }


    @Override
    protected RequestParams getParams() {
        return null;
    }

    @Override
    protected String getUrl() {
        return null;
    }

    @OnClick({R.id.iv_is_extend, R.id.view_masking})
    public void OnClick(View view) {
        switch (view.getId()) {
            case R.id.iv_is_extend:
                if (isExtendedStatus) {//让其合并
                    tagToMerge();
                } else {//让其扩展
                    tagToExtend();
                }
                break;
            case R.id.view_masking:
                tagToMerge();
                break;
        }
    }

    private void tagToMerge() {
        if (isExtendedStatus) {
            isExtendedStatus = false;
            iv_is_extend.setSelected(false);
            scrollview_tag.setSmoothScrollingEnabled(false);
            scrollview_tag.fullScroll(ScrollView.FOCUS_UP);//滑到顶部
            scrollview_tag.getLayoutParams().height = TAG_1_LINE_HEIGHT;
            scrollview_tag.requestLayout();
            view_masking.setVisibility(View.GONE);
            ((MainActivity) getActivity()).setBottomNavigationMasking(View.GONE);
            ((FindFragment) getParentFragment()).setTabMasking(View.GONE);
        }

    }

    private void tagToExtend() {
        if (isExtendedStatus) return;
        isExtendedStatus = true;
        iv_is_extend.setSelected(true);
        view_masking.setVisibility(View.VISIBLE);
        //MainActivity的底部导航栏
        ((MainActivity) getActivity()).setBottomNavigationMasking(View.VISIBLE);
        ((FindFragment) getParentFragment()).setTabMasking(View.VISIBLE);
        scrollview_tag.setVisibility(View.INVISIBLE);
        scrollview_tag.getLayoutParams().height = TagGroup.LayoutParams.WRAP_CONTENT;
        scrollview_tag.requestLayout();
        scrollview_tag.post(() -> {
            int tagHeight = scrollview_tag.getHeight();
            int p = UiUtils.getScreenHeight(getContext()) / 3;
            if (tagHeight > p) {//如果大于屏幕三分之一
                scrollview_tag.getLayoutParams().height = p;
                scrollview_tag.requestLayout();
                scrollview_tag.setVisibility(View.VISIBLE);
            } else {
                scrollview_tag.setVisibility(View.VISIBLE);
            }
        });
    }


    @Override
    public void onHiddenChanged(boolean hidden) {
        if (hidden) {
            this.onPause();
        }
    }

    /**
     * 一级列表被点击
     *
     * @param position
     */
    @Override
    public void onTagCallback(int position) {
        changeTagCheckPosition(position);
        leftLevel2Adapter.setNewData(null);
        goodsAdapter.setNewData(null);
        pageNum = START_PAGE_INDEX;

        level2ResultId = "";
        level1ResultId = tagLevel1Adapter.getData().get(position).getId();

        requestLeftTitleListLevel2();
        tagToMerge();
    }

    @Override
    public boolean onLicenseStatusEnable() {
        return true;
    }

    @Override
    public void handleLicenseStatusChange(int status) {
        requestGoodsData(START_PAGE_INDEX);
    }

    /**
     * 二级列表被点击
     */
    private class Leve2ItemClickCallback implements AllianceLeftLevel2Adapter.OnItemClickCallback {

        @Override
        public void onItemClick(int position) {
            leftLevel2Adapter.setSelectedPosition(position);
            level2ResultId = ((AllianceLeve2Bean) leftLevel2Adapter.getData().get(position)).getDrugId();
            goodsAdapter.setNewData(null);
            onRefresh();
        }
    }

    private void changeTagCheckPosition(int position) {
        for (int i = 0; i < tagLevel1Adapter.getItemCount(); i++) {
            if (position != i) {
                tagLevel1Adapter.getItem(i).setChecked(false);
            } else {
                tagLevel1Adapter.getItem(i).setChecked(true);
            }
        }
        tagLevel1Adapter.notifyDataSetChanged();
    }

    @Override
    public void onRefresh() {
        loadCount++;
        if (loadCount == 1) {
            return;
        }

        goodsAdapter.setNewData(null);
        requestGoodsData(START_PAGE_INDEX);

    }


    @Override
    public void onLoadMore() {
        requestGoodsData(pageNum + 1);
    }

    private class RetrLoadLeve1 implements StatusViewLayout.OnRetryCallback {

        @Override
        public void onRetryLoadClick(View view) {
            requestGoodsCategoryLevel1();
        }
    }

    private class RetrLoadLeve2 implements StatusViewLayout.OnRetryCallback {

        @Override
        public void onRetryLoadClick(View view) {
            requestLeftTitleListLevel2();
        }
    }

    private class RetrLoadLeve3 implements StatusViewLayout.OnRetryCallback {

        @Override
        public void onRetryLoadClick(View view) {
            requestGoodsData(START_PAGE_INDEX);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (broadcastReceiver != null)
            LocalBroadcastManager.getInstance(getContext()).unregisterReceiver(broadcastReceiver);
    }
}

