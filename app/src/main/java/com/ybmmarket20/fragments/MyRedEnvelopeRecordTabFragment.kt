package com.ybmmarket20.fragments

import androidx.fragment.app.Fragment

/**
 * 收支记录
 */
class MyRedEnvelopeRecordTabFragment: MyRedEnvelopeClassFragment() {

    var mFragments: Array<Fragment>? = null

    override fun getTitles(): Array<String> = arrayOf("全部记录", "收入记录", "支出记录")

    override fun getFragments(): Array<Fragment> {
        if (mFragments == null) {
            mFragments = arrayOf(
                    MyRedEnvelopeRecordSubTabFragment.getInstance(0),
                    MyRedEnvelopeRecordSubTabFragment.getInstance(1),
                    MyRedEnvelopeRecordSubTabFragment.getInstance(2)
            )
        }
        return mFragments!!
    }
}