package com.ybmmarket20.fragments

import com.ybmmarket20.common.LazyFragment
import com.ybmmarket20.common.RequestParams

open class LoadingFragment: LazyFragment() {
    override fun initData(content: String?) {
    }

    override fun initTitle() {
    }

    override fun getParams(): RequestParams = RequestParams()

    override fun getUrl(): String = ""

    override fun getLayoutId(): Int = -1
    override fun loadData() {

    }
}