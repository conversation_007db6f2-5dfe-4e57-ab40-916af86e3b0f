package com.ybmmarket20.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybmmarket20.R
import com.ybmmarket20.bean.IRedEnvelopeBean
import com.ybmmarket20.common.LazyFragment
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.viewmodel.RedEnvelopeViewModel

/**
 * 红包子tab基类
 */
abstract class BaseRedEnvelopeSubTabFragment: LazyFragment() {

    val mList: ArrayList<IRedEnvelopeBean> = java.util.ArrayList()
    var pageNum = 1
    val mViewModel: RedEnvelopeViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val root = View.inflate(context, R.layout.fragment_red_envelope_sub_tab, null)
        val rvRedEnvelope = root.findViewById<RecyclerView>(R.id.rv_red_envelope)
        val llNoData = root.findViewById<TextView>(R.id.tv_no_data)
        rvRedEnvelope.layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
        rvRedEnvelope.adapter = getRedEnvelopeAdapter()
        getRedEnvelopeAdapter().setEnableLoadMore(true)
        mViewModel._redEnvelopeLiveData.observe(viewLifecycleOwner, Observer {
            dismissProgress()
            if (it?.result != null && it.result!!.list!= null && it.result!!.list!!.isNotEmpty()) {
                it.result?.list?.let(mList::addAll)
                getRedEnvelopeAdapter().notifyDataChangedAfterLoadMore(pageNum != it.result!!.pages)
                pageNum = it.result!!.nextPage
            } else {
                if (pageNum == 1) {
                    llNoData.visibility = View.VISIBLE
                }
            }
        })

        mViewModel._redEnvelopeRecordLiveData.observe(viewLifecycleOwner, Observer {
            dismissProgress()
            if (it?.result != null && it.result!!.list!= null && it.result!!.list!!.isNotEmpty()) {
                it.result?.list?.let(mList::addAll)
                getRedEnvelopeAdapter().notifyDataChangedAfterLoadMore(pageNum != it.result!!.pages)
                pageNum = it.result!!.nextPage
            } else {
                if (pageNum == 1) {
                    llNoData.visibility = View.VISIBLE
                }
            }
        })
        init()
        return root
    }

    abstract fun init ()

    abstract fun getRedEnvelopeAdapter(): YBMBaseAdapter<IRedEnvelopeBean>

    fun getRedEnvelopeList(queryStatus: String, pageSize: String = "") {
        if (pageNum == 1) showProgress()
        mViewModel.getRedEnvelopeList(queryStatus, "$pageNum", pageSize)
    }

    fun getRedEnvelopeRecordList(tradeType: String, pageSize: String = "") {
        if (pageNum == 1) showProgress()
        mViewModel.getRedEnvelopeRecordList(tradeType, "$pageNum", pageSize)
    }

    override fun initData(content: String?) {
    }

    override fun initTitle() {
    }

    override fun getParams(): RequestParams {
        return RequestParams()
    }

    override fun getUrl(): String = ""

    override fun getLayoutId(): Int = 0
}
