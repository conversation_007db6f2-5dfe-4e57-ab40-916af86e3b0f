package com.ybmmarket20.fragments

import android.graphics.Rect
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import android.view.View
import com.google.gson.reflect.TypeToken
import com.ybmmarket20.report.coupon.CouponEntryType
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybmmarket20.R
import com.ybmmarket20.activity.COUPON_STATUS_KEY
import com.ybmmarket20.activity.COUPON_STATUS_UNUSED
import com.ybmmarket20.activity.COUPON_STATUS_USED
import com.ybmmarket20.adapter.CouponAdapter
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CouponBean
import com.ybmmarket20.bean.CouponRowBean
import com.ybmmarket20.common.RefreshFragment
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.constant.AppNetConfig
import java.lang.reflect.Type

/**
 * 我的优惠券Fragment
 */

class CouponFragment: RefreshFragment<CouponRowBean, CouponBean<CouponRowBean>>() {

    private var adapter: CouponAdapter? = null

    fun getCouponEntryType(): String = CouponEntryType.COUPON_ENTRY_TYPE_MINE

    override fun getRequestParams(): RequestParams = RequestParams().apply {
        put("state", arguments?.getInt(COUPON_STATUS_KEY).toString())
    }

    override fun getAdapter(rows: MutableList<CouponRowBean>): YBMBaseAdapter<CouponRowBean> {
        if(adapter == null) {
            adapter = CouponAdapter(this, R.layout.item_goods_coupon, rows)
        } else adapter

        return adapter as CouponAdapter
    }

    override fun getUrl(): String = AppNetConfig.VOUCHER_FINDALLVOUCHER_WITH_SHOP

    override fun getType(): Type = object : TypeToken<BaseBean<CouponBean<CouponRowBean>>>() {}.type

    override fun getLayoutId(): Int = R.layout.fragment_coupon

    override fun getTitle(): String = when(arguments?.getInt(COUPON_STATUS_KEY)){
        COUPON_STATUS_UNUSED -> "未使用"
        COUPON_STATUS_USED -> "已使用"
        else -> "已失效"
    }

    override fun getEmptyImg(): Int = R.drawable.icon_empty_coupon

    override fun getEmptyMsg(): String = getString(R.string.no_coupon)

    override fun getStartPage(): Int {
        return 0
    }

    override fun preLoadData() {
        super.preLoadData()
        showProgress()
    }

    /**
     * 分割线
     */
    class CouponItemDecoration : ItemDecoration() {
        private val space = ConvertUtils.dp2px(1f)
        private var row = 1

        fun setRow(row: Int) {
            this.row = row
        }

        override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
            if (row <= 0) {
                outRect.bottom = 0
                outRect.left = 0
                outRect.top = 0
                outRect.right = 0
                return
            }
            if (row == 1) {
                outRect.top = 0
                outRect.left = space
                outRect.right = space
                outRect.bottom = 0
            }
            if (row == 2) {
                outRect.bottom = space * 10
                if (parent.getChildLayoutPosition(view) % 2 == 0) {
                    outRect.left = space * 10
                    outRect.right = space * 5
                } else {
                    outRect.left = space * 5
                    outRect.right = space * 10
                }
                if (parent.getChildLayoutPosition(view) < row) {
                    outRect.top = space * 10
                } else {
                    outRect.top = 0
                }
            }
        }
    }
}