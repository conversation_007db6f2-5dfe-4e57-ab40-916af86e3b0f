package com.ybmmarket20.fragments

import android.graphics.Color
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.View
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.jeremyliao.liveeventbus.LiveEventBus
import com.ybmmarket20.R
import com.ybmmarket20.adapter.GiftSelectAdapter
import com.ybmmarket20.bean.GiftSelectNormalBean
import com.ybmmarket20.bean.GiftSelectStatus
import com.ybmmarket20.common.BaseFragment
import com.ybmmarket20.common.LiveEventBusManager
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.view.GiftSelectBottomDialog
import com.ybmmarket20.viewmodel.GiftSelectBottomDialogVM
import kotlinx.android.synthetic.main.fragment_gift_select.iv_select
import kotlinx.android.synthetic.main.fragment_gift_select.rv_gift_select
import kotlinx.android.synthetic.main.fragment_gift_select.tv_select
import kotlinx.coroutines.launch
import java.util.regex.Matcher
import java.util.regex.Pattern

/**
 * @class   GiftSelectFragment
 * <AUTHOR>
 * @date  2024/9/13
 * @description
 */
class GiftSelectFragment():BaseFragment() {

    //使用一个ViewModel但是不共享
    private val mViewModel: GiftSelectBottomDialogVM by lazy {
        ViewModelProvider(requireActivity())["GiftSelectFragment_${System.currentTimeMillis()}",GiftSelectBottomDialogVM::class.java]
    }
    private val mGiveUpLiveData = MutableLiveData(false)

    private val mAdapter:GiftSelectAdapter by lazy {
        GiftSelectAdapter(mGiveUpLiveData).apply {
            selectGoodsCallBack = { pair,position:Int ->
                val isNeedSelect = pair.first
                val mBean = pair.second
                val skuId:Long = mBean.id?.toLongOrNull() ?: 0L
                val selectNumber = mBean.selectNum?:1
                lifecycleScope.launch {
                    if (isNeedSelect) {
                        mViewModel.selectItemGiftPool(
                                hashMapOf(
                                        "bizSource" to bizSource,
                                        "promoId" to promoId,
                                        "skuId" to skuId,
                                        "merchantId" to SpUtil.getMerchantid(),
                                        "totalSelectedNum" to mCanSelectNumber,
                                        "amount" to selectNumber
                                ),
                                position
                        )
                    } else {
                        mViewModel.cancelItemGiftPool(
                                hashMapOf(
                                        "bizSource" to bizSource,
                                        "promoId" to promoId,
                                        "skuId" to skuId,
                                        "merchantId" to SpUtil.getMerchantid(),
                                        "totalSelectedNum" to mCanSelectNumber,
                                        "amount" to selectNumber
                                ),
                                position
                        )
                    }
                }
            }

            changeAmountCallBack = { giftSelectNormalBean, amount, position ->

                val skuId:Long = giftSelectNormalBean.id?.toLongOrNull() ?: 0L
                lifecycleScope.launch {

                    //跟换数量时默认先选中
                    if (giftSelectNormalBean.selectStatus.value != GiftSelectStatus.SELECTED){
                        mViewModel.selectItemGiftPool(
                                hashMapOf(
                                        "bizSource" to bizSource,
                                        "promoId" to promoId,
                                        "skuId" to skuId,
                                        "merchantId" to SpUtil.getMerchantid(),
                                        "totalSelectedNum" to mCanSelectNumber,
                                        "amount" to amount
                                ),
                                position
                        )
                    }

                    mViewModel.changeGiftPool(
                            hashMapOf(
                                    "bizSource" to bizSource,
                                    "promoId" to promoId,
                                    "skuId" to skuId,
                                    "amount" to amount,
                                    "merchantId" to SpUtil.getMerchantid(),
                                    "totalSelectedNum" to mCanSelectNumber
                            ),
                            position,
                            amount
                    )
                }

            }

        }
    }
    private var mCanSelectNumber = 0
    private var promoId = ""
    private var bizSource = 0
    private var tabTagPosition = -1

    companion object{
        const val CAN_SELECT_NUMBER = "canSelectNumber"
        const val PROMO_ID = "promoId"
        const val BIZ_SOURCE = "bizSource"
        const val TAB_TAG_POSITION = "tabTagPosition"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.apply {
            mCanSelectNumber = getInt(CAN_SELECT_NUMBER, 0)
            promoId = getString(PROMO_ID)?:""
            bizSource = getInt(BIZ_SOURCE,0)
            tabTagPosition = getInt(TAB_TAG_POSITION,-1)
            mAdapter.canSelectNumber = mCanSelectNumber
        }
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setObservable()

        rv_gift_select.let{
            it.layoutManager = LinearLayoutManager(requireActivity())
            it.adapter = mAdapter
        }

        setTvSelect(0)

    }

    private fun setTvSelect(number: Int){
        if (!checkAttachment()) return
        val mNumber = if (mGiveUpLiveData.value == true){
            0
        }else number
        val content = requireActivity().resources.getString(R.string.str_gift_select_number,mCanSelectNumber.toString(),mNumber.toString())
        val spannableString = SpannableString(content)

        // 查找字符串中的所有数字
        val pattern: Pattern = Pattern.compile("\\d+")
        val matcher: Matcher = pattern.matcher(spannableString)

        // 改变数字的颜色
        while (matcher.find()) {
            spannableString.setSpan(ForegroundColorSpan(Color.RED), matcher.start(), matcher.end(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        tv_select.text = spannableString

        when (mNumber) {
            mCanSelectNumber -> {
                LiveEventBus.get(LiveEventBusManager.GiftSelect.TAB_TAG_GIFT_SELECT,Pair::class.java).post(Pair(tabTagPosition,GiftSelectBottomDialog.GIFT_SELECT_TAB_STATUS_SELECTED))
            }
            in 1 until mCanSelectNumber -> {
                LiveEventBus.get(LiveEventBusManager.GiftSelect.TAB_TAG_GIFT_SELECT,Pair::class.java).post(Pair(tabTagPosition,GiftSelectBottomDialog.GIFT_SELECT_TAB_STATUS_SELECTED_PART))
            }
            else -> {
                LiveEventBus.get(LiveEventBusManager.GiftSelect.TAB_TAG_GIFT_SELECT,Pair::class.java).post(Pair(tabTagPosition,GiftSelectBottomDialog.GIFT_SELECT_TAB_STATUS_NO_SELECT))
            }
        }
    }

    private fun setObservable(){

        iv_select?.setOnClickListener {
            mGiveUpLiveData.value?.let { value->
                val needGiveUp = !value
                lifecycleScope.launch {
                    if (needGiveUp){
                        mViewModel.addAutoGiveUpActFlag(hashMapOf(
                                "bizSource" to bizSource,
                                "promoId" to promoId,
                                "merchantId" to SpUtil.getMerchantid(),
                        ))
                    }else{
                        mViewModel.deleteAutoGiveUpActFlag(hashMapOf(
                                "bizSource" to bizSource,
                                "promoId" to promoId,
                                "merchantId" to SpUtil.getMerchantid(),
                        ))
                    }
                }
            }
        }

        mGiveUpLiveData.observe(requireActivity()){
            if (!checkAttachment()) return@observe

            if (it) {  //放弃当前增
                Glide.with(requireActivity()).load(R.drawable.icon_gift_select).into(iv_select)
                rv_gift_select.alpha = 0.5f
                setTvSelect(0)
            }else {
                Glide.with(requireActivity()).load(R.drawable.icon_gift_no_select).into(iv_select)
                rv_gift_select.alpha = 1f
                setTvSelect(mAdapter.getSelectGiftNumber())
            }
        }

        mViewModel.giftData.observe(requireActivity()){
            if (!checkAttachment()) return@observe
            dismissProgress()
            mGiveUpLiveData.value = it.data?.isGiveUpGift
            mAdapter.setNewData(it.data?.productList?.map { bean->
                if (bean.isSellOut()) {
                    bean.selectStatus.value = GiftSelectStatus.CANT_SELECT
                } else {
                    if (bean.isSelected == true) {
                        bean.selectStatus.value = GiftSelectStatus.SELECTED
                    } else {
                        bean.selectStatus.value = GiftSelectStatus.NO_SELECT
                    }
                }

                bean
            } ?: arrayListOf<GiftSelectNormalBean>())
            setTvSelect(mAdapter.getSelectGiftNumber())
        }

        mViewModel.giftGiveUpSelect.observe(requireActivity()){
            val isNeedSelect = it.first
            val result = it.second
            if (result.isSuccess){
                mGiveUpLiveData.value = isNeedSelect
            }
        }

        mViewModel.giftPoolSelect.observe(requireActivity()){
            val result = it.first
            if (!result.isSuccess) return@observe
            val position = it.second
            val mBean = mAdapter.getItem(position) as? GiftSelectNormalBean
            mBean?.let { giftSelectNormalBean ->
                giftSelectNormalBean.selectStatus.value = GiftSelectStatus.SELECTED
                mAdapter.notifyItemChanged(position)
                setTvSelect(mAdapter.getSelectGiftNumber())
            }
        }

        mViewModel.giftPoolNoSelect.observe(requireActivity()){
            val result = it.first
            if (!result.isSuccess) return@observe
            val position = it.second
            val mBean = mAdapter.getItem(position) as? GiftSelectNormalBean
            mBean?.let { giftSelectNormalBean ->
                giftSelectNormalBean.selectStatus.value = GiftSelectStatus.NO_SELECT
                mAdapter.notifyItemChanged(position)
                setTvSelect(mAdapter.getSelectGiftNumber())
            }
        }

        mViewModel.giftChangeAmount.observe(requireActivity()){
            val position = it.first
            val amount = it.second
            val mBean = mAdapter.getItem(position) as? GiftSelectNormalBean
            mBean?.let { giftSelectNormalBean ->
                giftSelectNormalBean.selectNum = amount
                mAdapter.notifyItemChanged(position)
                setTvSelect(mAdapter.getSelectGiftNumber())
            }
        }

        //需要交互的加载框时就放开
//        mViewModel.loadingLiveData.observe(requireActivity()){
//            if (it) showProgress() else dismissProgress()
//        }
    }


    override fun initData(content: String?) {

        showProgress()
        mViewModel.requestGiftData(promoId,bizSource)
    }

    override fun initTitle() {
    }

    override fun getParams(): RequestParams? = null


    override fun getUrl(): String = ""

    override fun getLayoutId(): Int = R.layout.fragment_gift_select

    private fun checkAttachment() = activity != null
}